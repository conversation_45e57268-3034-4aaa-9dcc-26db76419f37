<template>
  <div class="container">
    <div class="top-box">
      <div class="search-box">
        <span>区域：</span>
        <el-input v-model="search.poleName" placeholder="请输入区域" style="width: 300px; margin-right: 24px;"></el-input>
        <span>编号：</span>
        <el-input v-model="search.poleNo" placeholder="请输入编号" style="width: 300px; margin-right: 24px;"></el-input>
        <el-button color="#165DFF" type="primary" icon="Search" @click="searchList">搜索</el-button>
      </div>
      <div class="add-box">
        <el-button color="#165DFF" type="primary" icon="Plus" @click="addlightpole">新增</el-button>
      </div>
    </div>
    <div class="itemBig-box">
      <div class="item-box" v-for="(item,index) in lampList" :key="index">
          <div class="xinxibox-top">
            <div class="xinxibox-top-text">灯杆</div>
            <div class="item-edit">
              <el-icon style="font-size: 22px; color: #333333; margin-right: 20px;" @click="editlightpole(item)"><Edit /></el-icon>
              <el-icon style="font-size: 22px; color: #333333;" @click="deletelightpole(item)"><Delete /></el-icon>
            </div>
          </div>
          <div class="xinxibox-bottom">
            <div class="xinxibox-img">
              <img class="item-box-img" src="@/assets/lightpole.png" alt="">
            </div>
            <div class="xinxibox-text">
              <div class="xinxi-box">
                <div class="xinxi-box-text">名称：</div>
                <div class="xinxi-box-nr">{{ item.poleName }}</div>
              </div>
              <div class="xinxi-box">
                <div class="xinxi-box-text" >编号：</div>
                <div class="xinxi-box-nr">{{ item.poleNo|| '暂无' }}</div>
              </div>
              <div class="xinxi-box" >
                <div class="xinxi-box-text">位置：</div>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.poleAddress"
                  placement="top"
                >
                  <div class="xinxi-box-nr address">{{ item.poleAddress }}</div>
                </el-tooltip>
              </div>
            </div>
          </div>
      </div>
    </div>
    <div class="paging-box">
      <el-pagination
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        background
        layout="prev, pager, next"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
  <el-dialog
    v-model="dialogadd"
    title="新增灯杆"
    width="50%"
    :close-on-click-modal="false"
    :before-close="handleCloseadd">
    <addlogbox v-if="dialogadd" @handleClose="handleCloseadd"></addlogbox>
  </el-dialog>
  <el-dialog
    v-model="dialogEdit"
    title="编辑灯杆"
    width="50%"
    :close-on-click-modal="false"
    :before-close="EditClose">
    <editlogbox v-if="dialogEdit" @handleClose="EditClose" :editdata="editdata"></editlogbox>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
// import { ArrowRightBold } from "@element-plus/icons";
import addlogbox from '@/components/lamp/addlightpole/index.vue'
import editlogbox from '@/components/lamp/editlightpole/index.vue'
// import Rightpagebox from "@/components/publiczj/rightpagebox.vue"
import { useRouter } from 'vue-router';
import { getlampList, add, remove } from "@/api/lamp/index.js"
import { ElMessage, ElMessageBox } from 'element-plus'
const router = useRouter();


const dialogadd = ref(false)
const dialogEdit = ref(false)
const showMenu = ref(false)

const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const page = ref(
  {
    pageSize: 8,
    currentPage: 1,
    total: 0,
  },
)

const lampList = ref([])
const searchList = () => {
  getlampList(page.value.currentPage, page.value.pageSize, search.value).then(res => {
    lampList.value = res.data.data.records
    page.value.total = res.data.data.total
    page.value.currentPage = res.data.data.current
    page.value.pageSize = res.data.data.size
  })
}


const search = ref({
  poleName: '',
  poleNo: ''
})

const addlightpole = (()=>{
  console.log('addlightpole')
  dialogadd.value = true
  console.log(dialogadd.value)
})

const menuData = ref([
  {
    name: '单灯',
    // icon: ArrowRightBold,
    path: '/lamp/console',
  },
  {
    name: '集中控制器',
    // icon: ArrowRightBold,
    path: '/lamp/switchbox',
  }
])
const toluyou = (item) => {
  console.log('跳转',item)
  showMenu.value = !showMenu.value
  router.push(item.path);
}

const handleCloseadd = (done) => {
  dialogadd.value = false
  searchList()
}

const EditClose = (done) => {
  dialogEdit.value = false
  searchList()
}

const handleSizeChange = (val) => {
  page.value.pageSize = val
  searchList()
}

const handleCurrentChange=(val)=>{
  console.log(`current page: ${val}`)
  page.value.currentPage = val
  searchList()
}

const deletelightpole = (row) => {
  ElMessageBox.confirm(
    '该操作将删除该灯杆，是否继续？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      remove(row.id).then(res => {
        searchList()
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
      }).catch(error=>{
        console.log(error)
      })
     
      
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: 'Delete canceled',
      })
    })

  
}

const editdata = ref({})
const editlightpole = (row) => {
  editdata.value = row
  dialogEdit.value = true
}


onMounted(() => {
  searchList()
})


</script>
<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.container{
  height: 100%;
  padding: 16px 16px 0;
}
.itemBig-box{
  width: 100%;
  height: calc(100% - 150px);
  display: grid;
  grid-template-columns: repeat(4, 375px);
  grid-gap: 16px 24px;
  margin-top: 24px;
  .item-box{
    height: 335px;
    background-color: #fff;
    border-radius: 8px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    padding: 14px 16px 16px;
    .xinxibox-top{
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      padding-bottom: 12px;
      .xinxibox-top-text{
        font-family: Source Han Sans;
        font-size: 18px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.03em;
        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #333333;
        display: flex;
        align-items: center;
      }
      .xinxibox-top-text::before{
        content: "";
        display: inline-block;
        width: 4px;
        height: 24px;
        border-radius: 81px;
        background: #2E74FF;
        margin-right: 6px;
      }
      .item-edit{
        display: flex;
        align-items: center;
      }
    }
    .xinxibox-bottom{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px;
      .xinxi-box{
        display: flex;
        flex-direction: column;
        color: #000;
        margin-bottom: 16px;
        .xinxi-box-text{
          font-family: Source Han Sans;
          font-size: 16px;
          font-weight: normal;
          line-height: normal;
          letter-spacing: 0.03em;
          font-variation-settings: "opsz" auto;
          font-feature-settings: "kern" on;
          color: #666666;
        }
        .xinxi-box-nr{
          font-family: Source Han Sans;
          font-size: 16px;
          font-weight: 500;
          line-height: normal;
          letter-spacing: 0.03em;
          font-variation-settings: "opsz" auto;
          font-feature-settings: "kern" on;
          color: #333333;
          margin-top: 8px;
          width: 169px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .xinxibox-img{
        width: 157px;
        height: 218px;
        border-radius: 8px;
        background: #F4F4F4;
        overflow: hidden;
        .item-box-img{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}



.add-box{
  margin-right: 24px;
}
.top-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  border-radius: 8px;
  background: #FFFFFF;
  padding: 0 16px;
}
.paging-box{
  display: flex;
  justify-content: flex-end;
  margin: 24px 0;
}

.menu-btn{
  position: fixed;
  right: 0;
  bottom: calc(50% - 130px);
  width: 52px;
  height: 262px;
  font-size: 20px;
  line-height: 52px;
  writing-mode: vertical-lr;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  cursor: pointer;
}


.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}
</style>
