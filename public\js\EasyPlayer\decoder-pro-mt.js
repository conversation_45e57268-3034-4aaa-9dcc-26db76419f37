!function(e){"function"==typeof define&&define.amd?define(["crypto-js"],e):e()}((function(){"use strict";var e,t=(e="undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-mt.js",document.baseURI).href,function(t){var r,i;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(e,t){r=e,i=t})),(t=void 0!==t?t:{}).locateFile=function(e){return"decoder-pro-mt-worker.wasm"==e&&"undefined"!=typeof EASYPLAYER_PRO_MT_WORKER_WASM_URL&&""!=EASYPLAYER_PRO_MT_WORKER_WASM_URL?EASYPLAYER_PRO_MT_WORKER_WASM_URL:e};var n,o,s,a,d,l,u=Object.assign({},t),c="./this.program",f=(e,t)=>{throw t},h="object"==typeof window,p="function"==typeof importScripts,m="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,_=t.ENVIRONMENT_IS_PTHREAD||!1,g="";function y(e){return t.locateFile?t.locateFile(e,g):g+e}if(m){let e;g=p?require("path").dirname(g)+"/":__dirname+"/",l=()=>{d||(a=require("fs"),d=require("path"))},n=function(e,t){return l(),e=d.normalize(e),a.readFileSync(e,t?void 0:"utf8")},s=e=>{var t=n(e,!0);return t.buffer||(t=new Uint8Array(t)),t},o=(e,t,r)=>{l(),e=d.normalize(e),a.readFile(e,(function(e,i){e?r(e):t(i.buffer)}))},process.argv.length>1&&(c=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof fe))throw e})),process.on("unhandledRejection",(function(e){throw e})),f=(e,t)=>{if(Z())throw process.exitCode=e,t;var r;(r=t)instanceof fe||S("exiting due to exception: "+r),process.exit(e)},t.inspect=function(){return"[Emscripten Module object]"};try{e=require("worker_threads")}catch(e){throw console.error('The "worker_threads" module is not supported in this node.js build - perhaps a newer version is needed?'),e}global.Worker=e.Worker}else(h||p)&&(p?g=self.location.href:"undefined"!=typeof document&&document.currentScript&&(g=document.currentScript.src),e&&(g=e),g=0!==g.indexOf("blob:")?g.substr(0,g.replace(/[?#].*/,"").lastIndexOf("/")+1):"",m||(n=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},p&&(s=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),o=(e,t,r)=>{var i=new XMLHttpRequest;i.open("GET",e,!0),i.responseType="arraybuffer",i.onload=()=>{200==i.status||0==i.status&&i.response?t(i.response):r()},i.onerror=r,i.send(null)}));m&&"undefined"==typeof performance&&(global.performance=require("perf_hooks").performance);var v=console.log.bind(console),b=console.warn.bind(console);m&&(l(),v=e=>a.writeSync(1,e+"\n"),b=e=>a.writeSync(2,e+"\n"));var w=t.print||v,S=t.printErr||b;Object.assign(t,u),u=null,t.arguments&&t.arguments,t.thisProgram&&(c=t.thisProgram),t.quit&&(f=t.quit);var E,A=4;t.wasmBinary&&(E=t.wasmBinary);var U,T,x=t.noExitRuntime||!0;"object"!=typeof WebAssembly&&ie("no native wasm support detected");var B,k=!1;function C(e,t){e||ie(t)}var D,P,F,I,L,M,R,N,z,O="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function G(e,t,r){for(var i=t+r,n=t;e[n]&&!(n>=i);)++n;if(n-t>16&&e.buffer&&O)return O.decode(e.buffer instanceof SharedArrayBuffer?e.slice(t,n):e.subarray(t,n));for(var o="";t<n;){var s=e[t++];if(128&s){var a=63&e[t++];if(192!=(224&s)){var d=63&e[t++];if((s=224==(240&s)?(15&s)<<12|a<<6|d:(7&s)<<18|a<<12|d<<6|63&e[t++])<65536)o+=String.fromCharCode(s);else{var l=s-65536;o+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else o+=String.fromCharCode((31&s)<<6|a)}else o+=String.fromCharCode(s)}return o}function $(e,t){return e?G(F,e,t):""}function H(e,t,r,i){if(!(i>0))return 0;for(var n=r,o=r+i-1,s=0;s<e.length;++s){var a=e.charCodeAt(s);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++s)),a<=127){if(r>=o)break;t[r++]=a}else if(a<=2047){if(r+1>=o)break;t[r++]=192|a>>6,t[r++]=128|63&a}else if(a<=65535){if(r+2>=o)break;t[r++]=224|a>>12,t[r++]=128|a>>6&63,t[r++]=128|63&a}else{if(r+3>=o)break;t[r++]=240|a>>18,t[r++]=128|a>>12&63,t[r++]=128|a>>6&63,t[r++]=128|63&a}}return t[r]=0,r-n}function V(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i<=127?t++:i<=2047?t+=2:i>=55296&&i<=57343?(t+=4,++r):t+=3}return t}_&&(D=t.buffer);var W,j,q=t.INITIAL_MEMORY||268435456;if(_)U=t.wasmMemory,D=t.buffer;else if(t.wasmMemory)U=t.wasmMemory;else if(!((U=new WebAssembly.Memory({initial:q/65536,maximum:q/65536,shared:!0})).buffer instanceof SharedArrayBuffer))throw S("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag"),m&&console.log("(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and also use a recent version)"),Error("bad memory");U&&(D=U.buffer),q=D.byteLength,D=W=D,t.HEAP8=P=new Int8Array(W),t.HEAP16=I=new Int16Array(W),t.HEAP32=M=new Int32Array(W),t.HEAPU8=F=new Uint8Array(W),t.HEAPU16=L=new Uint16Array(W),t.HEAPU32=R=new Uint32Array(W),t.HEAPF32=N=new Float32Array(W),t.HEAPF64=z=new Float64Array(W);var Y=[],K=[],X=[];function Z(){return x}function J(){_||(t.noFSInit||we.init.initialized||we.init(),we.ignorePermissions=!1,Te(K))}var Q=0,ee=null;function te(e){Q++,t.monitorRunDependencies&&t.monitorRunDependencies(Q)}function re(e){if(Q--,t.monitorRunDependencies&&t.monitorRunDependencies(Q),0==Q&&ee){var r=ee;ee=null,r()}}function ie(e){_?postMessage({cmd:"onAbort",arg:e}):t.onAbort&&t.onAbort(e),S(e="Aborted("+e+")"),k=!0,B=1,e+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(e);throw i(r),r}var ne,oe,se,ae="data:application/octet-stream;base64,";function de(e){return e.startsWith(ae)}function le(e){return e.startsWith("file://")}function ue(e){try{if(e==ne&&E)return new Uint8Array(E);if(s)return s(e);throw"both async and sync fetching of the wasm failed"}catch(e){ie(e)}}t.locateFile?de(ne="decoder-pro-mt-worker.wasm")||(ne=y(ne)):ne=new URL("decoder-pro-mt-worker.wasm","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-mt.js",document.baseURI).href).toString();var ce={};function fe(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function he(e){var t=Ue.pthreads[e];C(t),Ue.returnWorkerToPool(t)}function pe(e){var t=Ue.getNewWorker();if(!t)return 6;Ue.runningWorkers.push(t),Ue.pthreads[e.pthread_ptr]=t,t.pthread_ptr=e.pthread_ptr;var r={cmd:"run",start_routine:e.startRoutine,arg:e.arg,pthread_ptr:e.pthread_ptr};return t.runPthread=()=>{r.time=performance.now(),t.postMessage(r,e.transferList)},t.loaded&&(t.runPthread(),delete t.runPthread),0}var me={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,i=e.length-1;i>=0;i--){var n=e[i];"."===n?e.splice(i,1):".."===n?(e.splice(i,1),r++):r&&(e.splice(i,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=me.isAbs(e),r="/"===e.substr(-1);return(e=me.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=me.splitPath(e),r=t[0],i=t[1];return r||i?(i&&(i=i.substr(0,i.length-1)),r+i):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=me.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return me.normalize(e.join("/"))},join2:(e,t)=>me.normalize(e+"/"+t)},_e={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var i=r>=0?arguments[r]:we.cwd();if("string"!=typeof i)throw new TypeError("Arguments to path.resolve must be strings");if(!i)return"";e=i+"/"+e,t=me.isAbs(i)}return(t?"/":"")+(e=me.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=_e.resolve(e).substr(1),t=_e.resolve(t).substr(1);for(var i=r(e.split("/")),n=r(t.split("/")),o=Math.min(i.length,n.length),s=o,a=0;a<o;a++)if(i[a]!==n[a]){s=a;break}var d=[];for(a=s;a<i.length;a++)d.push("..");return(d=d.concat(n.slice(s))).join("/")}};function ge(e,t,r){var i=r>0?r:V(e)+1,n=new Array(i),o=H(e,n,0,n.length);return t&&(n.length=o),n}var ye={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){ye.ttys[e]={input:[],output:[],ops:t},we.registerDevice(e,ye.stream_ops)},stream_ops:{open:function(e){var t=ye.ttys[e.node.rdev];if(!t)throw new we.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.get_char)throw new we.ErrnoError(60);for(var o=0,s=0;s<i;s++){var a;try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new we.ErrnoError(29)}if(void 0===a&&0===o)throw new we.ErrnoError(6);if(null==a)break;o++,t[r+s]=a}return o&&(e.node.timestamp=Date.now()),o},write:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.put_char)throw new we.ErrnoError(60);try{for(var o=0;o<i;o++)e.tty.ops.put_char(e.tty,t[r+o])}catch(e){throw new we.ErrnoError(29)}return i&&(e.node.timestamp=Date.now()),o}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(m){var r=Buffer.alloc(256),i=0;try{i=a.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;i=0}t=i>0?r.slice(0,i).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=ge(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(w(G(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(w(G(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(S(G(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(S(G(e.output,0)),e.output=[])}}};function ve(e){e=function(e,t){return Math.ceil(e/t)*t}(e,65536);var t=kr(65536,e);return t?(function(e,t){F.fill(0,e,e+t)}(t,e),t):0}var be={ops_table:null,mount:function(e){return be.createNode(null,"/",16895,0)},createNode:function(e,t,r,i){if(we.isBlkdev(r)||we.isFIFO(r))throw new we.ErrnoError(63);be.ops_table||(be.ops_table={dir:{node:{getattr:be.node_ops.getattr,setattr:be.node_ops.setattr,lookup:be.node_ops.lookup,mknod:be.node_ops.mknod,rename:be.node_ops.rename,unlink:be.node_ops.unlink,rmdir:be.node_ops.rmdir,readdir:be.node_ops.readdir,symlink:be.node_ops.symlink},stream:{llseek:be.stream_ops.llseek}},file:{node:{getattr:be.node_ops.getattr,setattr:be.node_ops.setattr},stream:{llseek:be.stream_ops.llseek,read:be.stream_ops.read,write:be.stream_ops.write,allocate:be.stream_ops.allocate,mmap:be.stream_ops.mmap,msync:be.stream_ops.msync}},link:{node:{getattr:be.node_ops.getattr,setattr:be.node_ops.setattr,readlink:be.node_ops.readlink},stream:{}},chrdev:{node:{getattr:be.node_ops.getattr,setattr:be.node_ops.setattr},stream:we.chrdev_stream_ops}});var n=we.createNode(e,t,r,i);return we.isDir(n.mode)?(n.node_ops=be.ops_table.dir.node,n.stream_ops=be.ops_table.dir.stream,n.contents={}):we.isFile(n.mode)?(n.node_ops=be.ops_table.file.node,n.stream_ops=be.ops_table.file.stream,n.usedBytes=0,n.contents=null):we.isLink(n.mode)?(n.node_ops=be.ops_table.link.node,n.stream_ops=be.ops_table.link.stream):we.isChrdev(n.mode)&&(n.node_ops=be.ops_table.chrdev.node,n.stream_ops=be.ops_table.chrdev.stream),n.timestamp=Date.now(),e&&(e.contents[t]=n,e.timestamp=n.timestamp),n},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var i=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(i.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=we.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,we.isDir(e.mode)?t.size=4096:we.isFile(e.mode)?t.size=e.usedBytes:we.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&be.resizeFileStorage(e,t.size)},lookup:function(e,t){throw we.genericErrors[44]},mknod:function(e,t,r,i){return be.createNode(e,t,r,i)},rename:function(e,t,r){if(we.isDir(e.mode)){var i;try{i=we.lookupNode(t,r)}catch(e){}if(i)for(var n in i.contents)throw new we.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=we.lookupNode(e,t);for(var i in r.contents)throw new we.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var i=be.createNode(e,t,41471,0);return i.link=r,i},readlink:function(e){if(!we.isLink(e.mode))throw new we.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,i,n){var o=e.node.contents;if(n>=e.node.usedBytes)return 0;var s=Math.min(e.node.usedBytes-n,i);if(s>8&&o.subarray)t.set(o.subarray(n,n+s),r);else for(var a=0;a<s;a++)t[r+a]=o[n+a];return s},write:function(e,t,r,i,n,o){if(!i)return 0;var s=e.node;if(s.timestamp=Date.now(),t.subarray&&(!s.contents||s.contents.subarray)){if(o)return s.contents=t.subarray(r,r+i),s.usedBytes=i,i;if(0===s.usedBytes&&0===n)return s.contents=t.slice(r,r+i),s.usedBytes=i,i;if(n+i<=s.usedBytes)return s.contents.set(t.subarray(r,r+i),n),i}if(be.expandFileStorage(s,n+i),s.contents.subarray&&t.subarray)s.contents.set(t.subarray(r,r+i),n);else for(var a=0;a<i;a++)s.contents[n+a]=t[r+a];return s.usedBytes=Math.max(s.usedBytes,n+i),i},llseek:function(e,t,r){var i=t;if(1===r?i+=e.position:2===r&&we.isFile(e.node.mode)&&(i+=e.node.usedBytes),i<0)throw new we.ErrnoError(28);return i},allocate:function(e,t,r){be.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,i,n){if(!we.isFile(e.node.mode))throw new we.ErrnoError(43);var o,s,a=e.node.contents;if(2&n||a.buffer!==D){if((r>0||r+t<a.length)&&(a=a.subarray?a.subarray(r,r+t):Array.prototype.slice.call(a,r,r+t)),s=!0,!(o=ve(t)))throw new we.ErrnoError(48);P.set(a,o)}else s=!1,o=a.byteOffset;return{ptr:o,allocated:s}},msync:function(e,t,r,i,n){if(!we.isFile(e.node.mode))throw new we.ErrnoError(43);return 2&n||be.stream_ops.write(e,t,0,i,r,!1),0}}},we={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(e=_e.resolve(we.cwd(),e)))return{path:"",node:null};if(t=Object.assign({follow_mount:!0,recurse_count:0},t),t.recurse_count>8)throw new we.ErrnoError(32);for(var r=me.normalizeArray(e.split("/").filter((e=>!!e)),!1),i=we.root,n="/",o=0;o<r.length;o++){var s=o===r.length-1;if(s&&t.parent)break;if(i=we.lookupNode(i,r[o]),n=me.join2(n,r[o]),we.isMountpoint(i)&&(!s||s&&t.follow_mount)&&(i=i.mounted.root),!s||t.follow)for(var a=0;we.isLink(i.mode);){var d=we.readlink(n);if(n=_e.resolve(me.dirname(n),d),i=we.lookupPath(n,{recurse_count:t.recurse_count+1}).node,a++>40)throw new we.ErrnoError(32)}}return{path:n,node:i}},getPath:e=>{for(var t;;){if(we.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,i=0;i<t.length;i++)r=(r<<5)-r+t.charCodeAt(i)|0;return(e+r>>>0)%we.nameTable.length},hashAddNode:e=>{var t=we.hashName(e.parent.id,e.name);e.name_next=we.nameTable[t],we.nameTable[t]=e},hashRemoveNode:e=>{var t=we.hashName(e.parent.id,e.name);if(we.nameTable[t]===e)we.nameTable[t]=e.name_next;else for(var r=we.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=we.mayLookup(e);if(r)throw new we.ErrnoError(r,e);for(var i=we.hashName(e.id,t),n=we.nameTable[i];n;n=n.name_next){var o=n.name;if(n.parent.id===e.id&&o===t)return n}return we.lookup(e,t)},createNode:(e,t,r,i)=>{var n=new we.FSNode(e,t,r,i);return we.hashAddNode(n),n},destroyNode:e=>{we.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=we.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>we.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=we.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return we.lookupNode(e,t),20}catch(e){}return we.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var i;try{i=we.lookupNode(e,t)}catch(e){return e.errno}var n=we.nodePermissions(e,"wx");if(n)return n;if(r){if(!we.isDir(i.mode))return 54;if(we.isRoot(i)||we.getPath(i)===we.cwd())return 10}else if(we.isDir(i.mode))return 31;return 0},mayOpen:(e,t)=>e?we.isLink(e.mode)?32:we.isDir(e.mode)&&("r"!==we.flagsToPermissionString(t)||512&t)?31:we.nodePermissions(e,we.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:we.MAX_OPEN_FDS;for(var r=e;r<=t;r++)if(!we.streams[r])return r;throw new we.ErrnoError(33)},getStream:e=>we.streams[e],createStream:(e,t,r)=>{we.FSStream||(we.FSStream=function(){this.shared={}},we.FSStream.prototype={},Object.defineProperties(we.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new we.FSStream,e);var i=we.nextfd(t,r);return e.fd=i,we.streams[i]=e,e},closeStream:e=>{we.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=we.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new we.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{we.devices[e]={stream_ops:t}},getDevice:e=>we.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var i=r.pop();t.push(i),r.push.apply(r,i.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),we.syncFSRequests++,we.syncFSRequests>1&&S("warning: "+we.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=we.getMounts(we.root.mount),i=0;function n(e){return we.syncFSRequests--,t(e)}function o(e){if(e)return o.errored?void 0:(o.errored=!0,n(e));++i>=r.length&&n(null)}r.forEach((t=>{if(!t.type.syncfs)return o(null);t.type.syncfs(t,e,o)}))},mount:(e,t,r)=>{var i,n="/"===r,o=!r;if(n&&we.root)throw new we.ErrnoError(10);if(!n&&!o){var s=we.lookupPath(r,{follow_mount:!1});if(r=s.path,i=s.node,we.isMountpoint(i))throw new we.ErrnoError(10);if(!we.isDir(i.mode))throw new we.ErrnoError(54)}var a={type:e,opts:t,mountpoint:r,mounts:[]},d=e.mount(a);return d.mount=a,a.root=d,n?we.root=d:i&&(i.mounted=a,i.mount&&i.mount.mounts.push(a)),d},unmount:e=>{var t=we.lookupPath(e,{follow_mount:!1});if(!we.isMountpoint(t.node))throw new we.ErrnoError(28);var r=t.node,i=r.mounted,n=we.getMounts(i);Object.keys(we.nameTable).forEach((e=>{for(var t=we.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&we.destroyNode(t),t=r}})),r.mounted=null;var o=r.mount.mounts.indexOf(i);r.mount.mounts.splice(o,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var i=we.lookupPath(e,{parent:!0}).node,n=me.basename(e);if(!n||"."===n||".."===n)throw new we.ErrnoError(28);var o=we.mayCreate(i,n);if(o)throw new we.ErrnoError(o);if(!i.node_ops.mknod)throw new we.ErrnoError(63);return i.node_ops.mknod(i,n,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,we.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,we.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),i="",n=0;n<r.length;++n)if(r[n]){i+="/"+r[n];try{we.mkdir(i,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,we.mknod(e,t,r)),symlink:(e,t)=>{if(!_e.resolve(e))throw new we.ErrnoError(44);var r=we.lookupPath(t,{parent:!0}).node;if(!r)throw new we.ErrnoError(44);var i=me.basename(t),n=we.mayCreate(r,i);if(n)throw new we.ErrnoError(n);if(!r.node_ops.symlink)throw new we.ErrnoError(63);return r.node_ops.symlink(r,i,e)},rename:(e,t)=>{var r,i,n=me.dirname(e),o=me.dirname(t),s=me.basename(e),a=me.basename(t);if(r=we.lookupPath(e,{parent:!0}).node,i=we.lookupPath(t,{parent:!0}).node,!r||!i)throw new we.ErrnoError(44);if(r.mount!==i.mount)throw new we.ErrnoError(75);var d,l=we.lookupNode(r,s),u=_e.relative(e,o);if("."!==u.charAt(0))throw new we.ErrnoError(28);if("."!==(u=_e.relative(t,n)).charAt(0))throw new we.ErrnoError(55);try{d=we.lookupNode(i,a)}catch(e){}if(l!==d){var c=we.isDir(l.mode),f=we.mayDelete(r,s,c);if(f)throw new we.ErrnoError(f);if(f=d?we.mayDelete(i,a,c):we.mayCreate(i,a))throw new we.ErrnoError(f);if(!r.node_ops.rename)throw new we.ErrnoError(63);if(we.isMountpoint(l)||d&&we.isMountpoint(d))throw new we.ErrnoError(10);if(i!==r&&(f=we.nodePermissions(r,"w")))throw new we.ErrnoError(f);we.hashRemoveNode(l);try{r.node_ops.rename(l,i,a)}catch(e){throw e}finally{we.hashAddNode(l)}}},rmdir:e=>{var t=we.lookupPath(e,{parent:!0}).node,r=me.basename(e),i=we.lookupNode(t,r),n=we.mayDelete(t,r,!0);if(n)throw new we.ErrnoError(n);if(!t.node_ops.rmdir)throw new we.ErrnoError(63);if(we.isMountpoint(i))throw new we.ErrnoError(10);t.node_ops.rmdir(t,r),we.destroyNode(i)},readdir:e=>{var t=we.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new we.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=we.lookupPath(e,{parent:!0}).node;if(!t)throw new we.ErrnoError(44);var r=me.basename(e),i=we.lookupNode(t,r),n=we.mayDelete(t,r,!1);if(n)throw new we.ErrnoError(n);if(!t.node_ops.unlink)throw new we.ErrnoError(63);if(we.isMountpoint(i))throw new we.ErrnoError(10);t.node_ops.unlink(t,r),we.destroyNode(i)},readlink:e=>{var t=we.lookupPath(e).node;if(!t)throw new we.ErrnoError(44);if(!t.node_ops.readlink)throw new we.ErrnoError(28);return _e.resolve(we.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=we.lookupPath(e,{follow:!t}).node;if(!r)throw new we.ErrnoError(44);if(!r.node_ops.getattr)throw new we.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>we.stat(e,!0),chmod:(e,t,r)=>{var i;if(!(i="string"==typeof e?we.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new we.ErrnoError(63);i.node_ops.setattr(i,{mode:4095&t|-4096&i.mode,timestamp:Date.now()})},lchmod:(e,t)=>{we.chmod(e,t,!0)},fchmod:(e,t)=>{var r=we.getStream(e);if(!r)throw new we.ErrnoError(8);we.chmod(r.node,t)},chown:(e,t,r,i)=>{var n;if(!(n="string"==typeof e?we.lookupPath(e,{follow:!i}).node:e).node_ops.setattr)throw new we.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{we.chown(e,t,r,!0)},fchown:(e,t,r)=>{var i=we.getStream(e);if(!i)throw new we.ErrnoError(8);we.chown(i.node,t,r)},truncate:(e,t)=>{if(t<0)throw new we.ErrnoError(28);var r;if(!(r="string"==typeof e?we.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new we.ErrnoError(63);if(we.isDir(r.mode))throw new we.ErrnoError(31);if(!we.isFile(r.mode))throw new we.ErrnoError(28);var i=we.nodePermissions(r,"w");if(i)throw new we.ErrnoError(i);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=we.getStream(e);if(!r)throw new we.ErrnoError(8);if(0==(2097155&r.flags))throw new we.ErrnoError(28);we.truncate(r.node,t)},utime:(e,t,r)=>{var i=we.lookupPath(e,{follow:!0}).node;i.node_ops.setattr(i,{timestamp:Math.max(t,r)})},open:(e,r,i)=>{if(""===e)throw new we.ErrnoError(44);var n;if(i=void 0===i?438:i,i=64&(r="string"==typeof r?we.modeStringToFlags(r):r)?4095&i|32768:0,"object"==typeof e)n=e;else{e=me.normalize(e);try{n=we.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var o=!1;if(64&r)if(n){if(128&r)throw new we.ErrnoError(20)}else n=we.mknod(e,i,0),o=!0;if(!n)throw new we.ErrnoError(44);if(we.isChrdev(n.mode)&&(r&=-513),65536&r&&!we.isDir(n.mode))throw new we.ErrnoError(54);if(!o){var s=we.mayOpen(n,r);if(s)throw new we.ErrnoError(s)}512&r&&!o&&we.truncate(n,0),r&=-131713;var a=we.createStream({node:n,path:we.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return a.stream_ops.open&&a.stream_ops.open(a),!t.logReadFiles||1&r||(we.readFiles||(we.readFiles={}),e in we.readFiles||(we.readFiles[e]=1)),a},close:e=>{if(we.isClosed(e))throw new we.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{we.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(we.isClosed(e))throw new we.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new we.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new we.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,i,n)=>{if(i<0||n<0)throw new we.ErrnoError(28);if(we.isClosed(e))throw new we.ErrnoError(8);if(1==(2097155&e.flags))throw new we.ErrnoError(8);if(we.isDir(e.node.mode))throw new we.ErrnoError(31);if(!e.stream_ops.read)throw new we.ErrnoError(28);var o=void 0!==n;if(o){if(!e.seekable)throw new we.ErrnoError(70)}else n=e.position;var s=e.stream_ops.read(e,t,r,i,n);return o||(e.position+=s),s},write:(e,t,r,i,n,o)=>{if(i<0||n<0)throw new we.ErrnoError(28);if(we.isClosed(e))throw new we.ErrnoError(8);if(0==(2097155&e.flags))throw new we.ErrnoError(8);if(we.isDir(e.node.mode))throw new we.ErrnoError(31);if(!e.stream_ops.write)throw new we.ErrnoError(28);e.seekable&&1024&e.flags&&we.llseek(e,0,2);var s=void 0!==n;if(s){if(!e.seekable)throw new we.ErrnoError(70)}else n=e.position;var a=e.stream_ops.write(e,t,r,i,n,o);return s||(e.position+=a),a},allocate:(e,t,r)=>{if(we.isClosed(e))throw new we.ErrnoError(8);if(t<0||r<=0)throw new we.ErrnoError(28);if(0==(2097155&e.flags))throw new we.ErrnoError(8);if(!we.isFile(e.node.mode)&&!we.isDir(e.node.mode))throw new we.ErrnoError(43);if(!e.stream_ops.allocate)throw new we.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,i,n)=>{if(0!=(2&i)&&0==(2&n)&&2!=(2097155&e.flags))throw new we.ErrnoError(2);if(1==(2097155&e.flags))throw new we.ErrnoError(2);if(!e.stream_ops.mmap)throw new we.ErrnoError(43);return e.stream_ops.mmap(e,t,r,i,n)},msync:(e,t,r,i,n)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,i,n):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new we.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,i=we.open(e,t.flags),n=we.stat(e).size,o=new Uint8Array(n);return we.read(i,o,0,n,0),"utf8"===t.encoding?r=G(o,0):"binary"===t.encoding&&(r=o),we.close(i),r},writeFile:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r.flags=r.flags||577;var i=we.open(e,r.flags,r.mode);if("string"==typeof t){var n=new Uint8Array(V(t)+1),o=H(t,n,0,n.length);we.write(i,n,0,o,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");we.write(i,t,0,t.byteLength,void 0,r.canOwn)}we.close(i)},cwd:()=>we.currentPath,chdir:e=>{var t=we.lookupPath(e,{follow:!0});if(null===t.node)throw new we.ErrnoError(44);if(!we.isDir(t.node.mode))throw new we.ErrnoError(54);var r=we.nodePermissions(t.node,"x");if(r)throw new we.ErrnoError(r);we.currentPath=t.path},createDefaultDirectories:()=>{we.mkdir("/tmp"),we.mkdir("/home"),we.mkdir("/home/<USER>")},createDefaultDevices:()=>{we.mkdir("/dev"),we.registerDevice(we.makedev(1,3),{read:()=>0,write:(e,t,r,i,n)=>i}),we.mkdev("/dev/null",we.makedev(1,3)),ye.register(we.makedev(5,0),ye.default_tty_ops),ye.register(we.makedev(6,0),ye.default_tty1_ops),we.mkdev("/dev/tty",we.makedev(5,0)),we.mkdev("/dev/tty1",we.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}if(m)try{var t=require("crypto");return()=>t.randomBytes(1)[0]}catch(e){}return()=>ie("randomDevice")}();we.createDevice("/dev","random",e),we.createDevice("/dev","urandom",e),we.mkdir("/dev/shm"),we.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{we.mkdir("/proc");var e=we.mkdir("/proc/self");we.mkdir("/proc/self/fd"),we.mount({mount:()=>{var t=we.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,i=we.getStream(r);if(!i)throw new we.ErrnoError(8);var n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>i.path}};return n.parent=n,n}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{t.stdin?we.createDevice("/dev","stdin",t.stdin):we.symlink("/dev/tty","/dev/stdin"),t.stdout?we.createDevice("/dev","stdout",null,t.stdout):we.symlink("/dev/tty","/dev/stdout"),t.stderr?we.createDevice("/dev","stderr",null,t.stderr):we.symlink("/dev/tty1","/dev/stderr"),we.open("/dev/stdin",0),we.open("/dev/stdout",1),we.open("/dev/stderr",1)},ensureErrnoError:()=>{we.ErrnoError||(we.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},we.ErrnoError.prototype=new Error,we.ErrnoError.prototype.constructor=we.ErrnoError,[44].forEach((e=>{we.genericErrors[e]=new we.ErrnoError(e),we.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{we.ensureErrnoError(),we.nameTable=new Array(4096),we.mount(be,{},"/"),we.createDefaultDirectories(),we.createDefaultDevices(),we.createSpecialDirectories(),we.filesystems={MEMFS:be}},init:(e,r,i)=>{we.init.initialized=!0,we.ensureErrnoError(),t.stdin=e||t.stdin,t.stdout=r||t.stdout,t.stderr=i||t.stderr,we.createStandardStreams()},quit:()=>{we.init.initialized=!1;for(var e=0;e<we.streams.length;e++){var t=we.streams[e];t&&we.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=we.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(i=we.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var i=we.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=i.path,r.parentObject=i.node,r.name=me.basename(e),i=we.lookupPath(e,{follow:!t}),r.exists=!0,r.path=i.path,r.object=i.node,r.name=i.node.name,r.isRoot="/"===i.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,i)=>{e="string"==typeof e?e:we.getPath(e);for(var n=t.split("/").reverse();n.length;){var o=n.pop();if(o){var s=me.join2(e,o);try{we.mkdir(s)}catch(e){}e=s}}return s},createFile:(e,t,r,i,n)=>{var o=me.join2("string"==typeof e?e:we.getPath(e),t),s=we.getMode(i,n);return we.create(o,s)},createDataFile:(e,t,r,i,n,o)=>{var s=t;e&&(e="string"==typeof e?e:we.getPath(e),s=t?me.join2(e,t):e);var a=we.getMode(i,n),d=we.create(s,a);if(r){if("string"==typeof r){for(var l=new Array(r.length),u=0,c=r.length;u<c;++u)l[u]=r.charCodeAt(u);r=l}we.chmod(d,146|a);var f=we.open(d,577);we.write(f,r,0,r.length,0,o),we.close(f),we.chmod(d,a)}return d},createDevice:(e,t,r,i)=>{var n=me.join2("string"==typeof e?e:we.getPath(e),t),o=we.getMode(!!r,!!i);we.createDevice.major||(we.createDevice.major=64);var s=we.makedev(we.createDevice.major++,0);return we.registerDevice(s,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,i,n,o)=>{for(var s=0,a=0;a<n;a++){var d;try{d=r()}catch(e){throw new we.ErrnoError(29)}if(void 0===d&&0===s)throw new we.ErrnoError(6);if(null==d)break;s++,t[i+a]=d}return s&&(e.node.timestamp=Date.now()),s},write:(e,t,r,n,o)=>{for(var s=0;s<n;s++)try{i(t[r+s])}catch(e){throw new we.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),s}}),we.mkdev(n,o,s)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!n)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=ge(n(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new we.ErrnoError(29)}},createLazyFile:(e,t,r,i,n)=>{function o(){this.lengthKnown=!1,this.chunks=[]}if(o.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},o.prototype.setDataGetter=function(e){this.getter=e},o.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,i=Number(e.getResponseHeader("Content-length")),n=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,o=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,s=1048576;n||(s=i);var a=this;a.setDataGetter((e=>{var t=e*s,n=(e+1)*s-1;if(n=Math.min(n,i-1),void 0===a.chunks[e]&&(a.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>i-1)throw new Error("only "+i+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",r,!1),i!==s&&n.setRequestHeader("Range","bytes="+e+"-"+t),n.responseType="arraybuffer",n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(n.status>=200&&n.status<300||304===n.status))throw new Error("Couldn't load "+r+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):ge(n.responseText||"",!0)})(t,n)),void 0===a.chunks[e])throw new Error("doXHR failed!");return a.chunks[e]})),!o&&i||(s=i=1,i=this.getter(0).length,s=i,w("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=i,this._chunkSize=s,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!p)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var s=new o;Object.defineProperties(s,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var a={isDevice:!1,contents:s}}else a={isDevice:!1,url:r};var d=we.createFile(e,t,a,i,n);a.contents?d.contents=a.contents:a.url&&(d.contents=null,d.url=a.url),Object.defineProperties(d,{usedBytes:{get:function(){return this.contents.length}}});var l={};function u(e,t,r,i,n){var o=e.node.contents;if(n>=o.length)return 0;var s=Math.min(o.length-n,i);if(o.slice)for(var a=0;a<s;a++)t[r+a]=o[n+a];else for(a=0;a<s;a++)t[r+a]=o.get(n+a);return s}return Object.keys(d.stream_ops).forEach((e=>{var t=d.stream_ops[e];l[e]=function(){return we.forceLoadFile(d),t.apply(null,arguments)}})),l.read=(e,t,r,i,n)=>(we.forceLoadFile(d),u(e,t,r,i,n)),l.mmap=(e,t,r,i,n)=>{we.forceLoadFile(d);var o=ve(t);if(!o)throw new we.ErrnoError(48);return u(e,P,o,t,r),{ptr:o,allocated:!0}},d.stream_ops=l,d},createPreloadedFile:(e,t,r,i,n,s,a,d,l,u)=>{var c=t?_e.resolve(me.join2(e,t)):e;function f(r){function o(r){u&&u(),d||we.createDataFile(e,t,r,i,n,l),s&&s(),re()}Browser.handledByPreloadPlugin(r,c,o,(()=>{a&&a(),re()}))||o(r)}te(),"string"==typeof r?function(e,t,r,i){var n=i?"":"al "+e;o(e,(r=>{C(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),n&&re()}),(t=>{if(!r)throw'Loading data file "'+e+'" failed.';r()})),n&&te()}(r,(e=>f(e)),a):f(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=we.indexedDB();try{var n=i.open(we.DB_NAME(),we.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=()=>{w("creating db"),n.result.createObjectStore(we.DB_STORE_NAME)},n.onsuccess=()=>{var i=n.result.transaction([we.DB_STORE_NAME],"readwrite"),o=i.objectStore(we.DB_STORE_NAME),s=0,a=0,d=e.length;function l(){0==a?t():r()}e.forEach((e=>{var t=o.put(we.analyzePath(e).object.contents,e);t.onsuccess=()=>{++s+a==d&&l()},t.onerror=()=>{a++,s+a==d&&l()}})),i.onerror=r},n.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=we.indexedDB();try{var n=i.open(we.DB_NAME(),we.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=r,n.onsuccess=()=>{var i=n.result;try{var o=i.transaction([we.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var s=o.objectStore(we.DB_STORE_NAME),a=0,d=0,l=e.length;function u(){0==d?t():r()}e.forEach((e=>{var t=s.get(e);t.onsuccess=()=>{we.analyzePath(e).exists&&we.unlink(e),we.createDataFile(me.dirname(e),me.basename(e),t.result,!0,!0,!0),++a+d==l&&u()},t.onerror=()=>{d++,a+d==l&&u()}})),o.onerror=r},n.onerror=r}},Se={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(me.isAbs(t))return t;var i;if(-100===e)i=we.cwd();else{var n=we.getStream(e);if(!n)throw new we.ErrnoError(8);i=n.path}if(0==t.length){if(!r)throw new we.ErrnoError(44);return i}return me.join2(i,t)},doStat:function(e,t,r){try{var i=e(t)}catch(e){if(e&&e.node&&me.normalize(t)!==me.normalize(we.getPath(e.node)))return-54;throw e}return M[r>>2]=i.dev,M[r+4>>2]=0,M[r+8>>2]=i.ino,M[r+12>>2]=i.mode,M[r+16>>2]=i.nlink,M[r+20>>2]=i.uid,M[r+24>>2]=i.gid,M[r+28>>2]=i.rdev,M[r+32>>2]=0,se=[i.size>>>0,(oe=i.size,+Math.abs(oe)>=1?oe>0?(0|Math.min(+Math.floor(oe/4294967296),4294967295))>>>0:~~+Math.ceil((oe-+(~~oe>>>0))/4294967296)>>>0:0)],M[r+40>>2]=se[0],M[r+44>>2]=se[1],M[r+48>>2]=4096,M[r+52>>2]=i.blocks,se=[Math.floor(i.atime.getTime()/1e3)>>>0,(oe=Math.floor(i.atime.getTime()/1e3),+Math.abs(oe)>=1?oe>0?(0|Math.min(+Math.floor(oe/4294967296),4294967295))>>>0:~~+Math.ceil((oe-+(~~oe>>>0))/4294967296)>>>0:0)],M[r+56>>2]=se[0],M[r+60>>2]=se[1],M[r+64>>2]=0,se=[Math.floor(i.mtime.getTime()/1e3)>>>0,(oe=Math.floor(i.mtime.getTime()/1e3),+Math.abs(oe)>=1?oe>0?(0|Math.min(+Math.floor(oe/4294967296),4294967295))>>>0:~~+Math.ceil((oe-+(~~oe>>>0))/4294967296)>>>0:0)],M[r+72>>2]=se[0],M[r+76>>2]=se[1],M[r+80>>2]=0,se=[Math.floor(i.ctime.getTime()/1e3)>>>0,(oe=Math.floor(i.ctime.getTime()/1e3),+Math.abs(oe)>=1?oe>0?(0|Math.min(+Math.floor(oe/4294967296),4294967295))>>>0:~~+Math.ceil((oe-+(~~oe>>>0))/4294967296)>>>0:0)],M[r+88>>2]=se[0],M[r+92>>2]=se[1],M[r+96>>2]=0,se=[i.ino>>>0,(oe=i.ino,+Math.abs(oe)>=1?oe>0?(0|Math.min(+Math.floor(oe/4294967296),4294967295))>>>0:~~+Math.ceil((oe-+(~~oe>>>0))/4294967296)>>>0:0)],M[r+104>>2]=se[0],M[r+108>>2]=se[1],0},doMsync:function(e,t,r,i,n){var o=F.slice(e,e+r);we.msync(t,o,n,r,i)},varargs:void 0,get:function(){return Se.varargs+=4,M[Se.varargs-4>>2]},getStr:function(e){return $(e)},getStreamFromFD:function(e){var t=we.getStream(e);if(!t)throw new we.ErrnoError(8);return t}};function Ee(e){if(_)return lr(1,1,e);B=e,Z()||(Ue.terminateAllThreads(),t.onExit&&t.onExit(e),k=!0),f(e,new fe(e))}var Ae=function(e,t){if(B=e,!t&&_)throw xe(e),"unwind";Ee(e)},Ue={unusedWorkers:[],runningWorkers:[],tlsInitFunctions:[],pthreads:{},init:function(){_?Ue.initWorker():Ue.initMainThread()},initMainThread:function(){},initWorker:function(){x=!1},setExitStatus:function(e){B=e},terminateAllThreads:function(){for(var e in Ue.pthreads)(r=Ue.pthreads[e])&&Ue.returnWorkerToPool(r);for(var t=0;t<Ue.unusedWorkers.length;++t){var r;(r=Ue.unusedWorkers[t]).terminate()}Ue.unusedWorkers=[]},returnWorkerToPool:function(e){var t=e.pthread_ptr;delete Ue.pthreads[t],Ue.unusedWorkers.push(e),Ue.runningWorkers.splice(Ue.runningWorkers.indexOf(e),1),e.pthread_ptr=0,Lr(t)},receiveObjectTransfer:function(e){},threadInitTLS:function(){for(var e in Ue.tlsInitFunctions)Ue.tlsInitFunctions.hasOwnProperty(e)&&Ue.tlsInitFunctions[e]()},loadWasmModuleToWorker:function(e,r){e.onmessage=i=>{var n,o=i.data,s=o.cmd;if(e.pthread_ptr&&(Ue.currentProxiedOperationCallerThread=e.pthread_ptr),o.targetThread&&o.targetThread!=Br()){var a=Ue.pthreads[o.targetThread];return a?a.postMessage(o,o.transferList):S('Internal error! Worker sent a message "'+s+'" to target pthread '+o.targetThread+", but that thread no longer exists!"),void(Ue.currentProxiedOperationCallerThread=void 0)}"processProxyingQueue"===s?ir(o.queue):"spawnThread"===s?pe(o):"cleanupThread"===s?he(o.thread):"killThread"===s?function(e){var t=Ue.pthreads[e];delete Ue.pthreads[e],t.terminate(),Lr(e),Ue.runningWorkers.splice(Ue.runningWorkers.indexOf(t),1),t.pthread_ptr=0}(o.thread):"cancelThread"===s?(n=o.thread,Ue.pthreads[n].postMessage({cmd:"cancel"})):"loaded"===s?(e.loaded=!0,r&&r(e),e.runPthread&&(e.runPthread(),delete e.runPthread)):"print"===s?w("Thread "+o.threadId+": "+o.text):"printErr"===s?S("Thread "+o.threadId+": "+o.text):"alert"===s?alert("Thread "+o.threadId+": "+o.text):"setimmediate"===o.target?e.postMessage(o):"onAbort"===s?t.onAbort&&t.onAbort(o.arg):s&&S("worker sent an unknown command "+s),Ue.currentProxiedOperationCallerThread=void 0},e.onerror=e=>{throw S("worker sent an error! "+e.filename+":"+e.lineno+": "+e.message),e},m&&(e.on("message",(function(t){e.onmessage({data:t})})),e.on("error",(function(t){e.onerror(t)})),e.on("detachedExit",(function(){}))),e.postMessage({cmd:"load",urlOrBlob:t.mainScriptUrlOrBlob,wasmMemory:U,wasmModule:T})},allocateUnusedWorker:function(){if(t.locateFile){var e=y("decoder-pro-mt-worker.worker.js");Ue.unusedWorkers.push(new Worker(e))}else Ue.unusedWorkers.push(new Worker(new URL("decoder-pro-mt-worker.worker.js","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-mt.js",document.baseURI).href)))},getNewWorker:function(){return 0==Ue.unusedWorkers.length&&(Ue.allocateUnusedWorker(),Ue.loadWasmModuleToWorker(Ue.unusedWorkers[0])),Ue.unusedWorkers.pop()}};function Te(e){for(;e.length>0;)e.shift()(t)}function xe(e){if(_)return lr(2,0,e);try{Ae(e)}catch(e){!function(e){if(e instanceof fe||"unwind"==e)return B;f(1,e)}(e)}}t.PThread=Ue,t.establishStackSpace=function(){var e=Br(),t=M[e+44>>2],r=M[e+48>>2];Rr(t,t-r),zr(t)};var Be=[];function ke(e){var t=Be[e];return t||(e>=Be.length&&(Be.length=e+1),Be[e]=t=j.get(e)),t}function Ce(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){R[this.ptr+4>>2]=e},this.get_type=function(){return R[this.ptr+4>>2]},this.set_destructor=function(e){R[this.ptr+8>>2]=e},this.get_destructor=function(){return R[this.ptr+8>>2]},this.set_refcount=function(e){M[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,P[this.ptr+12>>0]=e},this.get_caught=function(){return 0!=P[this.ptr+12>>0]},this.set_rethrown=function(e){e=e?1:0,P[this.ptr+13>>0]=e},this.get_rethrown=function(){return 0!=P[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){Atomics.add(M,this.ptr+0>>2,1)},this.release_ref=function(){return 1===Atomics.sub(M,this.ptr+0>>2,1)},this.set_adjusted_ptr=function(e){R[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return R[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Gr(this.get_type()))return R[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}function De(e,t,r,i){return _?lr(3,1,e,t,r,i):Pe(e,t,r,i)}function Pe(e,t,r,i){if("undefined"==typeof SharedArrayBuffer)return S("Current environment does not support SharedArrayBuffer, pthreads are not available!"),6;var n=[];if(_&&0===n.length)return De(e,t,r,i);var o={startRoutine:r,pthread_ptr:e,arg:i,transferList:n};return _?(o.cmd="spawnThread",postMessage(o,n),0):pe(o)}function Fe(e,t,r){if(_)return lr(4,1,e,t,r);Se.varargs=r;try{var i=Se.getStreamFromFD(e);switch(t){case 0:return(n=Se.get())<0?-28:we.createStream(i,n).fd;case 1:case 2:case 6:case 7:return 0;case 3:return i.flags;case 4:var n=Se.get();return i.flags|=n,0;case 5:return n=Se.get(),I[n+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return o=28,M[xr()>>2]=o,-1}}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return-e.errno}var o}function Ie(e,t,r,i){if(_)return lr(5,1,e,t,r,i);Se.varargs=i;try{t=Se.getStr(t),t=Se.calculateAt(e,t);var n=i?Se.get():0;return we.open(t,r,n).fd}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return-e.errno}}function Le(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}t.invokeEntryPoint=function(e,t){var r=ke(e)(t);Z()?Ue.setExitStatus(r):Mr(r)};var Me=void 0;function Re(e){for(var t="",r=e;F[r];)t+=Me[F[r++]];return t}var Ne={},ze={},Oe={},Ge=48,$e=57;function He(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=Ge&&t<=$e?"_"+e:e}function Ve(e,t){return e=He(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function We(e,t){var r=Ve(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var je=void 0;function qe(e){throw new je(e)}var Ye=void 0;function Ke(e){throw new Ye(e)}function Xe(e,t,r){function i(t){var i=r(t);i.length!==e.length&&Ke("Mismatched type converter count");for(var n=0;n<e.length;++n)Ze(e[n],i[n])}e.forEach((function(e){Oe[e]=t}));var n=new Array(t.length),o=[],s=0;t.forEach(((e,t)=>{ze.hasOwnProperty(e)?n[t]=ze[e]:(o.push(e),Ne.hasOwnProperty(e)||(Ne[e]=[]),Ne[e].push((()=>{n[t]=ze[e],++s===o.length&&i(n)})))})),0===o.length&&i(n)}function Ze(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var i=t.name;if(e||qe('type "'+i+'" must have a positive integer typeid pointer'),ze.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;qe("Cannot register type '"+i+"' twice")}if(ze[e]=t,delete Oe[e],Ne.hasOwnProperty(e)){var n=Ne[e];delete Ne[e],n.forEach((e=>e()))}}function Je(e){if(!(this instanceof bt))return!1;if(!(e instanceof bt))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,i=e.$$.ptrType.registeredClass,n=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;i.baseClass;)n=i.upcast(n),i=i.baseClass;return t===i&&r===n}function Qe(e){qe(e.$$.ptrType.registeredClass.name+" instance already deleted")}var et=!1;function tt(e){}function rt(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function it(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var i=it(e,t,r.baseClass);return null===i?null:r.downcast(i)}var nt={};function ot(){return Object.keys(ct).length}function st(){var e=[];for(var t in ct)ct.hasOwnProperty(t)&&e.push(ct[t]);return e}var at=[];function dt(){for(;at.length;){var e=at.pop();e.$$.deleteScheduled=!1,e.delete()}}var lt=void 0;function ut(e){lt=e,at.length&&lt&&lt(dt)}var ct={};function ft(e,t){return t=function(e,t){for(void 0===t&&qe("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),ct[t]}function ht(e,t){return t.ptrType&&t.ptr||Ke("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&Ke("Both smartPtrType and smartPtr must be specified"),t.count={value:1},mt(Object.create(e,{$$:{value:t}}))}function pt(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=ft(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var i=r.clone();return this.destructor(e),i}function n(){return this.isSmartPointer?ht(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):ht(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var o,s=this.registeredClass.getActualType(t),a=nt[s];if(!a)return n.call(this);o=this.isConst?a.constPointerType:a.pointerType;var d=it(t,this.registeredClass,o.registeredClass);return null===d?n.call(this):this.isSmartPointer?ht(o.registeredClass.instancePrototype,{ptrType:o,ptr:d,smartPtrType:this,smartPtr:e}):ht(o.registeredClass.instancePrototype,{ptrType:o,ptr:d})}function mt(e){return"undefined"==typeof FinalizationRegistry?(mt=e=>e,e):(et=new FinalizationRegistry((e=>{rt(e.$$)})),mt=e=>{var t=e.$$;if(t.smartPtr){var r={$$:t};et.register(e,r,e)}return e},tt=e=>et.unregister(e),mt(e))}function _t(){if(this.$$.ptr||Qe(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=mt(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function gt(){this.$$.ptr||Qe(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&qe("Object already scheduled for deletion"),tt(this),rt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function yt(){return!this.$$.ptr}function vt(){return this.$$.ptr||Qe(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&qe("Object already scheduled for deletion"),at.push(this),1===at.length&&lt&&lt(dt),this.$$.deleteScheduled=!0,this}function bt(){}function wt(e,t,r){if(void 0===e[t].overloadTable){var i=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||qe("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[i.argCount]=i}}function St(e,t,r,i,n,o,s,a){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=i,this.baseClass=n,this.getActualType=o,this.upcast=s,this.downcast=a,this.pureVirtualFunctions=[]}function Et(e,t,r){for(;t!==r;)t.upcast||qe("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function At(e,t){if(null===t)return this.isReference&&qe("null is not a valid "+this.name),0;t.$$||qe('Cannot pass "'+qt(t)+'" as a '+this.name),t.$$.ptr||qe("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Et(t.$$.ptr,r,this.registeredClass)}function Ut(e,t){var r;if(null===t)return this.isReference&&qe("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||qe('Cannot pass "'+qt(t)+'" as a '+this.name),t.$$.ptr||qe("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&qe("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var i=t.$$.ptrType.registeredClass;if(r=Et(t.$$.ptr,i,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&qe("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:qe("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var n=t.clone();r=this.rawShare(r,jt.toHandle((function(){n.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:qe("Unsupporting sharing policy")}return r}function Tt(e,t){if(null===t)return this.isReference&&qe("null is not a valid "+this.name),0;t.$$||qe('Cannot pass "'+qt(t)+'" as a '+this.name),t.$$.ptr||qe("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&qe("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Et(t.$$.ptr,r,this.registeredClass)}function xt(e){return this.fromWireType(M[e>>2])}function Bt(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function kt(e){this.rawDestructor&&this.rawDestructor(e)}function Ct(e){null!==e&&e.delete()}function Dt(e,t,r,i,n,o,s,a,d,l,u){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=i,this.isSmartPointer=n,this.pointeeType=o,this.sharingPolicy=s,this.rawGetPointee=a,this.rawConstructor=d,this.rawShare=l,this.rawDestructor=u,n||void 0!==t.baseClass?this.toWireType=Ut:i?(this.toWireType=At,this.destructorFunction=null):(this.toWireType=Tt,this.destructorFunction=null)}function Pt(e,r,i){return e.includes("j")?function(e,r,i){var n=t["dynCall_"+e];return i&&i.length?n.apply(null,[r].concat(i)):n.call(null,r)}(e,r,i):ke(r).apply(null,i)}function Ft(e,t){var r,i,n,o=(e=Re(e)).includes("j")?(r=e,i=t,n=[],function(){return n.length=0,Object.assign(n,arguments),Pt(r,i,n)}):ke(t);return"function"!=typeof o&&qe("unknown function pointer with signature "+e+": "+t),o}var It=void 0;function Lt(e){var t=Cr(e),r=Re(t);return Ur(t),r}function Mt(e,t){var r=[],i={};throw t.forEach((function e(t){i[t]||ze[t]||(Oe[t]?Oe[t].forEach(e):(r.push(t),i[t]=!0))})),new It(e+": "+r.map(Lt).join([", "]))}function Rt(e,t){for(var r=[],i=0;i<e;i++)r.push(R[t+4*i>>2]);return r}function Nt(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function zt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=Ve(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var i=new r,n=e.apply(i,t);return n instanceof Object?n:i}function Ot(e,t,r,i,n){var o=t.length;o<2&&qe("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==t[1]&&null!==r,a=!1,d=1;d<t.length;++d)if(null!==t[d]&&void 0===t[d].destructorFunction){a=!0;break}var l="void"!==t[0].name,u="",c="";for(d=0;d<o-2;++d)u+=(0!==d?", ":"")+"arg"+d,c+=(0!==d?", ":"")+"arg"+d+"Wired";var f="return function "+He(e)+"("+u+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";a&&(f+="var destructors = [];\n");var h=a?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[qe,i,n,Nt,t[0],t[1]];for(s&&(f+="var thisWired = classParam.toWireType("+h+", this);\n"),d=0;d<o-2;++d)f+="var arg"+d+"Wired = argType"+d+".toWireType("+h+", arg"+d+"); // "+t[d+2].name+"\n",p.push("argType"+d),m.push(t[d+2]);if(s&&(c="thisWired"+(c.length>0?", ":"")+c),f+=(l?"var rv = ":"")+"invoker(fn"+(c.length>0?", ":"")+c+");\n",a)f+="runDestructors(destructors);\n";else for(d=s?1:2;d<t.length;++d){var _=1===d?"thisWired":"arg"+(d-2)+"Wired";null!==t[d].destructorFunction&&(f+=_+"_dtor("+_+"); // "+t[d].name+"\n",p.push(_+"_dtor"),m.push(t[d].destructorFunction))}return l&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",p.push(f),zt(Function,p).apply(null,m)}var Gt=[],$t=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Ht(e){e>4&&0==--$t[e].refcount&&($t[e]=void 0,Gt.push(e))}function Vt(){for(var e=0,t=5;t<$t.length;++t)void 0!==$t[t]&&++e;return e}function Wt(){for(var e=5;e<$t.length;++e)if(void 0!==$t[e])return $t[e];return null}var jt={toValue:e=>(e||qe("Cannot use deleted val. handle = "+e),$t[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=Gt.length?Gt.pop():$t.length;return $t[t]={refcount:1,value:e},t}}};function qt(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Yt(e,t){switch(t){case 2:return function(e){return this.fromWireType(N[e>>2])};case 3:return function(e){return this.fromWireType(z[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Kt(e,t,r){switch(t){case 0:return r?function(e){return P[e]}:function(e){return F[e]};case 1:return r?function(e){return I[e>>1]}:function(e){return L[e>>1]};case 2:return r?function(e){return M[e>>2]}:function(e){return R[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Xt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Zt(e,t){for(var r=e,i=r>>1,n=i+t/2;!(i>=n)&&L[i];)++i;if((r=i<<1)-e>32&&Xt)return Xt.decode(F.slice(e,r));for(var o="",s=0;!(s>=t/2);++s){var a=I[e+2*s>>1];if(0==a)break;o+=String.fromCharCode(a)}return o}function Jt(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var i=t,n=(r-=2)<2*e.length?r/2:e.length,o=0;o<n;++o){var s=e.charCodeAt(o);I[t>>1]=s,t+=2}return I[t>>1]=0,t-i}function Qt(e){return 2*e.length}function er(e,t){for(var r=0,i="";!(r>=t/4);){var n=M[e+4*r>>2];if(0==n)break;if(++r,n>=65536){var o=n-65536;i+=String.fromCharCode(55296|o>>10,56320|1023&o)}else i+=String.fromCharCode(n)}return i}function tr(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var i=t,n=i+r-4,o=0;o<e.length;++o){var s=e.charCodeAt(o);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++o)),M[t>>2]=s,(t+=4)+4>n)break}return M[t>>2]=0,t-i}function rr(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i>=55296&&i<=57343&&++r,t+=4}return t}function ir(e){Atomics.store(M,e>>2,1),Br()&&Ir(e),Atomics.compareExchange(M,e>>2,1,0)}t.executeNotifiedProxyingQueue=ir;var nr,or={},sr=[],ar=[];function dr(e){dr.shown||(dr.shown={}),dr.shown[e]||(dr.shown[e]=1,m&&(e="warning: "+e),S(e))}function lr(e,t){var r=arguments.length-2,i=arguments;return function(e){var t=Nr(),r=e();return zr(t),r}((function(){for(var n=r,o=Or(8*n),s=o>>3,a=0;a<r;a++){var d=i[2+a];z[s+a]=d}return Fr(e,n,o,t)}))}nr=m?()=>{var e=process.hrtime();return 1e3*e[0]+e[1]/1e6}:_?()=>performance.now()-t.__performance_now_clock_drift:()=>performance.now();var ur=[],cr={};function fr(){if(!fr.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var t in cr)void 0===cr[t]?delete e[t]:e[t]=cr[t];var r=[];for(var t in e)r.push(t+"="+e[t]);fr.strings=r}return fr.strings}function hr(e,t){if(_)return lr(6,1,e,t);var r=0;return fr().forEach((function(i,n){var o=t+r;R[e+4*n>>2]=o,function(e,t,r){for(var i=0;i<e.length;++i)P[t++>>0]=e.charCodeAt(i);r||(P[t>>0]=0)}(i,o),r+=i.length+1})),0}function pr(e,t){if(_)return lr(7,1,e,t);var r=fr();R[e>>2]=r.length;var i=0;return r.forEach((function(e){i+=e.length+1})),R[t>>2]=i,0}function mr(e){if(_)return lr(8,1,e);try{var t=Se.getStreamFromFD(e);return we.close(t),0}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return e.errno}}function _r(e,t){if(_)return lr(9,1,e,t);try{var r=Se.getStreamFromFD(e),i=r.tty?2:we.isDir(r.mode)?3:we.isLink(r.mode)?7:4;return P[t>>0]=i,0}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return e.errno}}function gr(e,t,r,i){if(_)return lr(10,1,e,t,r,i);try{var n=function(e,t,r,i){for(var n=0,o=0;o<r;o++){var s=R[t>>2],a=R[t+4>>2];t+=8;var d=we.read(e,P,s,a,i);if(d<0)return-1;if(n+=d,d<a)break}return n}(Se.getStreamFromFD(e),t,r);return M[i>>2]=n,0}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return e.errno}}function yr(e,t,r,i,n){if(_)return lr(11,1,e,t,r,i,n);try{var o=(d=r)+2097152>>>0<4194305-!!(a=t)?(a>>>0)+4294967296*d:NaN;if(isNaN(o))return 61;var s=Se.getStreamFromFD(e);return we.llseek(s,o,i),se=[s.position>>>0,(oe=s.position,+Math.abs(oe)>=1?oe>0?(0|Math.min(+Math.floor(oe/4294967296),4294967295))>>>0:~~+Math.ceil((oe-+(~~oe>>>0))/4294967296)>>>0:0)],M[n>>2]=se[0],M[n+4>>2]=se[1],s.getdents&&0===o&&0===i&&(s.getdents=null),0}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return e.errno}var a,d}function vr(e,t,r,i){if(_)return lr(12,1,e,t,r,i);try{var n=function(e,t,r,i){for(var n=0,o=0;o<r;o++){var s=R[t>>2],a=R[t+4>>2];t+=8;var d=we.write(e,P,s,a,i);if(d<0)return-1;n+=d}return n}(Se.getStreamFromFD(e),t,r);return R[i>>2]=n,0}catch(e){if(void 0===we||!(e instanceof we.ErrnoError))throw e;return e.errno}}Ue.init();var br=function(e,t,r,i){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=we.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=i},wr=365,Sr=146;Object.defineProperties(br.prototype,{read:{get:function(){return(this.mode&wr)===wr},set:function(e){e?this.mode|=wr:this.mode&=-366}},write:{get:function(){return(this.mode&Sr)===Sr},set:function(e){e?this.mode|=Sr:this.mode&=-147}},isFolder:{get:function(){return we.isDir(this.mode)}},isDevice:{get:function(){return we.isChrdev(this.mode)}}}),we.FSNode=br,we.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Me=e}(),je=t.BindingError=We(Error,"BindingError"),Ye=t.InternalError=We(Error,"InternalError"),bt.prototype.isAliasOf=Je,bt.prototype.clone=_t,bt.prototype.delete=gt,bt.prototype.isDeleted=yt,bt.prototype.deleteLater=vt,t.getInheritedInstanceCount=ot,t.getLiveInheritedInstances=st,t.flushPendingDeletes=dt,t.setDelayFunction=ut,Dt.prototype.getPointee=Bt,Dt.prototype.destructor=kt,Dt.prototype.argPackAdvance=8,Dt.prototype.readValueFromPointer=xt,Dt.prototype.deleteObject=Ct,Dt.prototype.fromWireType=pt,It=t.UnboundTypeError=We(Error,"UnboundTypeError"),t.count_emval_handles=Vt,t.get_first_emval=Wt;var Er=[null,Ee,xe,De,Fe,Ie,hr,pr,mr,_r,gr,yr,vr],Ar={t:function(e){return Tr(e+24)+24},s:function(e,t,r){throw new Ce(e).init(t,r),e},G:function(e){Dr(e,!p,1,!h),Ue.threadInitTLS()},j:function(e){_?postMessage({cmd:"cleanupThread",thread:e}):he(e)},C:Pe,Q:Fe,F:Ie,v:function(e,t,r,i,n){},S:function(e,t,r,i,n){var o=Le(r);Ze(e,{name:t=Re(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?i:n},argPackAdvance:8,readValueFromPointer:function(e){var i;if(1===r)i=P;else if(2===r)i=I;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);i=M}return this.fromWireType(i[e>>o])},destructorFunction:null})},H:function(e,r,i,n,o,s,a,d,l,u,c,f,h){c=Re(c),s=Ft(o,s),d&&(d=Ft(a,d)),u&&(u=Ft(l,u)),h=Ft(f,h);var p=He(c);!function(e,r,i){t.hasOwnProperty(e)?((void 0===i||void 0!==t[e].overloadTable&&void 0!==t[e].overloadTable[i])&&qe("Cannot register public name '"+e+"' twice"),wt(t,e,e),t.hasOwnProperty(i)&&qe("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),t[e].overloadTable[i]=r):(t[e]=r,void 0!==i&&(t[e].numArguments=i))}(p,(function(){Mt("Cannot construct "+c+" due to unbound types",[n])})),Xe([e,r,i],n?[n]:[],(function(r){var i,o;r=r[0],o=n?(i=r.registeredClass).instancePrototype:bt.prototype;var a=Ve(p,(function(){if(Object.getPrototypeOf(this)!==l)throw new je("Use 'new' to construct "+c);if(void 0===f.constructor_body)throw new je(c+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new je("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),l=Object.create(o,{constructor:{value:a}});a.prototype=l;var f=new St(c,a,l,h,i,s,d,u),m=new Dt(c,f,!0,!1,!1),_=new Dt(c+"*",f,!1,!1,!1),g=new Dt(c+" const*",f,!1,!0,!1);return nt[e]={pointerType:_,constPointerType:g},function(e,r,i){t.hasOwnProperty(e)||Ke("Replacing nonexistant public symbol"),void 0!==t[e].overloadTable&&void 0!==i?t[e].overloadTable[i]=r:(t[e]=r,t[e].argCount=i)}(p,a),[m,_,g]}))},y:function(e,t,r,i,n,o){C(t>0);var s=Rt(t,r);n=Ft(i,n),Xe([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new je("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{Mt("Cannot construct "+e.name+" due to unbound types",s)},Xe([],s,(function(i){return i.splice(1,0,null),e.registeredClass.constructor_body[t-1]=Ot(r,i,null,n,o),[]})),[]}))},f:function(e,t,r,i,n,o,s,a){var d=Rt(r,i);t=Re(t),o=Ft(n,o),Xe([],[e],(function(e){var i=(e=e[0]).name+"."+t;function n(){Mt("Cannot call "+i+" due to unbound types",d)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),a&&e.registeredClass.pureVirtualFunctions.push(t);var l=e.registeredClass.instancePrototype,u=l[t];return void 0===u||void 0===u.overloadTable&&u.className!==e.name&&u.argCount===r-2?(n.argCount=r-2,n.className=e.name,l[t]=n):(wt(l,t,i),l[t].overloadTable[r-2]=n),Xe([],d,(function(n){var a=Ot(i,n,e,o,s);return void 0===l[t].overloadTable?(a.argCount=r-2,l[t]=a):l[t].overloadTable[r-2]=a,[]})),[]}))},R:function(e,t){Ze(e,{name:t=Re(t),fromWireType:function(e){var t=jt.toValue(e);return Ht(e),t},toWireType:function(e,t){return jt.toHandle(t)},argPackAdvance:8,readValueFromPointer:xt,destructorFunction:null})},p:function(e,t,r){var i=Le(r);Ze(e,{name:t=Re(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:Yt(t,i),destructorFunction:null})},d:function(e,t,r,i,n){t=Re(t);var o=Le(r),s=e=>e;if(0===i){var a=32-8*r;s=e=>e<<a>>>a}var d=t.includes("unsigned");Ze(e,{name:t,fromWireType:s,toWireType:d?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:Kt(t,o,0!==i),destructorFunction:null})},c:function(e,t,r){var i=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function n(e){var t=R,r=t[e>>=2],n=t[e+1];return new i(D,n,r)}Ze(e,{name:r=Re(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})},o:function(e,t){var r="std::string"===(t=Re(t));Ze(e,{name:t,fromWireType:function(e){var t,i=R[e>>2],n=e+4;if(r)for(var o=n,s=0;s<=i;++s){var a=n+s;if(s==i||0==F[a]){var d=$(o,a-o);void 0===t?t=d:(t+=String.fromCharCode(0),t+=d),o=a+1}}else{var l=new Array(i);for(s=0;s<i;++s)l[s]=String.fromCharCode(F[n+s]);t=l.join("")}return Ur(e),t},toWireType:function(e,t){var i;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||qe("Cannot pass non-string to std::string"),i=r&&n?V(t):t.length;var o=Tr(4+i+1),s=o+4;if(R[o>>2]=i,r&&n)H(t,F,s,i+1);else if(n)for(var a=0;a<i;++a){var d=t.charCodeAt(a);d>255&&(Ur(s),qe("String has UTF-16 code units that do not fit in 8 bits")),F[s+a]=d}else for(a=0;a<i;++a)F[s+a]=t[a];return null!==e&&e.push(Ur,o),o},argPackAdvance:8,readValueFromPointer:xt,destructorFunction:function(e){Ur(e)}})},h:function(e,t,r){var i,n,o,s,a;r=Re(r),2===t?(i=Zt,n=Jt,s=Qt,o=()=>L,a=1):4===t&&(i=er,n=tr,s=rr,o=()=>R,a=2),Ze(e,{name:r,fromWireType:function(e){for(var r,n=R[e>>2],s=o(),d=e+4,l=0;l<=n;++l){var u=e+4+l*t;if(l==n||0==s[u>>a]){var c=i(d,u-d);void 0===r?r=c:(r+=String.fromCharCode(0),r+=c),d=u+t}}return Ur(e),r},toWireType:function(e,i){"string"!=typeof i&&qe("Cannot pass non-string to C++ string type "+r);var o=s(i),d=Tr(4+o+t);return R[d>>2]=o>>a,n(i,d+4,o+t),null!==e&&e.push(Ur,d),d},argPackAdvance:8,readValueFromPointer:xt,destructorFunction:function(e){Ur(e)}})},r:function(e,t){Ze(e,{isVoid:!0,name:t=Re(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},g:function(){return Date.now()},D:function(){return 2097152},E:function(e,t,r,i){if(e==t)setTimeout((()=>ir(i)));else if(_)postMessage({targetThread:e,cmd:"processProxyingQueue",queue:i});else{var n=Ue.pthreads[e];if(!n)return;n.postMessage({cmd:"processProxyingQueue",queue:i})}return 1},J:function(e,t,r){return-1},m:function(e,t,r,i){var n,o;(e=sr[e])(t=jt.toValue(t),r=void 0===(o=or[n=r])?Re(n):o,null,i)},i:Ht,q:function(e,t){var r=function(e,t){for(var r,i,n,o=new Array(e),s=0;s<e;++s)o[s]=(r=R[t+s*A>>2],i="parameter "+s,n=void 0,void 0===(n=ze[r])&&qe(i+" has unknown type "+Lt(r)),n);return o}(e,t),i=r[0],n=i.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",o=ar[n];if(void 0!==o)return o;for(var s=["retType"],a=[i],d="",l=0;l<e-1;++l)d+=(0!==l?", ":"")+"arg"+l,s.push("argType"+l),a.push(r[1+l]);var u="return function "+He("methodCaller_"+n)+"(handle, name, destructors, args) {\n",c=0;for(l=0;l<e-1;++l)u+="    var arg"+l+" = argType"+l+".readValueFromPointer(args"+(c?"+"+c:"")+");\n",c+=r[l+1].argPackAdvance;for(u+="    var rv = handle[name]("+d+");\n",l=0;l<e-1;++l)r[l+1].deleteObject&&(u+="    argType"+l+".deleteObject(arg"+l+");\n");i.isVoid||(u+="    return retType.toWireType(destructors, rv);\n"),u+="};\n",s.push(u);var f,h,p=zt(Function,s).apply(null,a);return f=p,h=sr.length,sr.push(f),o=h,ar[n]=o,o},b:function(){ie("")},k:function(){m||p||dr("Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread")},z:function(){return F.length},e:nr,N:function(e,t,r){F.copyWithin(e,t,t+r)},A:function(){return m?require("os").cpus().length:navigator.hardwareConcurrency},I:function(e,t,r){ur.length=t;for(var i=r>>3,n=0;n<t;n++)ur[n]=z[i+n];return(e<0?ce[-e-1]:Er[e]).apply(null,ur)},x:function(e){F.length,ie("OOM")},O:function(){throw"unwind"},L:hr,M:pr,B:Ae,n:mr,K:_r,P:gr,u:yr,l:vr,a:U||t.wasmMemory,w:function(e){}};!function(){var e={a:Ar};function r(e,r){var i,n,o=e.exports;t.asm=o,i=t.asm.Y,Ue.tlsInitFunctions.push(i),j=t.asm.W,n=t.asm.T,K.unshift(n),T=r,_||re()}function n(e){r(e.instance,e.module)}function s(t){return function(){if(!E&&(h||p)){if("function"==typeof fetch&&!le(ne))return fetch(ne,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+ne+"'";return e.arrayBuffer()})).catch((function(){return ue(ne)}));if(o)return new Promise((function(e,t){o(ne,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return ue(ne)}))}().then((function(t){return WebAssembly.instantiate(t,e)})).then((function(e){return e})).then(t,(function(e){S("failed to asynchronously prepare wasm: "+e),ie(e)}))}if(_||te(),t.instantiateWasm)try{return t.instantiateWasm(e,r)}catch(e){return S("Module.instantiateWasm callback failed with error: "+e),!1}(E||"function"!=typeof WebAssembly.instantiateStreaming||de(ne)||le(ne)||m||"function"!=typeof fetch?s(n):fetch(ne,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(n,(function(e){return S("wasm streaming compile failed: "+e),S("falling back to ArrayBuffer instantiation"),s(n)}))}))).catch(i)}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.T).apply(null,arguments)};var Ur=t._free=function(){return(Ur=t._free=t.asm.U).apply(null,arguments)},Tr=t._malloc=function(){return(Tr=t._malloc=t.asm.V).apply(null,arguments)},xr=t.___errno_location=function(){return(xr=t.___errno_location=t.asm.X).apply(null,arguments)};t.__emscripten_tls_init=function(){return(t.__emscripten_tls_init=t.asm.Y).apply(null,arguments)};var Br=t._pthread_self=function(){return(Br=t._pthread_self=t.asm.Z).apply(null,arguments)},kr=t._emscripten_builtin_memalign=function(){return(kr=t._emscripten_builtin_memalign=t.asm._).apply(null,arguments)},Cr=t.___getTypeName=function(){return(Cr=t.___getTypeName=t.asm.$).apply(null,arguments)};t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.aa).apply(null,arguments)};var Dr=t.__emscripten_thread_init=function(){return(Dr=t.__emscripten_thread_init=t.asm.ba).apply(null,arguments)};t.__emscripten_thread_crashed=function(){return(t.__emscripten_thread_crashed=t.asm.ca).apply(null,arguments)};var Pr,Fr=t._emscripten_run_in_main_runtime_thread_js=function(){return(Fr=t._emscripten_run_in_main_runtime_thread_js=t.asm.da).apply(null,arguments)},Ir=t.__emscripten_proxy_execute_task_queue=function(){return(Ir=t.__emscripten_proxy_execute_task_queue=t.asm.ea).apply(null,arguments)},Lr=t.__emscripten_thread_free_data=function(){return(Lr=t.__emscripten_thread_free_data=t.asm.fa).apply(null,arguments)},Mr=t.__emscripten_thread_exit=function(){return(Mr=t.__emscripten_thread_exit=t.asm.ga).apply(null,arguments)},Rr=t._emscripten_stack_set_limits=function(){return(Rr=t._emscripten_stack_set_limits=t.asm.ha).apply(null,arguments)},Nr=t.stackSave=function(){return(Nr=t.stackSave=t.asm.ia).apply(null,arguments)},zr=t.stackRestore=function(){return(zr=t.stackRestore=t.asm.ja).apply(null,arguments)},Or=t.stackAlloc=function(){return(Or=t.stackAlloc=t.asm.ka).apply(null,arguments)},Gr=t.___cxa_is_pointer_type=function(){return(Gr=t.___cxa_is_pointer_type=t.asm.la).apply(null,arguments)};function $r(e){if(!(Q>0)){if(_)return r(t),J(),void postMessage({cmd:"loaded"});!function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),Y.unshift(e);var e;Te(Y)}(),Q>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),i()}),1)):i())}function i(){Pr||(Pr=!0,t.calledRun=!0,k||(J(),r(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(!_){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),X.unshift(e);var e;Te(X)}}()))}}if(t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.ma).apply(null,arguments)},t._ff_h264_cabac_tables=111557,t.keepRuntimeAlive=Z,t.wasmMemory=U,t.ExitStatus=fe,t.PThread=Ue,ee=function e(){Pr||$r(),Pr||(ee=e)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return $r(),t.ready}),r=(()=>{var e="undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-mt.js",document.baseURI).href;return function(t){var r,i;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(e,t){r=e,i=t})),(t=void 0!==t?t:{}).locateFile=function(e){return"decoder-pro-audio.wasm"==e&&"undefined"!=typeof EASYPLAYER_PRO_AUDIO_WASM_URL&&""!=EASYPLAYER_PRO_AUDIO_WASM_URL?EASYPLAYER_PRO_AUDIO_WASM_URL:e};var n,o,s,a,d,l,u=Object.assign({},t),c="./this.program",f="object"==typeof window,h="function"==typeof importScripts,p="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,m="";p?(m=h?require("path").dirname(m)+"/":__dirname+"/",l=()=>{d||(a=require("fs"),d=require("path"))},n=function(e,t){return l(),e=d.normalize(e),a.readFileSync(e,t?void 0:"utf8")},s=e=>{var t=n(e,!0);return t.buffer||(t=new Uint8Array(t)),t},o=(e,t,r)=>{l(),e=d.normalize(e),a.readFile(e,(function(e,i){e?r(e):t(i.buffer)}))},process.argv.length>1&&(c=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof ee))throw e})),process.on("unhandledRejection",(function(e){throw e})),t.inspect=function(){return"[Emscripten Module object]"}):(f||h)&&(h?m=self.location.href:"undefined"!=typeof document&&document.currentScript&&(m=document.currentScript.src),e&&(m=e),m=0!==m.indexOf("blob:")?m.substr(0,m.replace(/[?#].*/,"").lastIndexOf("/")+1):"",n=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},h&&(s=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),o=(e,t,r)=>{var i=new XMLHttpRequest;i.open("GET",e,!0),i.responseType="arraybuffer",i.onload=()=>{200==i.status||0==i.status&&i.response?t(i.response):r()},i.onerror=r,i.send(null)});var _=t.print||console.log.bind(console),g=t.printErr||console.warn.bind(console);Object.assign(t,u),u=null,t.arguments&&t.arguments,t.thisProgram&&(c=t.thisProgram),t.quit&&t.quit;var y,v,b=4;t.wasmBinary&&(y=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&W("no native wasm support detected");var w=!1;function S(e,t){e||W(t)}var E,A,U,T,x,B,k,C,D,P,F="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function I(e,t,r){for(var i=t+r,n=t;e[n]&&!(n>=i);)++n;if(n-t>16&&e.buffer&&F)return F.decode(e.subarray(t,n));for(var o="";t<n;){var s=e[t++];if(128&s){var a=63&e[t++];if(192!=(224&s)){var d=63&e[t++];if((s=224==(240&s)?(15&s)<<12|a<<6|d:(7&s)<<18|a<<12|d<<6|63&e[t++])<65536)o+=String.fromCharCode(s);else{var l=s-65536;o+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else o+=String.fromCharCode((31&s)<<6|a)}else o+=String.fromCharCode(s)}return o}function L(e,t){return e?I(U,e,t):""}function M(e,t,r,i){if(!(i>0))return 0;for(var n=r,o=r+i-1,s=0;s<e.length;++s){var a=e.charCodeAt(s);if(a>=55296&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++s);if(a<=127){if(r>=o)break;t[r++]=a}else if(a<=2047){if(r+1>=o)break;t[r++]=192|a>>6,t[r++]=128|63&a}else if(a<=65535){if(r+2>=o)break;t[r++]=224|a>>12,t[r++]=128|a>>6&63,t[r++]=128|63&a}else{if(r+3>=o)break;t[r++]=240|a>>18,t[r++]=128|a>>12&63,t[r++]=128|a>>6&63,t[r++]=128|63&a}}return t[r]=0,r-n}function R(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i<=127?t++:i<=2047?t+=2:i>=55296&&i<=57343?(t+=4,++r):t+=3}return t}t.INITIAL_MEMORY;var N=[],z=[],O=[];var G=0,$=null;function H(e){G++,t.monitorRunDependencies&&t.monitorRunDependencies(G)}function V(e){if(G--,t.monitorRunDependencies&&t.monitorRunDependencies(G),0==G&&$){var r=$;$=null,r()}}function W(e){t.onAbort&&t.onAbort(e),g(e="Aborted("+e+")"),w=!0,e+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(e);throw i(r),r}var j,q,Y,K,X="data:application/octet-stream;base64,";function Z(e){return e.startsWith(X)}function J(e){return e.startsWith("file://")}function Q(e){try{if(e==j&&y)return new Uint8Array(y);if(s)return s(e);throw"both async and sync fetching of the wasm failed"}catch(e){W(e)}}function ee(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function te(e){for(;e.length>0;)e.shift()(t)}function re(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){k[this.ptr+4>>2]=e},this.get_type=function(){return k[this.ptr+4>>2]},this.set_destructor=function(e){k[this.ptr+8>>2]=e},this.get_destructor=function(){return k[this.ptr+8>>2]},this.set_refcount=function(e){B[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,A[this.ptr+12>>0]=e},this.get_caught=function(){return 0!=A[this.ptr+12>>0]},this.set_rethrown=function(e){e=e?1:0,A[this.ptr+13>>0]=e},this.get_rethrown=function(){return 0!=A[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=B[this.ptr>>2];B[this.ptr>>2]=e+1},this.release_ref=function(){var e=B[this.ptr>>2];return B[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){k[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return k[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Xt(this.get_type()))return k[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}t.locateFile?Z(j="decoder-pro-audio.wasm")||(q=j,j=t.locateFile?t.locateFile(q,m):m+q):j=new URL("decoder-pro-audio.wasm","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-mt.js",document.baseURI).href).toString();var ie={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,i=e.length-1;i>=0;i--){var n=e[i];"."===n?e.splice(i,1):".."===n?(e.splice(i,1),r++):r&&(e.splice(i,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=ie.isAbs(e),r="/"===e.substr(-1);return(e=ie.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=ie.splitPath(e),r=t[0],i=t[1];return r||i?(i&&(i=i.substr(0,i.length-1)),r+i):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=ie.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return ie.normalize(e.join("/"))},join2:(e,t)=>ie.normalize(e+"/"+t)};var ne={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var i=r>=0?arguments[r]:le.cwd();if("string"!=typeof i)throw new TypeError("Arguments to path.resolve must be strings");if(!i)return"";e=i+"/"+e,t=ie.isAbs(i)}return(t?"/":"")+(e=ie.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=ne.resolve(e).substr(1),t=ne.resolve(t).substr(1);for(var i=r(e.split("/")),n=r(t.split("/")),o=Math.min(i.length,n.length),s=o,a=0;a<o;a++)if(i[a]!==n[a]){s=a;break}var d=[];for(a=s;a<i.length;a++)d.push("..");return(d=d.concat(n.slice(s))).join("/")}};function oe(e,t,r){var i=r>0?r:R(e)+1,n=new Array(i),o=M(e,n,0,n.length);return t&&(n.length=o),n}var se={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){se.ttys[e]={input:[],output:[],ops:t},le.registerDevice(e,se.stream_ops)},stream_ops:{open:function(e){var t=se.ttys[e.node.rdev];if(!t)throw new le.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.get_char)throw new le.ErrnoError(60);for(var o=0,s=0;s<i;s++){var a;try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new le.ErrnoError(29)}if(void 0===a&&0===o)throw new le.ErrnoError(6);if(null==a)break;o++,t[r+s]=a}return o&&(e.node.timestamp=Date.now()),o},write:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.put_char)throw new le.ErrnoError(60);try{for(var o=0;o<i;o++)e.tty.ops.put_char(e.tty,t[r+o])}catch(e){throw new le.ErrnoError(29)}return i&&(e.node.timestamp=Date.now()),o}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(p){var r=Buffer.alloc(256),i=0;try{i=a.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;i=0}t=i>0?r.slice(0,i).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=oe(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(_(I(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(_(I(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(g(I(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(g(I(e.output,0)),e.output=[])}}};function ae(e){e=function(e,t){return Math.ceil(e/t)*t}(e,65536);var t=Kt(65536,e);return t?(function(e,t){U.fill(0,e,e+t)}(t,e),t):0}var de={ops_table:null,mount:function(e){return de.createNode(null,"/",16895,0)},createNode:function(e,t,r,i){if(le.isBlkdev(r)||le.isFIFO(r))throw new le.ErrnoError(63);de.ops_table||(de.ops_table={dir:{node:{getattr:de.node_ops.getattr,setattr:de.node_ops.setattr,lookup:de.node_ops.lookup,mknod:de.node_ops.mknod,rename:de.node_ops.rename,unlink:de.node_ops.unlink,rmdir:de.node_ops.rmdir,readdir:de.node_ops.readdir,symlink:de.node_ops.symlink},stream:{llseek:de.stream_ops.llseek}},file:{node:{getattr:de.node_ops.getattr,setattr:de.node_ops.setattr},stream:{llseek:de.stream_ops.llseek,read:de.stream_ops.read,write:de.stream_ops.write,allocate:de.stream_ops.allocate,mmap:de.stream_ops.mmap,msync:de.stream_ops.msync}},link:{node:{getattr:de.node_ops.getattr,setattr:de.node_ops.setattr,readlink:de.node_ops.readlink},stream:{}},chrdev:{node:{getattr:de.node_ops.getattr,setattr:de.node_ops.setattr},stream:le.chrdev_stream_ops}});var n=le.createNode(e,t,r,i);return le.isDir(n.mode)?(n.node_ops=de.ops_table.dir.node,n.stream_ops=de.ops_table.dir.stream,n.contents={}):le.isFile(n.mode)?(n.node_ops=de.ops_table.file.node,n.stream_ops=de.ops_table.file.stream,n.usedBytes=0,n.contents=null):le.isLink(n.mode)?(n.node_ops=de.ops_table.link.node,n.stream_ops=de.ops_table.link.stream):le.isChrdev(n.mode)&&(n.node_ops=de.ops_table.chrdev.node,n.stream_ops=de.ops_table.chrdev.stream),n.timestamp=Date.now(),e&&(e.contents[t]=n,e.timestamp=n.timestamp),n},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var i=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(i.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=le.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,le.isDir(e.mode)?t.size=4096:le.isFile(e.mode)?t.size=e.usedBytes:le.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&de.resizeFileStorage(e,t.size)},lookup:function(e,t){throw le.genericErrors[44]},mknod:function(e,t,r,i){return de.createNode(e,t,r,i)},rename:function(e,t,r){if(le.isDir(e.mode)){var i;try{i=le.lookupNode(t,r)}catch(e){}if(i)for(var n in i.contents)throw new le.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=le.lookupNode(e,t);for(var i in r.contents)throw new le.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var i=de.createNode(e,t,41471,0);return i.link=r,i},readlink:function(e){if(!le.isLink(e.mode))throw new le.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,i,n){var o=e.node.contents;if(n>=e.node.usedBytes)return 0;var s=Math.min(e.node.usedBytes-n,i);if(s>8&&o.subarray)t.set(o.subarray(n,n+s),r);else for(var a=0;a<s;a++)t[r+a]=o[n+a];return s},write:function(e,t,r,i,n,o){if(!i)return 0;var s=e.node;if(s.timestamp=Date.now(),t.subarray&&(!s.contents||s.contents.subarray)){if(o)return s.contents=t.subarray(r,r+i),s.usedBytes=i,i;if(0===s.usedBytes&&0===n)return s.contents=t.slice(r,r+i),s.usedBytes=i,i;if(n+i<=s.usedBytes)return s.contents.set(t.subarray(r,r+i),n),i}if(de.expandFileStorage(s,n+i),s.contents.subarray&&t.subarray)s.contents.set(t.subarray(r,r+i),n);else for(var a=0;a<i;a++)s.contents[n+a]=t[r+a];return s.usedBytes=Math.max(s.usedBytes,n+i),i},llseek:function(e,t,r){var i=t;if(1===r?i+=e.position:2===r&&le.isFile(e.node.mode)&&(i+=e.node.usedBytes),i<0)throw new le.ErrnoError(28);return i},allocate:function(e,t,r){de.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,i,n){if(!le.isFile(e.node.mode))throw new le.ErrnoError(43);var o,s,a=e.node.contents;if(2&n||a.buffer!==E){if((r>0||r+t<a.length)&&(a=a.subarray?a.subarray(r,r+t):Array.prototype.slice.call(a,r,r+t)),s=!0,!(o=ae(t)))throw new le.ErrnoError(48);A.set(a,o)}else s=!1,o=a.byteOffset;return{ptr:o,allocated:s}},msync:function(e,t,r,i,n){if(!le.isFile(e.node.mode))throw new le.ErrnoError(43);return 2&n||de.stream_ops.write(e,t,0,i,r,!1),0}}};var le={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(e=ne.resolve(le.cwd(),e)))return{path:"",node:null};if(t=Object.assign({follow_mount:!0,recurse_count:0},t),t.recurse_count>8)throw new le.ErrnoError(32);for(var r=ie.normalizeArray(e.split("/").filter((e=>!!e)),!1),i=le.root,n="/",o=0;o<r.length;o++){var s=o===r.length-1;if(s&&t.parent)break;if(i=le.lookupNode(i,r[o]),n=ie.join2(n,r[o]),le.isMountpoint(i)&&(!s||s&&t.follow_mount)&&(i=i.mounted.root),!s||t.follow)for(var a=0;le.isLink(i.mode);){var d=le.readlink(n);if(n=ne.resolve(ie.dirname(n),d),i=le.lookupPath(n,{recurse_count:t.recurse_count+1}).node,a++>40)throw new le.ErrnoError(32)}}return{path:n,node:i}},getPath:e=>{for(var t;;){if(le.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,i=0;i<t.length;i++)r=(r<<5)-r+t.charCodeAt(i)|0;return(e+r>>>0)%le.nameTable.length},hashAddNode:e=>{var t=le.hashName(e.parent.id,e.name);e.name_next=le.nameTable[t],le.nameTable[t]=e},hashRemoveNode:e=>{var t=le.hashName(e.parent.id,e.name);if(le.nameTable[t]===e)le.nameTable[t]=e.name_next;else for(var r=le.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=le.mayLookup(e);if(r)throw new le.ErrnoError(r,e);for(var i=le.hashName(e.id,t),n=le.nameTable[i];n;n=n.name_next){var o=n.name;if(n.parent.id===e.id&&o===t)return n}return le.lookup(e,t)},createNode:(e,t,r,i)=>{var n=new le.FSNode(e,t,r,i);return le.hashAddNode(n),n},destroyNode:e=>{le.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=le.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>le.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=le.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{le.lookupNode(e,t);return 20}catch(e){}return le.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var i;try{i=le.lookupNode(e,t)}catch(e){return e.errno}var n=le.nodePermissions(e,"wx");if(n)return n;if(r){if(!le.isDir(i.mode))return 54;if(le.isRoot(i)||le.getPath(i)===le.cwd())return 10}else if(le.isDir(i.mode))return 31;return 0},mayOpen:(e,t)=>e?le.isLink(e.mode)?32:le.isDir(e.mode)&&("r"!==le.flagsToPermissionString(t)||512&t)?31:le.nodePermissions(e,le.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:le.MAX_OPEN_FDS;for(var r=e;r<=t;r++)if(!le.streams[r])return r;throw new le.ErrnoError(33)},getStream:e=>le.streams[e],createStream:(e,t,r)=>{le.FSStream||(le.FSStream=function(){this.shared={}},le.FSStream.prototype={},Object.defineProperties(le.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new le.FSStream,e);var i=le.nextfd(t,r);return e.fd=i,le.streams[i]=e,e},closeStream:e=>{le.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=le.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new le.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{le.devices[e]={stream_ops:t}},getDevice:e=>le.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var i=r.pop();t.push(i),r.push.apply(r,i.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),le.syncFSRequests++,le.syncFSRequests>1&&g("warning: "+le.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=le.getMounts(le.root.mount),i=0;function n(e){return le.syncFSRequests--,t(e)}function o(e){if(e)return o.errored?void 0:(o.errored=!0,n(e));++i>=r.length&&n(null)}r.forEach((t=>{if(!t.type.syncfs)return o(null);t.type.syncfs(t,e,o)}))},mount:(e,t,r)=>{var i,n="/"===r,o=!r;if(n&&le.root)throw new le.ErrnoError(10);if(!n&&!o){var s=le.lookupPath(r,{follow_mount:!1});if(r=s.path,i=s.node,le.isMountpoint(i))throw new le.ErrnoError(10);if(!le.isDir(i.mode))throw new le.ErrnoError(54)}var a={type:e,opts:t,mountpoint:r,mounts:[]},d=e.mount(a);return d.mount=a,a.root=d,n?le.root=d:i&&(i.mounted=a,i.mount&&i.mount.mounts.push(a)),d},unmount:e=>{var t=le.lookupPath(e,{follow_mount:!1});if(!le.isMountpoint(t.node))throw new le.ErrnoError(28);var r=t.node,i=r.mounted,n=le.getMounts(i);Object.keys(le.nameTable).forEach((e=>{for(var t=le.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&le.destroyNode(t),t=r}})),r.mounted=null;var o=r.mount.mounts.indexOf(i);r.mount.mounts.splice(o,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var i=le.lookupPath(e,{parent:!0}).node,n=ie.basename(e);if(!n||"."===n||".."===n)throw new le.ErrnoError(28);var o=le.mayCreate(i,n);if(o)throw new le.ErrnoError(o);if(!i.node_ops.mknod)throw new le.ErrnoError(63);return i.node_ops.mknod(i,n,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,le.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,le.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),i="",n=0;n<r.length;++n)if(r[n]){i+="/"+r[n];try{le.mkdir(i,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,le.mknod(e,t,r)),symlink:(e,t)=>{if(!ne.resolve(e))throw new le.ErrnoError(44);var r=le.lookupPath(t,{parent:!0}).node;if(!r)throw new le.ErrnoError(44);var i=ie.basename(t),n=le.mayCreate(r,i);if(n)throw new le.ErrnoError(n);if(!r.node_ops.symlink)throw new le.ErrnoError(63);return r.node_ops.symlink(r,i,e)},rename:(e,t)=>{var r,i,n=ie.dirname(e),o=ie.dirname(t),s=ie.basename(e),a=ie.basename(t);if(r=le.lookupPath(e,{parent:!0}).node,i=le.lookupPath(t,{parent:!0}).node,!r||!i)throw new le.ErrnoError(44);if(r.mount!==i.mount)throw new le.ErrnoError(75);var d,l=le.lookupNode(r,s),u=ne.relative(e,o);if("."!==u.charAt(0))throw new le.ErrnoError(28);if("."!==(u=ne.relative(t,n)).charAt(0))throw new le.ErrnoError(55);try{d=le.lookupNode(i,a)}catch(e){}if(l!==d){var c=le.isDir(l.mode),f=le.mayDelete(r,s,c);if(f)throw new le.ErrnoError(f);if(f=d?le.mayDelete(i,a,c):le.mayCreate(i,a))throw new le.ErrnoError(f);if(!r.node_ops.rename)throw new le.ErrnoError(63);if(le.isMountpoint(l)||d&&le.isMountpoint(d))throw new le.ErrnoError(10);if(i!==r&&(f=le.nodePermissions(r,"w")))throw new le.ErrnoError(f);le.hashRemoveNode(l);try{r.node_ops.rename(l,i,a)}catch(e){throw e}finally{le.hashAddNode(l)}}},rmdir:e=>{var t=le.lookupPath(e,{parent:!0}).node,r=ie.basename(e),i=le.lookupNode(t,r),n=le.mayDelete(t,r,!0);if(n)throw new le.ErrnoError(n);if(!t.node_ops.rmdir)throw new le.ErrnoError(63);if(le.isMountpoint(i))throw new le.ErrnoError(10);t.node_ops.rmdir(t,r),le.destroyNode(i)},readdir:e=>{var t=le.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new le.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=le.lookupPath(e,{parent:!0}).node;if(!t)throw new le.ErrnoError(44);var r=ie.basename(e),i=le.lookupNode(t,r),n=le.mayDelete(t,r,!1);if(n)throw new le.ErrnoError(n);if(!t.node_ops.unlink)throw new le.ErrnoError(63);if(le.isMountpoint(i))throw new le.ErrnoError(10);t.node_ops.unlink(t,r),le.destroyNode(i)},readlink:e=>{var t=le.lookupPath(e).node;if(!t)throw new le.ErrnoError(44);if(!t.node_ops.readlink)throw new le.ErrnoError(28);return ne.resolve(le.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=le.lookupPath(e,{follow:!t}).node;if(!r)throw new le.ErrnoError(44);if(!r.node_ops.getattr)throw new le.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>le.stat(e,!0),chmod:(e,t,r)=>{var i;"string"==typeof e?i=le.lookupPath(e,{follow:!r}).node:i=e;if(!i.node_ops.setattr)throw new le.ErrnoError(63);i.node_ops.setattr(i,{mode:4095&t|-4096&i.mode,timestamp:Date.now()})},lchmod:(e,t)=>{le.chmod(e,t,!0)},fchmod:(e,t)=>{var r=le.getStream(e);if(!r)throw new le.ErrnoError(8);le.chmod(r.node,t)},chown:(e,t,r,i)=>{var n;"string"==typeof e?n=le.lookupPath(e,{follow:!i}).node:n=e;if(!n.node_ops.setattr)throw new le.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{le.chown(e,t,r,!0)},fchown:(e,t,r)=>{var i=le.getStream(e);if(!i)throw new le.ErrnoError(8);le.chown(i.node,t,r)},truncate:(e,t)=>{if(t<0)throw new le.ErrnoError(28);var r;"string"==typeof e?r=le.lookupPath(e,{follow:!0}).node:r=e;if(!r.node_ops.setattr)throw new le.ErrnoError(63);if(le.isDir(r.mode))throw new le.ErrnoError(31);if(!le.isFile(r.mode))throw new le.ErrnoError(28);var i=le.nodePermissions(r,"w");if(i)throw new le.ErrnoError(i);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=le.getStream(e);if(!r)throw new le.ErrnoError(8);if(0==(2097155&r.flags))throw new le.ErrnoError(28);le.truncate(r.node,t)},utime:(e,t,r)=>{var i=le.lookupPath(e,{follow:!0}).node;i.node_ops.setattr(i,{timestamp:Math.max(t,r)})},open:(e,r,i)=>{if(""===e)throw new le.ErrnoError(44);var n;if(i=void 0===i?438:i,i=64&(r="string"==typeof r?le.modeStringToFlags(r):r)?4095&i|32768:0,"object"==typeof e)n=e;else{e=ie.normalize(e);try{n=le.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var o=!1;if(64&r)if(n){if(128&r)throw new le.ErrnoError(20)}else n=le.mknod(e,i,0),o=!0;if(!n)throw new le.ErrnoError(44);if(le.isChrdev(n.mode)&&(r&=-513),65536&r&&!le.isDir(n.mode))throw new le.ErrnoError(54);if(!o){var s=le.mayOpen(n,r);if(s)throw new le.ErrnoError(s)}512&r&&!o&&le.truncate(n,0),r&=-131713;var a=le.createStream({node:n,path:le.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return a.stream_ops.open&&a.stream_ops.open(a),!t.logReadFiles||1&r||(le.readFiles||(le.readFiles={}),e in le.readFiles||(le.readFiles[e]=1)),a},close:e=>{if(le.isClosed(e))throw new le.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{le.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(le.isClosed(e))throw new le.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new le.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new le.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,i,n)=>{if(i<0||n<0)throw new le.ErrnoError(28);if(le.isClosed(e))throw new le.ErrnoError(8);if(1==(2097155&e.flags))throw new le.ErrnoError(8);if(le.isDir(e.node.mode))throw new le.ErrnoError(31);if(!e.stream_ops.read)throw new le.ErrnoError(28);var o=void 0!==n;if(o){if(!e.seekable)throw new le.ErrnoError(70)}else n=e.position;var s=e.stream_ops.read(e,t,r,i,n);return o||(e.position+=s),s},write:(e,t,r,i,n,o)=>{if(i<0||n<0)throw new le.ErrnoError(28);if(le.isClosed(e))throw new le.ErrnoError(8);if(0==(2097155&e.flags))throw new le.ErrnoError(8);if(le.isDir(e.node.mode))throw new le.ErrnoError(31);if(!e.stream_ops.write)throw new le.ErrnoError(28);e.seekable&&1024&e.flags&&le.llseek(e,0,2);var s=void 0!==n;if(s){if(!e.seekable)throw new le.ErrnoError(70)}else n=e.position;var a=e.stream_ops.write(e,t,r,i,n,o);return s||(e.position+=a),a},allocate:(e,t,r)=>{if(le.isClosed(e))throw new le.ErrnoError(8);if(t<0||r<=0)throw new le.ErrnoError(28);if(0==(2097155&e.flags))throw new le.ErrnoError(8);if(!le.isFile(e.node.mode)&&!le.isDir(e.node.mode))throw new le.ErrnoError(43);if(!e.stream_ops.allocate)throw new le.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,i,n)=>{if(0!=(2&i)&&0==(2&n)&&2!=(2097155&e.flags))throw new le.ErrnoError(2);if(1==(2097155&e.flags))throw new le.ErrnoError(2);if(!e.stream_ops.mmap)throw new le.ErrnoError(43);return e.stream_ops.mmap(e,t,r,i,n)},msync:(e,t,r,i,n)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,i,n):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new le.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,i=le.open(e,t.flags),n=le.stat(e).size,o=new Uint8Array(n);return le.read(i,o,0,n,0),"utf8"===t.encoding?r=I(o,0):"binary"===t.encoding&&(r=o),le.close(i),r},writeFile:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r.flags=r.flags||577;var i=le.open(e,r.flags,r.mode);if("string"==typeof t){var n=new Uint8Array(R(t)+1),o=M(t,n,0,n.length);le.write(i,n,0,o,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");le.write(i,t,0,t.byteLength,void 0,r.canOwn)}le.close(i)},cwd:()=>le.currentPath,chdir:e=>{var t=le.lookupPath(e,{follow:!0});if(null===t.node)throw new le.ErrnoError(44);if(!le.isDir(t.node.mode))throw new le.ErrnoError(54);var r=le.nodePermissions(t.node,"x");if(r)throw new le.ErrnoError(r);le.currentPath=t.path},createDefaultDirectories:()=>{le.mkdir("/tmp"),le.mkdir("/home"),le.mkdir("/home/<USER>")},createDefaultDevices:()=>{le.mkdir("/dev"),le.registerDevice(le.makedev(1,3),{read:()=>0,write:(e,t,r,i,n)=>i}),le.mkdev("/dev/null",le.makedev(1,3)),se.register(le.makedev(5,0),se.default_tty_ops),se.register(le.makedev(6,0),se.default_tty1_ops),le.mkdev("/dev/tty",le.makedev(5,0)),le.mkdev("/dev/tty1",le.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}if(p)try{var t=require("crypto");return()=>t.randomBytes(1)[0]}catch(e){}return()=>W("randomDevice")}();le.createDevice("/dev","random",e),le.createDevice("/dev","urandom",e),le.mkdir("/dev/shm"),le.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{le.mkdir("/proc");var e=le.mkdir("/proc/self");le.mkdir("/proc/self/fd"),le.mount({mount:()=>{var t=le.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,i=le.getStream(r);if(!i)throw new le.ErrnoError(8);var n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>i.path}};return n.parent=n,n}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{t.stdin?le.createDevice("/dev","stdin",t.stdin):le.symlink("/dev/tty","/dev/stdin"),t.stdout?le.createDevice("/dev","stdout",null,t.stdout):le.symlink("/dev/tty","/dev/stdout"),t.stderr?le.createDevice("/dev","stderr",null,t.stderr):le.symlink("/dev/tty1","/dev/stderr"),le.open("/dev/stdin",0),le.open("/dev/stdout",1),le.open("/dev/stderr",1)},ensureErrnoError:()=>{le.ErrnoError||(le.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},le.ErrnoError.prototype=new Error,le.ErrnoError.prototype.constructor=le.ErrnoError,[44].forEach((e=>{le.genericErrors[e]=new le.ErrnoError(e),le.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{le.ensureErrnoError(),le.nameTable=new Array(4096),le.mount(de,{},"/"),le.createDefaultDirectories(),le.createDefaultDevices(),le.createSpecialDirectories(),le.filesystems={MEMFS:de}},init:(e,r,i)=>{le.init.initialized=!0,le.ensureErrnoError(),t.stdin=e||t.stdin,t.stdout=r||t.stdout,t.stderr=i||t.stderr,le.createStandardStreams()},quit:()=>{le.init.initialized=!1;for(var e=0;e<le.streams.length;e++){var t=le.streams[e];t&&le.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=le.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(i=le.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var i=le.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=i.path,r.parentObject=i.node,r.name=ie.basename(e),i=le.lookupPath(e,{follow:!t}),r.exists=!0,r.path=i.path,r.object=i.node,r.name=i.node.name,r.isRoot="/"===i.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,i)=>{e="string"==typeof e?e:le.getPath(e);for(var n=t.split("/").reverse();n.length;){var o=n.pop();if(o){var s=ie.join2(e,o);try{le.mkdir(s)}catch(e){}e=s}}return s},createFile:(e,t,r,i,n)=>{var o=ie.join2("string"==typeof e?e:le.getPath(e),t),s=le.getMode(i,n);return le.create(o,s)},createDataFile:(e,t,r,i,n,o)=>{var s=t;e&&(e="string"==typeof e?e:le.getPath(e),s=t?ie.join2(e,t):e);var a=le.getMode(i,n),d=le.create(s,a);if(r){if("string"==typeof r){for(var l=new Array(r.length),u=0,c=r.length;u<c;++u)l[u]=r.charCodeAt(u);r=l}le.chmod(d,146|a);var f=le.open(d,577);le.write(f,r,0,r.length,0,o),le.close(f),le.chmod(d,a)}return d},createDevice:(e,t,r,i)=>{var n=ie.join2("string"==typeof e?e:le.getPath(e),t),o=le.getMode(!!r,!!i);le.createDevice.major||(le.createDevice.major=64);var s=le.makedev(le.createDevice.major++,0);return le.registerDevice(s,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,i,n,o)=>{for(var s=0,a=0;a<n;a++){var d;try{d=r()}catch(e){throw new le.ErrnoError(29)}if(void 0===d&&0===s)throw new le.ErrnoError(6);if(null==d)break;s++,t[i+a]=d}return s&&(e.node.timestamp=Date.now()),s},write:(e,t,r,n,o)=>{for(var s=0;s<n;s++)try{i(t[r+s])}catch(e){throw new le.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),s}}),le.mkdev(n,o,s)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!n)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=oe(n(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new le.ErrnoError(29)}},createLazyFile:(e,t,r,i,n)=>{function o(){this.lengthKnown=!1,this.chunks=[]}if(o.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},o.prototype.setDataGetter=function(e){this.getter=e},o.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,i=Number(e.getResponseHeader("Content-length")),n=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,o=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,s=1048576;n||(s=i);var a=this;a.setDataGetter((e=>{var t=e*s,n=(e+1)*s-1;if(n=Math.min(n,i-1),void 0===a.chunks[e]&&(a.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>i-1)throw new Error("only "+i+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",r,!1),i!==s&&n.setRequestHeader("Range","bytes="+e+"-"+t),n.responseType="arraybuffer",n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(n.status>=200&&n.status<300||304===n.status))throw new Error("Couldn't load "+r+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):oe(n.responseText||"",!0)})(t,n)),void 0===a.chunks[e])throw new Error("doXHR failed!");return a.chunks[e]})),!o&&i||(s=i=1,i=this.getter(0).length,s=i,_("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=i,this._chunkSize=s,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!h)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var s=new o;Object.defineProperties(s,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var a={isDevice:!1,contents:s}}else a={isDevice:!1,url:r};var d=le.createFile(e,t,a,i,n);a.contents?d.contents=a.contents:a.url&&(d.contents=null,d.url=a.url),Object.defineProperties(d,{usedBytes:{get:function(){return this.contents.length}}});var l={};function u(e,t,r,i,n){var o=e.node.contents;if(n>=o.length)return 0;var s=Math.min(o.length-n,i);if(o.slice)for(var a=0;a<s;a++)t[r+a]=o[n+a];else for(a=0;a<s;a++)t[r+a]=o.get(n+a);return s}return Object.keys(d.stream_ops).forEach((e=>{var t=d.stream_ops[e];l[e]=function(){return le.forceLoadFile(d),t.apply(null,arguments)}})),l.read=(e,t,r,i,n)=>(le.forceLoadFile(d),u(e,t,r,i,n)),l.mmap=(e,t,r,i,n)=>{le.forceLoadFile(d);var o=ae(t);if(!o)throw new le.ErrnoError(48);return u(e,A,o,t,r),{ptr:o,allocated:!0}},d.stream_ops=l,d},createPreloadedFile:(e,t,r,i,n,s,a,d,l,u)=>{var c=t?ne.resolve(ie.join2(e,t)):e;function f(r){function o(r){u&&u(),d||le.createDataFile(e,t,r,i,n,l),s&&s(),V()}Browser.handledByPreloadPlugin(r,c,o,(()=>{a&&a(),V()}))||o(r)}H(),"string"==typeof r?function(e,t,r,i){var n=i?"":"al "+e;o(e,(r=>{S(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),n&&V()}),(t=>{if(!r)throw'Loading data file "'+e+'" failed.';r()})),n&&H()}(r,(e=>f(e)),a):f(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=le.indexedDB();try{var n=i.open(le.DB_NAME(),le.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=()=>{_("creating db"),n.result.createObjectStore(le.DB_STORE_NAME)},n.onsuccess=()=>{var i=n.result.transaction([le.DB_STORE_NAME],"readwrite"),o=i.objectStore(le.DB_STORE_NAME),s=0,a=0,d=e.length;function l(){0==a?t():r()}e.forEach((e=>{var t=o.put(le.analyzePath(e).object.contents,e);t.onsuccess=()=>{++s+a==d&&l()},t.onerror=()=>{a++,s+a==d&&l()}})),i.onerror=r},n.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=le.indexedDB();try{var n=i.open(le.DB_NAME(),le.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=r,n.onsuccess=()=>{var i=n.result;try{var o=i.transaction([le.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var s=o.objectStore(le.DB_STORE_NAME),a=0,d=0,l=e.length;function u(){0==d?t():r()}e.forEach((e=>{var t=s.get(e);t.onsuccess=()=>{le.analyzePath(e).exists&&le.unlink(e),le.createDataFile(ie.dirname(e),ie.basename(e),t.result,!0,!0,!0),++a+d==l&&u()},t.onerror=()=>{d++,a+d==l&&u()}})),o.onerror=r},n.onerror=r}},ue={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(ie.isAbs(t))return t;var i;if(-100===e)i=le.cwd();else{var n=le.getStream(e);if(!n)throw new le.ErrnoError(8);i=n.path}if(0==t.length){if(!r)throw new le.ErrnoError(44);return i}return ie.join2(i,t)},doStat:function(e,t,r){try{var i=e(t)}catch(e){if(e&&e.node&&ie.normalize(t)!==ie.normalize(le.getPath(e.node)))return-54;throw e}return B[r>>2]=i.dev,B[r+4>>2]=0,B[r+8>>2]=i.ino,B[r+12>>2]=i.mode,B[r+16>>2]=i.nlink,B[r+20>>2]=i.uid,B[r+24>>2]=i.gid,B[r+28>>2]=i.rdev,B[r+32>>2]=0,K=[i.size>>>0,(Y=i.size,+Math.abs(Y)>=1?Y>0?(0|Math.min(+Math.floor(Y/4294967296),4294967295))>>>0:~~+Math.ceil((Y-+(~~Y>>>0))/4294967296)>>>0:0)],B[r+40>>2]=K[0],B[r+44>>2]=K[1],B[r+48>>2]=4096,B[r+52>>2]=i.blocks,K=[Math.floor(i.atime.getTime()/1e3)>>>0,(Y=Math.floor(i.atime.getTime()/1e3),+Math.abs(Y)>=1?Y>0?(0|Math.min(+Math.floor(Y/4294967296),4294967295))>>>0:~~+Math.ceil((Y-+(~~Y>>>0))/4294967296)>>>0:0)],B[r+56>>2]=K[0],B[r+60>>2]=K[1],B[r+64>>2]=0,K=[Math.floor(i.mtime.getTime()/1e3)>>>0,(Y=Math.floor(i.mtime.getTime()/1e3),+Math.abs(Y)>=1?Y>0?(0|Math.min(+Math.floor(Y/4294967296),4294967295))>>>0:~~+Math.ceil((Y-+(~~Y>>>0))/4294967296)>>>0:0)],B[r+72>>2]=K[0],B[r+76>>2]=K[1],B[r+80>>2]=0,K=[Math.floor(i.ctime.getTime()/1e3)>>>0,(Y=Math.floor(i.ctime.getTime()/1e3),+Math.abs(Y)>=1?Y>0?(0|Math.min(+Math.floor(Y/4294967296),4294967295))>>>0:~~+Math.ceil((Y-+(~~Y>>>0))/4294967296)>>>0:0)],B[r+88>>2]=K[0],B[r+92>>2]=K[1],B[r+96>>2]=0,K=[i.ino>>>0,(Y=i.ino,+Math.abs(Y)>=1?Y>0?(0|Math.min(+Math.floor(Y/4294967296),4294967295))>>>0:~~+Math.ceil((Y-+(~~Y>>>0))/4294967296)>>>0:0)],B[r+104>>2]=K[0],B[r+108>>2]=K[1],0},doMsync:function(e,t,r,i,n){var o=U.slice(e,e+r);le.msync(t,o,n,r,i)},varargs:void 0,get:function(){return ue.varargs+=4,B[ue.varargs-4>>2]},getStr:function(e){return L(e)},getStreamFromFD:function(e){var t=le.getStream(e);if(!t)throw new le.ErrnoError(8);return t}};function ce(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var fe=void 0;function he(e){for(var t="",r=e;U[r];)t+=fe[U[r++]];return t}var pe={},me={},_e={},ge=48,ye=57;function ve(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=ge&&t<=ye?"_"+e:e}function be(e,t){return e=ve(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function we(e,t){var r=be(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var Se=void 0;function Ee(e){throw new Se(e)}var Ae=void 0;function Ue(e){throw new Ae(e)}function Te(e,t,r){function i(t){var i=r(t);i.length!==e.length&&Ue("Mismatched type converter count");for(var n=0;n<e.length;++n)xe(e[n],i[n])}e.forEach((function(e){_e[e]=t}));var n=new Array(t.length),o=[],s=0;t.forEach(((e,t)=>{me.hasOwnProperty(e)?n[t]=me[e]:(o.push(e),pe.hasOwnProperty(e)||(pe[e]=[]),pe[e].push((()=>{n[t]=me[e],++s===o.length&&i(n)})))})),0===o.length&&i(n)}function xe(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var i=t.name;if(e||Ee('type "'+i+'" must have a positive integer typeid pointer'),me.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;Ee("Cannot register type '"+i+"' twice")}if(me[e]=t,delete _e[e],pe.hasOwnProperty(e)){var n=pe[e];delete pe[e],n.forEach((e=>e()))}}function Be(e){if(!(this instanceof Xe))return!1;if(!(e instanceof Xe))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,i=e.$$.ptrType.registeredClass,n=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;i.baseClass;)n=i.upcast(n),i=i.baseClass;return t===i&&r===n}function ke(e){Ee(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Ce=!1;function De(e){}function Pe(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Fe(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var i=Fe(e,t,r.baseClass);return null===i?null:r.downcast(i)}var Ie={};function Le(){return Object.keys(Ge).length}function Me(){var e=[];for(var t in Ge)Ge.hasOwnProperty(t)&&e.push(Ge[t]);return e}var Re=[];function Ne(){for(;Re.length;){var e=Re.pop();e.$$.deleteScheduled=!1,e.delete()}}var ze=void 0;function Oe(e){ze=e,Re.length&&ze&&ze(Ne)}var Ge={};function $e(e,t){return t=function(e,t){for(void 0===t&&Ee("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),Ge[t]}function He(e,t){return t.ptrType&&t.ptr||Ue("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!==!!t.smartPtr&&Ue("Both smartPtrType and smartPtr must be specified"),t.count={value:1},We(Object.create(e,{$$:{value:t}}))}function Ve(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=$e(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var i=r.clone();return this.destructor(e),i}function n(){return this.isSmartPointer?He(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):He(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var o,s=this.registeredClass.getActualType(t),a=Ie[s];if(!a)return n.call(this);o=this.isConst?a.constPointerType:a.pointerType;var d=Fe(t,this.registeredClass,o.registeredClass);return null===d?n.call(this):this.isSmartPointer?He(o.registeredClass.instancePrototype,{ptrType:o,ptr:d,smartPtrType:this,smartPtr:e}):He(o.registeredClass.instancePrototype,{ptrType:o,ptr:d})}function We(e){return"undefined"==typeof FinalizationRegistry?(We=e=>e,e):(Ce=new FinalizationRegistry((e=>{Pe(e.$$)})),We=e=>{var t=e.$$;if(!!t.smartPtr){var r={$$:t};Ce.register(e,r,e)}return e},De=e=>Ce.unregister(e),We(e))}function je(){if(this.$$.ptr||ke(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=We(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function qe(){this.$$.ptr||ke(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Ee("Object already scheduled for deletion"),De(this),Pe(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Ye(){return!this.$$.ptr}function Ke(){return this.$$.ptr||ke(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Ee("Object already scheduled for deletion"),Re.push(this),1===Re.length&&ze&&ze(Ne),this.$$.deleteScheduled=!0,this}function Xe(){}function Ze(e,t,r){if(void 0===e[t].overloadTable){var i=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Ee("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[i.argCount]=i}}function Je(e,t,r,i,n,o,s,a){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=i,this.baseClass=n,this.getActualType=o,this.upcast=s,this.downcast=a,this.pureVirtualFunctions=[]}function Qe(e,t,r){for(;t!==r;)t.upcast||Ee("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function et(e,t){if(null===t)return this.isReference&&Ee("null is not a valid "+this.name),0;t.$$||Ee('Cannot pass "'+Ut(t)+'" as a '+this.name),t.$$.ptr||Ee("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Qe(t.$$.ptr,r,this.registeredClass)}function tt(e,t){var r;if(null===t)return this.isReference&&Ee("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||Ee('Cannot pass "'+Ut(t)+'" as a '+this.name),t.$$.ptr||Ee("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&Ee("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var i=t.$$.ptrType.registeredClass;if(r=Qe(t.$$.ptr,i,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&Ee("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:Ee("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var n=t.clone();r=this.rawShare(r,At.toHandle((function(){n.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:Ee("Unsupporting sharing policy")}return r}function rt(e,t){if(null===t)return this.isReference&&Ee("null is not a valid "+this.name),0;t.$$||Ee('Cannot pass "'+Ut(t)+'" as a '+this.name),t.$$.ptr||Ee("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&Ee("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Qe(t.$$.ptr,r,this.registeredClass)}function it(e){return this.fromWireType(B[e>>2])}function nt(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function ot(e){this.rawDestructor&&this.rawDestructor(e)}function st(e){null!==e&&e.delete()}function at(e,t,r,i,n,o,s,a,d,l,u){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=i,this.isSmartPointer=n,this.pointeeType=o,this.sharingPolicy=s,this.rawGetPointee=a,this.rawConstructor=d,this.rawShare=l,this.rawDestructor=u,n||void 0!==t.baseClass?this.toWireType=tt:i?(this.toWireType=et,this.destructorFunction=null):(this.toWireType=rt,this.destructorFunction=null)}var dt=[];function lt(e){var t=dt[e];return t||(e>=dt.length&&(dt.length=e+1),dt[e]=t=P.get(e)),t}function ut(e,r,i){return e.includes("j")?function(e,r,i){var n=t["dynCall_"+e];return i&&i.length?n.apply(null,[r].concat(i)):n.call(null,r)}(e,r,i):lt(r).apply(null,i)}function ct(e,t){var r,i,n,o=(e=he(e)).includes("j")?(r=e,i=t,n=[],function(){return n.length=0,Object.assign(n,arguments),ut(r,i,n)}):lt(t);return"function"!=typeof o&&Ee("unknown function pointer with signature "+e+": "+t),o}var ft=void 0;function ht(e){var t=jt(e),r=he(t);return Vt(t),r}function pt(e,t){var r=[],i={};throw t.forEach((function e(t){i[t]||me[t]||(_e[t]?_e[t].forEach(e):(r.push(t),i[t]=!0))})),new ft(e+": "+r.map(ht).join([", "]))}function mt(e,t){for(var r=[],i=0;i<e;i++)r.push(k[t+4*i>>2]);return r}function _t(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function gt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=be(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var i=new r,n=e.apply(i,t);return n instanceof Object?n:i}function yt(e,t,r,i,n){var o=t.length;o<2&&Ee("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==t[1]&&null!==r,a=!1,d=1;d<t.length;++d)if(null!==t[d]&&void 0===t[d].destructorFunction){a=!0;break}var l="void"!==t[0].name,u="",c="";for(d=0;d<o-2;++d)u+=(0!==d?", ":"")+"arg"+d,c+=(0!==d?", ":"")+"arg"+d+"Wired";var f="return function "+ve(e)+"("+u+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";a&&(f+="var destructors = [];\n");var h=a?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[Ee,i,n,_t,t[0],t[1]];s&&(f+="var thisWired = classParam.toWireType("+h+", this);\n");for(d=0;d<o-2;++d)f+="var arg"+d+"Wired = argType"+d+".toWireType("+h+", arg"+d+"); // "+t[d+2].name+"\n",p.push("argType"+d),m.push(t[d+2]);if(s&&(c="thisWired"+(c.length>0?", ":"")+c),f+=(l?"var rv = ":"")+"invoker(fn"+(c.length>0?", ":"")+c+");\n",a)f+="runDestructors(destructors);\n";else for(d=s?1:2;d<t.length;++d){var _=1===d?"thisWired":"arg"+(d-2)+"Wired";null!==t[d].destructorFunction&&(f+=_+"_dtor("+_+"); // "+t[d].name+"\n",p.push(_+"_dtor"),m.push(t[d].destructorFunction))}return l&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",p.push(f),gt(Function,p).apply(null,m)}var vt=[],bt=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function wt(e){e>4&&0==--bt[e].refcount&&(bt[e]=void 0,vt.push(e))}function St(){for(var e=0,t=5;t<bt.length;++t)void 0!==bt[t]&&++e;return e}function Et(){for(var e=5;e<bt.length;++e)if(void 0!==bt[e])return bt[e];return null}var At={toValue:e=>(e||Ee("Cannot use deleted val. handle = "+e),bt[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=vt.length?vt.pop():bt.length;return bt[t]={refcount:1,value:e},t}}};function Ut(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Tt(e,t){switch(t){case 2:return function(e){return this.fromWireType(C[e>>2])};case 3:return function(e){return this.fromWireType(D[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function xt(e,t,r){switch(t){case 0:return r?function(e){return A[e]}:function(e){return U[e]};case 1:return r?function(e){return T[e>>1]}:function(e){return x[e>>1]};case 2:return r?function(e){return B[e>>2]}:function(e){return k[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Bt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function kt(e,t){for(var r=e,i=r>>1,n=i+t/2;!(i>=n)&&x[i];)++i;if((r=i<<1)-e>32&&Bt)return Bt.decode(U.subarray(e,r));for(var o="",s=0;!(s>=t/2);++s){var a=T[e+2*s>>1];if(0==a)break;o+=String.fromCharCode(a)}return o}function Ct(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var i=t,n=(r-=2)<2*e.length?r/2:e.length,o=0;o<n;++o){var s=e.charCodeAt(o);T[t>>1]=s,t+=2}return T[t>>1]=0,t-i}function Dt(e){return 2*e.length}function Pt(e,t){for(var r=0,i="";!(r>=t/4);){var n=B[e+4*r>>2];if(0==n)break;if(++r,n>=65536){var o=n-65536;i+=String.fromCharCode(55296|o>>10,56320|1023&o)}else i+=String.fromCharCode(n)}return i}function Ft(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var i=t,n=i+r-4,o=0;o<e.length;++o){var s=e.charCodeAt(o);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++o);if(B[t>>2]=s,(t+=4)+4>n)break}return B[t>>2]=0,t-i}function It(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i>=55296&&i<=57343&&++r,t+=4}return t}var Lt={};var Mt=[];var Rt=[];var Nt={};function zt(){if(!zt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var t in Nt)void 0===Nt[t]?delete e[t]:e[t]=Nt[t];var r=[];for(var t in e)r.push(t+"="+e[t]);zt.strings=r}return zt.strings}var Ot=function(e,t,r,i){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=le.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=i},Gt=365,$t=146;Object.defineProperties(Ot.prototype,{read:{get:function(){return(this.mode&Gt)===Gt},set:function(e){e?this.mode|=Gt:this.mode&=-366}},write:{get:function(){return(this.mode&$t)===$t},set:function(e){e?this.mode|=$t:this.mode&=-147}},isFolder:{get:function(){return le.isDir(this.mode)}},isDevice:{get:function(){return le.isChrdev(this.mode)}}}),le.FSNode=Ot,le.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);fe=e}(),Se=t.BindingError=we(Error,"BindingError"),Ae=t.InternalError=we(Error,"InternalError"),Xe.prototype.isAliasOf=Be,Xe.prototype.clone=je,Xe.prototype.delete=qe,Xe.prototype.isDeleted=Ye,Xe.prototype.deleteLater=Ke,t.getInheritedInstanceCount=Le,t.getLiveInheritedInstances=Me,t.flushPendingDeletes=Ne,t.setDelayFunction=Oe,at.prototype.getPointee=nt,at.prototype.destructor=ot,at.prototype.argPackAdvance=8,at.prototype.readValueFromPointer=it,at.prototype.deleteObject=st,at.prototype.fromWireType=Ve,ft=t.UnboundTypeError=we(Error,"UnboundTypeError"),t.count_emval_handles=St,t.get_first_emval=Et;var Ht={q:function(e){return Yt(e+24)+24},p:function(e,t,r){throw new re(e).init(t,r),e},C:function(e,t,r){ue.varargs=r;try{var i=ue.getStreamFromFD(e);switch(t){case 0:return(n=ue.get())<0?-28:le.createStream(i,n).fd;case 1:case 2:case 6:case 7:return 0;case 3:return i.flags;case 4:var n=ue.get();return i.flags|=n,0;case 5:n=ue.get();return T[n+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return o=28,B[Wt()>>2]=o,-1}}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return-e.errno}var o},w:function(e,t,r,i){ue.varargs=i;try{t=ue.getStr(t),t=ue.calculateAt(e,t);var n=i?ue.get():0;return le.open(t,r,n).fd}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return-e.errno}},u:function(e,t,r,i,n){},E:function(e,t,r,i,n){var o=ce(r);xe(e,{name:t=he(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?i:n},argPackAdvance:8,readValueFromPointer:function(e){var i;if(1===r)i=A;else if(2===r)i=T;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);i=B}return this.fromWireType(i[e>>o])},destructorFunction:null})},t:function(e,r,i,n,o,s,a,d,l,u,c,f,h){c=he(c),s=ct(o,s),d&&(d=ct(a,d)),u&&(u=ct(l,u)),h=ct(f,h);var p=ve(c);!function(e,r,i){t.hasOwnProperty(e)?((void 0===i||void 0!==t[e].overloadTable&&void 0!==t[e].overloadTable[i])&&Ee("Cannot register public name '"+e+"' twice"),Ze(t,e,e),t.hasOwnProperty(i)&&Ee("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),t[e].overloadTable[i]=r):(t[e]=r,void 0!==i&&(t[e].numArguments=i))}(p,(function(){pt("Cannot construct "+c+" due to unbound types",[n])})),Te([e,r,i],n?[n]:[],(function(r){var i,o;r=r[0],o=n?(i=r.registeredClass).instancePrototype:Xe.prototype;var a=be(p,(function(){if(Object.getPrototypeOf(this)!==l)throw new Se("Use 'new' to construct "+c);if(void 0===f.constructor_body)throw new Se(c+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new Se("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),l=Object.create(o,{constructor:{value:a}});a.prototype=l;var f=new Je(c,a,l,h,i,s,d,u),m=new at(c,f,!0,!1,!1),_=new at(c+"*",f,!1,!1,!1),g=new at(c+" const*",f,!1,!0,!1);return Ie[e]={pointerType:_,constPointerType:g},function(e,r,i){t.hasOwnProperty(e)||Ue("Replacing nonexistant public symbol"),void 0!==t[e].overloadTable&&void 0!==i?t[e].overloadTable[i]=r:(t[e]=r,t[e].argCount=i)}(p,a),[m,_,g]}))},r:function(e,t,r,i,n,o){S(t>0);var s=mt(t,r);n=ct(i,n),Te([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new Se("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{pt("Cannot construct "+e.name+" due to unbound types",s)},Te([],s,(function(i){return i.splice(1,0,null),e.registeredClass.constructor_body[t-1]=yt(r,i,null,n,o),[]})),[]}))},d:function(e,t,r,i,n,o,s,a){var d=mt(r,i);t=he(t),o=ct(n,o),Te([],[e],(function(e){var i=(e=e[0]).name+"."+t;function n(){pt("Cannot call "+i+" due to unbound types",d)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),a&&e.registeredClass.pureVirtualFunctions.push(t);var l=e.registeredClass.instancePrototype,u=l[t];return void 0===u||void 0===u.overloadTable&&u.className!==e.name&&u.argCount===r-2?(n.argCount=r-2,n.className=e.name,l[t]=n):(Ze(l,t,i),l[t].overloadTable[r-2]=n),Te([],d,(function(n){var a=yt(i,n,e,o,s);return void 0===l[t].overloadTable?(a.argCount=r-2,l[t]=a):l[t].overloadTable[r-2]=a,[]})),[]}))},D:function(e,t){xe(e,{name:t=he(t),fromWireType:function(e){var t=At.toValue(e);return wt(e),t},toWireType:function(e,t){return At.toHandle(t)},argPackAdvance:8,readValueFromPointer:it,destructorFunction:null})},n:function(e,t,r){var i=ce(r);xe(e,{name:t=he(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:Tt(t,i),destructorFunction:null})},c:function(e,t,r,i,n){t=he(t);var o=ce(r),s=e=>e;if(0===i){var a=32-8*r;s=e=>e<<a>>>a}var d=t.includes("unsigned");xe(e,{name:t,fromWireType:s,toWireType:d?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:xt(t,o,0!==i),destructorFunction:null})},b:function(e,t,r){var i=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function n(e){var t=k,r=t[e>>=2],n=t[e+1];return new i(E,n,r)}xe(e,{name:r=he(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})},m:function(e,t){var r="std::string"===(t=he(t));xe(e,{name:t,fromWireType:function(e){var t,i=k[e>>2],n=e+4;if(r)for(var o=n,s=0;s<=i;++s){var a=n+s;if(s==i||0==U[a]){var d=L(o,a-o);void 0===t?t=d:(t+=String.fromCharCode(0),t+=d),o=a+1}}else{var l=new Array(i);for(s=0;s<i;++s)l[s]=String.fromCharCode(U[n+s]);t=l.join("")}return Vt(e),t},toWireType:function(e,t){var i;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Ee("Cannot pass non-string to std::string"),i=r&&n?R(t):t.length;var o=Yt(4+i+1),s=o+4;if(k[o>>2]=i,r&&n)M(t,U,s,i+1);else if(n)for(var a=0;a<i;++a){var d=t.charCodeAt(a);d>255&&(Vt(s),Ee("String has UTF-16 code units that do not fit in 8 bits")),U[s+a]=d}else for(a=0;a<i;++a)U[s+a]=t[a];return null!==e&&e.push(Vt,o),o},argPackAdvance:8,readValueFromPointer:it,destructorFunction:function(e){Vt(e)}})},h:function(e,t,r){var i,n,o,s,a;r=he(r),2===t?(i=kt,n=Ct,s=Dt,o=()=>x,a=1):4===t&&(i=Pt,n=Ft,s=It,o=()=>k,a=2),xe(e,{name:r,fromWireType:function(e){for(var r,n=k[e>>2],s=o(),d=e+4,l=0;l<=n;++l){var u=e+4+l*t;if(l==n||0==s[u>>a]){var c=i(d,u-d);void 0===r?r=c:(r+=String.fromCharCode(0),r+=c),d=u+t}}return Vt(e),r},toWireType:function(e,i){"string"!=typeof i&&Ee("Cannot pass non-string to C++ string type "+r);var o=s(i),d=Yt(4+o+t);return k[d>>2]=o>>a,n(i,d+4,o+t),null!==e&&e.push(Vt,d),d},argPackAdvance:8,readValueFromPointer:it,destructorFunction:function(e){Vt(e)}})},o:function(e,t){xe(e,{isVoid:!0,name:t=he(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},f:function(){return Date.now()},g:function(e,t,r,i){var n,o;(e=Mt[e])(t=At.toValue(t),r=void 0===(o=Lt[n=r])?he(n):o,null,i)},j:wt,i:function(e,t){var r=function(e,t){for(var r,i,n,o=new Array(e),s=0;s<e;++s)o[s]=(r=k[t+s*b>>2],i="parameter "+s,n=void 0,void 0===(n=me[r])&&Ee(i+" has unknown type "+ht(r)),n);return o}(e,t),i=r[0],n=i.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",o=Rt[n];if(void 0!==o)return o;for(var s=["retType"],a=[i],d="",l=0;l<e-1;++l)d+=(0!==l?", ":"")+"arg"+l,s.push("argType"+l),a.push(r[1+l]);var u="return function "+ve("methodCaller_"+n)+"(handle, name, destructors, args) {\n",c=0;for(l=0;l<e-1;++l)u+="    var arg"+l+" = argType"+l+".readValueFromPointer(args"+(c?"+"+c:"")+");\n",c+=r[l+1].argPackAdvance;for(u+="    var rv = handle[name]("+d+");\n",l=0;l<e-1;++l)r[l+1].deleteObject&&(u+="    argType"+l+".deleteObject(arg"+l+");\n");i.isVoid||(u+="    return retType.toWireType(destructors, rv);\n"),u+="};\n",s.push(u);var f,h,p=gt(Function,s).apply(null,a);return f=p,h=Mt.length,Mt.push(f),o=h,Rt[n]=o,o},a:function(){W("")},A:function(e,t,r){U.copyWithin(e,t,t+r)},v:function(e){U.length,W("OOM")},y:function(e,t){var r=0;return zt().forEach((function(i,n){var o=t+r;k[e+4*n>>2]=o,function(e,t,r){for(var i=0;i<e.length;++i)A[t++>>0]=e.charCodeAt(i);r||(A[t>>0]=0)}(i,o),r+=i.length+1})),0},z:function(e,t){var r=zt();k[e>>2]=r.length;var i=0;return r.forEach((function(e){i+=e.length+1})),k[t>>2]=i,0},l:function(e){try{var t=ue.getStreamFromFD(e);return le.close(t),0}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return e.errno}},x:function(e,t){try{var r=ue.getStreamFromFD(e),i=r.tty?2:le.isDir(r.mode)?3:le.isLink(r.mode)?7:4;return A[t>>0]=i,0}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return e.errno}},B:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,o=0;o<r;o++){var s=k[t>>2],a=k[t+4>>2];t+=8;var d=le.read(e,A,s,a,i);if(d<0)return-1;if(n+=d,d<a)break}return n}(ue.getStreamFromFD(e),t,r);return B[i>>2]=n,0}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return e.errno}},s:function(e,t,r,i,n){try{var o=(d=r)+2097152>>>0<4194305-!!(a=t)?(a>>>0)+4294967296*d:NaN;if(isNaN(o))return 61;var s=ue.getStreamFromFD(e);return le.llseek(s,o,i),K=[s.position>>>0,(Y=s.position,+Math.abs(Y)>=1?Y>0?(0|Math.min(+Math.floor(Y/4294967296),4294967295))>>>0:~~+Math.ceil((Y-+(~~Y>>>0))/4294967296)>>>0:0)],B[n>>2]=K[0],B[n+4>>2]=K[1],s.getdents&&0===o&&0===i&&(s.getdents=null),0}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return e.errno}var a,d},k:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,o=0;o<r;o++){var s=k[t>>2],a=k[t+4>>2];t+=8;var d=le.write(e,A,s,a,i);if(d<0)return-1;n+=d}return n}(ue.getStreamFromFD(e),t,r);return k[i>>2]=n,0}catch(e){if(void 0===le||!(e instanceof le.ErrnoError))throw e;return e.errno}},e:function(e){}};!function(){var e={a:Ht};function r(e,r){var i,n,o=e.exports;t.asm=o,v=t.asm.F,i=v.buffer,E=i,t.HEAP8=A=new Int8Array(i),t.HEAP16=T=new Int16Array(i),t.HEAP32=B=new Int32Array(i),t.HEAPU8=U=new Uint8Array(i),t.HEAPU16=x=new Uint16Array(i),t.HEAPU32=k=new Uint32Array(i),t.HEAPF32=C=new Float32Array(i),t.HEAPF64=D=new Float64Array(i),P=t.asm.I,n=t.asm.G,z.unshift(n),V()}function n(e){r(e.instance)}function s(t){return function(){if(!y&&(f||h)){if("function"==typeof fetch&&!J(j))return fetch(j,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+j+"'";return e.arrayBuffer()})).catch((function(){return Q(j)}));if(o)return new Promise((function(e,t){o(j,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return Q(j)}))}().then((function(t){return WebAssembly.instantiate(t,e)})).then((function(e){return e})).then(t,(function(e){g("failed to asynchronously prepare wasm: "+e),W(e)}))}if(H(),t.instantiateWasm)try{return t.instantiateWasm(e,r)}catch(e){return g("Module.instantiateWasm callback failed with error: "+e),!1}(y||"function"!=typeof WebAssembly.instantiateStreaming||Z(j)||J(j)||p||"function"!=typeof fetch?s(n):fetch(j,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(n,(function(e){return g("wasm streaming compile failed: "+e),g("falling back to ArrayBuffer instantiation"),s(n)}))}))).catch(i)}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.G).apply(null,arguments)};var Vt=t._free=function(){return(Vt=t._free=t.asm.H).apply(null,arguments)},Wt=t.___errno_location=function(){return(Wt=t.___errno_location=t.asm.J).apply(null,arguments)},jt=t.___getTypeName=function(){return(jt=t.___getTypeName=t.asm.K).apply(null,arguments)};t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.L).apply(null,arguments)};var qt,Yt=t._malloc=function(){return(Yt=t._malloc=t.asm.M).apply(null,arguments)},Kt=t._emscripten_builtin_memalign=function(){return(Kt=t._emscripten_builtin_memalign=t.asm.N).apply(null,arguments)},Xt=t.___cxa_is_pointer_type=function(){return(Xt=t.___cxa_is_pointer_type=t.asm.O).apply(null,arguments)};function Zt(e){function i(){qt||(qt=!0,t.calledRun=!0,w||(t.noFSInit||le.init.initialized||le.init(),le.ignorePermissions=!1,te(z),r(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),O.unshift(e);var e;te(O)}()))}G>0||(!function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),N.unshift(e);var e;te(N)}(),G>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),i()}),1)):i()))}if(t.dynCall_viiijj=function(){return(t.dynCall_viiijj=t.asm.P).apply(null,arguments)},t.dynCall_jij=function(){return(t.dynCall_jij=t.asm.Q).apply(null,arguments)},t.dynCall_jii=function(){return(t.dynCall_jii=t.asm.R).apply(null,arguments)},t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.S).apply(null,arguments)},$=function e(){qt||Zt(),qt||($=e)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return Zt(),t.ready}})(),n=1e-6,o="undefined"!=typeof Float32Array?Float32Array:Array;function s(){var e=new o(16);return o!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function a(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var d,l=function(e,t,r,i,n,o,s){var a=1/(t-r),d=1/(i-n),l=1/(o-s);return e[0]=-2*a,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*d,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*l,e[11]=0,e[12]=(t+r)*a,e[13]=(n+i)*d,e[14]=(s+o)*l,e[15]=1,e};function u(e,t,r){var i=new o(3);return i[0]=e,i[1]=t,i[2]=r,i}d=new o(3),o!=Float32Array&&(d[0]=0,d[1]=0,d[2]=0);var c=(e,t)=>{t&&e.pixelStorei(e.UNPACK_ALIGNMENT,1);const r=function(){const t=m(e.VERTEX_SHADER,"\n            attribute vec4 aVertexPosition;\n            attribute vec2 aTexturePosition;\n            uniform mat4 uModelMatrix;\n            uniform mat4 uViewMatrix;\n            uniform mat4 uProjectionMatrix;\n            varying lowp vec2 vTexturePosition;\n            void main(void) {\n              gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * aVertexPosition;\n              vTexturePosition = aTexturePosition;\n            }\n        "),r=m(e.FRAGMENT_SHADER,"\n            precision highp float;\n            varying highp vec2 vTexturePosition;\n            uniform int isyuv;\n            uniform sampler2D rgbaTexture;\n            uniform sampler2D yTexture;\n            uniform sampler2D uTexture;\n            uniform sampler2D vTexture;\n\n            const mat4 YUV2RGB = mat4( 1.1643828125, 0, 1.59602734375, -.87078515625,\n                                       1.1643828125, -.39176171875, -.81296875, .52959375,\n                                       1.1643828125, 2.017234375, 0, -1.081390625,\n                                       0, 0, 0, 1);\n\n\n            void main(void) {\n\n                if (isyuv>0) {\n\n                    highp float y = texture2D(yTexture,  vTexturePosition).r;\n                    highp float u = texture2D(uTexture,  vTexturePosition).r;\n                    highp float v = texture2D(vTexture,  vTexturePosition).r;\n                    gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;\n\n                } else {\n                    gl_FragColor =  texture2D(rgbaTexture, vTexturePosition);\n                }\n            }\n        "),i=e.createProgram();if(e.attachShader(i,t),e.attachShader(i,r),e.linkProgram(i),!e.getProgramParameter(i,e.LINK_STATUS))return console.log("Unable to initialize the shader program: "+e.getProgramInfoLog(i)),null;return i}();let i={program:r,attribLocations:{vertexPosition:e.getAttribLocation(r,"aVertexPosition"),texturePosition:e.getAttribLocation(r,"aTexturePosition")},uniformLocations:{projectionMatrix:e.getUniformLocation(r,"uProjectionMatrix"),modelMatrix:e.getUniformLocation(r,"uModelMatrix"),viewMatrix:e.getUniformLocation(r,"uViewMatrix"),rgbatexture:e.getUniformLocation(r,"rgbaTexture"),ytexture:e.getUniformLocation(r,"yTexture"),utexture:e.getUniformLocation(r,"uTexture"),vtexture:e.getUniformLocation(r,"vTexture"),isyuv:e.getUniformLocation(r,"isyuv")}},o=function(){const t=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,t);e.bufferData(e.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1]),e.STATIC_DRAW);var r=[];r=r.concat([0,1],[1,1],[1,0],[0,0]);const i=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,i),e.bufferData(e.ARRAY_BUFFER,new Float32Array(r),e.STATIC_DRAW);const n=e.createBuffer();e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,n);return e.bufferData(e.ELEMENT_ARRAY_BUFFER,new Uint16Array([0,1,2,0,2,3]),e.STATIC_DRAW),{position:t,texPosition:i,indices:n}}(),d=p(),c=p(),f=p(),h=p();function p(){let t=e.createTexture();return e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t}function m(t,r){const i=e.createShader(t);return e.shaderSource(i,r),e.compileShader(i),e.getShaderParameter(i,e.COMPILE_STATUS)?i:(console.log("An error occurred compiling the shaders: "+e.getShaderInfoLog(i)),e.deleteShader(i),null)}function _(t,r){e.viewport(0,0,t,r),e.clearColor(0,0,0,0),e.clearDepth(1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT);const d=s();l(d,-1,1,-1,1,.1,100);const p=s();a(p);const m=s();!function(e,t,r,i){var o,s,d,l,u,c,f,h,p,m,_=t[0],g=t[1],y=t[2],v=i[0],b=i[1],w=i[2],S=r[0],E=r[1],A=r[2];Math.abs(_-S)<n&&Math.abs(g-E)<n&&Math.abs(y-A)<n?a(e):(f=_-S,h=g-E,p=y-A,o=b*(p*=m=1/Math.hypot(f,h,p))-w*(h*=m),s=w*(f*=m)-v*p,d=v*h-b*f,(m=Math.hypot(o,s,d))?(o*=m=1/m,s*=m,d*=m):(o=0,s=0,d=0),l=h*d-p*s,u=p*o-f*d,c=f*s-h*o,(m=Math.hypot(l,u,c))?(l*=m=1/m,u*=m,c*=m):(l=0,u=0,c=0),e[0]=o,e[1]=l,e[2]=f,e[3]=0,e[4]=s,e[5]=u,e[6]=h,e[7]=0,e[8]=d,e[9]=c,e[10]=p,e[11]=0,e[12]=-(o*_+s*g+d*y),e[13]=-(l*_+u*g+c*y),e[14]=-(f*_+h*g+p*y),e[15]=1)}(m,u(0,0,0),u(0,0,-1),u(0,1,0));{const t=3,r=e.FLOAT,n=!1,s=0,a=0;e.bindBuffer(e.ARRAY_BUFFER,o.position),e.vertexAttribPointer(i.attribLocations.vertexPosition,t,r,n,s,a),e.enableVertexAttribArray(i.attribLocations.vertexPosition)}{const t=2,r=e.FLOAT,n=!1,s=0,a=0;e.bindBuffer(e.ARRAY_BUFFER,o.texPosition),e.vertexAttribPointer(i.attribLocations.texturePosition,t,r,n,s,a),e.enableVertexAttribArray(i.attribLocations.texturePosition)}e.activeTexture(e.TEXTURE0+3),e.bindTexture(e.TEXTURE_2D,c),e.activeTexture(e.TEXTURE0+4),e.bindTexture(e.TEXTURE_2D,f),e.activeTexture(e.TEXTURE0+5),e.bindTexture(e.TEXTURE_2D,h),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,o.indices),e.useProgram(i.program),e.uniformMatrix4fv(i.uniformLocations.projectionMatrix,!1,d),e.uniformMatrix4fv(i.uniformLocations.modelMatrix,!1,p),e.uniformMatrix4fv(i.uniformLocations.viewMatrix,!1,m),e.uniform1i(i.uniformLocations.rgbatexture,2),e.uniform1i(i.uniformLocations.ytexture,3),e.uniform1i(i.uniformLocations.utexture,4),e.uniform1i(i.uniformLocations.vtexture,5),e.uniform1i(i.uniformLocations.isyuv,1);{const t=6,r=e.UNSIGNED_SHORT,i=0;e.drawElements(e.TRIANGLES,t,r,i)}}return{render:function(t,r,i,n,o){e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,c),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,r,0,e.LUMINANCE,e.UNSIGNED_BYTE,i),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,o),_(t,r)},renderYUV:function(t,r,i){let n=i.slice(0,t*r),o=i.slice(t*r,t*r*5/4),s=i.slice(t*r*5/4,t*r*3/2);e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,c),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,r,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,o),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),_(t,r)},destroy:function(){e.deleteProgram(i.program),e.deleteBuffer(o.position),e.deleteBuffer(o.texPosition),e.deleteBuffer(o.indices),e.deleteTexture(d),e.deleteTexture(c),e.deleteTexture(f),e.deleteTexture(h),i=null,o=null,d=null,c=null,f=null,h=null}}};const f=1,h=2,p="fetch",m="websocket",_="player",g="playbackTF",y="mp4",v="debug",b="warn",w=36e5,S={playType:_,container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,messageTime:5,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isFmp4Private:!1,isWebrtc:!1,isWebrtcForZLM:!1,isWebrtcForSRS:!1,isWebrtcForOthers:!1,isNakedFlow:!1,isMpeg4:!1,isAliyunRtc:!1,isTs:!1,debug:!1,debugLevel:b,debugUuid:"",isMulti:!0,multiIndex:-1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!0,replayUseLastFrameShow:!0,replayShowLoadingIcon:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,mseCorrectAudioTimeDuration:20,keepScreenOn:!0,isNotMute:!1,muted:!0,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,stretch:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,logSave:!1,aiFace:!1,aiObject:!1,aiOcclusion:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},iceServers:[],channelId:"",controlAutoHide:!1,isLogo:!0,hasControl:!1,loadingIcon:!0,loadingIconStyle:{},loadingText:"",background:"",poster:"",backgroundLoadingShow:!0,loadingBackground:"",loadingBackgroundWidth:0,loadingBackgroundHeight:0,decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderHard:"decoder-pro-hard.js",decoderHardNotWasm:"decoder-pro-hard-not-wasm.js",wasmMp4RecorderDecoder:"easyplayer-mp4-recorder-decoder.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",aspectRatio:"default",playbackConfig:{playList:[],fps:"",showControl:!0,controlType:"normal",duration:0,startTime:"",showRateBtn:!1,rateConfig:[],showPrecision:"",showPrecisionBtn:!0,isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!0,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1,useMSE:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:h,demuxType:"flv",useWasm:!1,useMSE:!1,useWCS:!1,useSIMD:!0,useMThreading:!1,wcsUseVideoRender:!0,wcsUseWebgl2Render:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,webrtcUseCanvasRender:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,simdDecodeErrorReplay:!0,simdDecodeErrorReplayType:"wasm",autoWasm:!0,decoderErrorAutoWasm:!0,hardDecodingNotSupportAutoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,syncAudioAndVideoDiff:500,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:y,checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,useImageDetector:!1,useOcclusionDetector:!1,ptzPositionConfig:{},ptzShowType:"vertical",ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,ptzCruiseShow:!1,ptzFogShow:!1,ptzWiperShow:!1,ptzSupportDraggable:!1,weiXinInAndroidAudioBufferSize:4800,isM7sCrypto:!1,m7sCryptoAudio:!1,isSm4Crypto:!1,isXorCrypto:!1,sm4CryptoKey:"",m7sCryptoKey:"",xorCryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectLevel:2,aiFaceDetectWidth:240,aiFaceDetectShowRect:!0,aiFaceDetectInterval:1e3,aiFaceDetectRectConfig:{},aiObjectDetectLevel:2,aiObjectDetectWidth:240,aiObjectDetectShowRect:!0,aiObjectDetectInterval:1e3,aiObjectDetectRectConfig:{},aiOcclusionDetectInterval:1e3,aiImageDetectDrop:!1,aiImageDetectActive:!1,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!1,isEmitSEI:!1,pauseAndNextPlayUseLastFrameShow:!1,demuxUseWorker:!0,playFailedAndReplay:!0,showMessageConfig:{webglAlignmentError:"Webgl 渲染失败",webglContextLostError:"webgl 上下文丢失",mediaSourceH265NotSupport:"不支持硬解码H265",mediaSourceFull:"缓冲区已满",mediaSourceAppendBufferError:"初始化解码器失败",mseSourceBufferError:"解码失败",mseAddSourceBufferError:"初始化解码器失败",mediaSourceDecoderConfigurationError:"初始化解码器失败",mediaSourceTsIsMaxDiff:"流异常",mseWidthOrHeightChange:"流异常",mediaSourceAudioG711NotSupport:"硬解码不支持G711a/u音频格式",mediaSourceUseCanvasRenderPlayFailed:"MediaSource解码使用canvas渲染失败",webcodecsH265NotSupport:"不支持硬解码H265",webcodecsUnsupportedConfigurationError:"初始化解码器失败",webcodecsDecodeConfigureError:"初始化解码器失败",webcodecsDecodeError:"解码失败",wcsWidthOrHeightChange:"解码失败",wasmDecodeError:"解码失败",simdDecodeError:"解码失败",wasmWidthOrHeightChange:"流异常",wasmUseVideoRenderError:"video自动渲染失败",videoElementPlayingFailed:"video自动渲染失败",simdH264DecodeVideoWidthIsTooLarge:"不支持该分辨率的视频",networkDelayTimeout:"网络超时重播失败",fetchError:"请求失败",streamEnd:"请求结束",websocketError:"请求失败",webrtcError:"请求失败",hlsError:"请求失败",decoderWorkerInitError:"初始化worker失败",videoElementPlayingFailedForWebrtc:"video自动渲染失败",videoInfoError:"解析视频分辨率失败",webrtcStreamH265:"webrtc不支持H265",delayTimeout:"播放超时重播失败",loadingTimeout:"加载超时重播失败",loadingTimeoutRetryEnd:"加载超时重播失败",delayTimeoutRetryEnd:"播放超时重播失败"},videoElementPlayingFailedReplay:!0,mp4RecordUseWasm:!0,mseAutoCleanupSourceBuffer:!0,mseAutoCleanupMaxBackwardDuration:30,mseAutoCleanupMinBackwardDuration:10,widthOrHeightChangeReplay:!0,simdH264DecodeVideoWidthIsTooLargeReplay:!0,mediaSourceAudioG711NotSupportReplay:!0,mediaSourceAudioInitTimeoutReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplayType:"video",widthOrHeightChangeReplayDelayTime:0,ghostWatermarkConfig:{on:5,off:5,content:"",fontSize:12,color:"white",opacity:.15,speed:.2},dynamicWatermarkConfig:{content:"",speed:.2,fontSize:12,color:"white",opacity:.15},isDropSameTimestampGop:!1,mseDecodeAudio:!1,nakedFlowH265DemuxUseNew:!0,extendDomConfig:{html:"",showBeforePlay:!1,showAfterLoading:!0},disableContextmenu:!1,websocket1006ErrorReplay:!1,websocket1006ErrorReplayDelayTime:0,streamErrorReplay:!1,streamErrorReplayDelayTime:0,streamEndReplay:!1,streamEndReplayDelayTime:0,mseDecoderUseWorker:!1,openMemoryLog:!1,mainThreadFetchUseWorker:!0,playFailedAndPausedShowPlayBtn:!0,mseCorrectionTimestamp:!0,flvDemuxBufferSizeTooLargeReplay:!1,flvDemuxBufferSizeMaxLarge:1048576,isCheckInView:!1},E="init",A="initVideo",U="render",T="playAudio",x="initAudio",B="audioCode",k="audioNalu",C="audioAACSequenceHeader",D="videoCode",P="videoCodec",F="videoNalu",I="videoPayload",L="audioPayload",M="workerFetch",R="iframeIntervalTs",N="isDropping",z="playbackStreamVideoFps",O="wasmWidthOrHeightChange",G="simdDecodeError",$="simdH264DecodeVideoWidthIsTooLarge",H="closeEnd",V="tempStream",W="videoSEI",j="flvScriptData",q="aacSequenceHeader",Y="videoSequenceHeader",K="flvBufferData",X="checkFirstIFrame",Z="mseHandle",J="mseFirstRenderTime",Q="mseError",ee=1,te=2,re=8,ie=9,ne=18,oe="init",se="decode",ae="audioDecode",de="videoDecode",le="close",ue="updateConfig",ce="clearBuffer",fe="fetchStream",he="sendWsMessage",pe="mseUpdateVideoTimestamp",me="streamEnd",_e="streamRate",ge="streamAbps",ye="streamVbps",ve="streamDts",be="streamSuccess",we="streamStats",Se="networkDelayTimeout",Ee="websocketOpen",Ae={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",webcodecsDecodeConfigureError:"webcodecsDecodeConfigureError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceAudioG711NotSupport:"mediaSourceAudioG711NotSupport",mediaSourceAudioInitTimeout:"mediaSourceAudioInitTimeout",mediaSourceAudioNoDataTimeout:"mediaSourceAudioNoDataTimeout",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:"mseSourceBufferFull",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",mediaSourceBufferedIsZeroError:"mediaSourceBufferedIsZeroError",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:"webrtcClosed",webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",widthOrHeightChange:"widthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError",streamEnd:me,delayTimeout:"delayTimeout",loadingTimeout:"loadingTimeout",networkDelayTimeout:Se,aliyunRtcError:"aliyunRtcError",...{talkStreamError:"talkStreamError",talkStreamClose:"talkStreamClose"}},Ue=1,Te=7,xe=12,Be=99,ke={h264:"H264(AVC)",h265:"H265(HEVC)"},Ce={AAC:10,ALAW:7,MULAW:8,MP3:2},De={sps:7,pps:8,iFrame:5,kUnspecified:0,kSliceNonIDR:1,kSliceDPA:2,kSliceDPB:3,kSliceDPC:4,kSliceIDR:5,kSliceSEI:6,kSliceSPS:7,kSlicePPS:8,kSliceAUD:9,kEndOfSequence:10,kEndOfStream:11,kFiller:12,kSPSExt:13,kReserved0:14},Pe={pFrame:1,iFrame:19,kSliceIDR_W_RADL:19,nLp:20,kSliceIDR_N_LP:20,craNut:21,kSliceCRA_NUT:21,vps:32,kSliceVPS:32,sps:33,kSliceSPS:33,pps:34,kSlicePPS:34,kSliceAUD:35,sei:39,prefixSei:39,suffixSei:40},Fe="key",Ie="delta",Le={avc:'video/mp4; codecs="avc1.64002A"',hev:'video/mp4; codecs="hev1.1.6.L123.b0"',hev2:'video/mp4;codecs="hev1.1.6.L120.90"',hev3:'video/mp4;codecs="hev1.2.4.L120.90"',hev4:'video/mp4;codecs="hev1.3.E.L120.90"',hev5:'video/mp4;codecs="hev1.4.10.L120.90"'},Me="ended",Re="open",Ne="closed",ze="sourceclose",Oe="sourceopen",Ge="sourceended",$e={h264:"avc",h265:"hevc"},He="AbortError",Ve={sequenceHeader:0,nalu:1},We={keyFrame:1,interFrame:2},je=1,qe="idle",Ye="buffering",Ke="complete",Xe=1,Ze=2,Je=0,Qe=1,et=3,tt=16;function rt(e,t){return e(t={exports:{}},t.exports),t.exports}rt((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},r=e.exports,i=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,n=r.length,o={};i<n;i++)if((e=r[i])&&e[1]in t){for(i=0;i<e.length;i++)o[r[0][i]]=e[i];return o}return!1}(),n={change:i.fullscreenchange,error:i.fullscreenerror},o={request:function(e,r){return new Promise(function(n,o){var s=function(){this.off("change",s),n()}.bind(this);this.on("change",s);var a=(e=e||t.documentElement)[i.requestFullscreen](r);a instanceof Promise&&a.then(s).catch(o)}.bind(this))},exit:function(){return new Promise(function(e,r){if(this.isFullscreen){var n=function(){this.off("change",n),e()}.bind(this);this.on("change",n);var o=t[i.exitFullscreen]();o instanceof Promise&&o.then(n).catch(r)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){var i=n[e];i&&t.addEventListener(i,r,!1)},off:function(e,r){var i=n[e];i&&t.removeEventListener(i,r,!1)},raw:i};i?(Object.defineProperties(o,{isFullscreen:{get:function(){return Boolean(t[i.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[i.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[i.fullscreenEnabled])}}}),r?e.exports=o:window.screenfull=o):r?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()})).isEnabled;class it{constructor(e){this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){let e=this._total_bytes-this._buffer_index;if(e<=0)return void console.error("ExpGolomb: _fillCurrentWord() but no bytes available",this._total_bytes,this._buffer_index);let t=Math.min(4,e),r=new Uint8Array(4);r.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(r.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t}readBits(e){if(e>32&&console.error("ExpGolomb: readBits() bits exceeded max 32bits!"),e<=this._current_word_bits_left){let t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}let t=this._current_word_bits_left?this._current_word:0;t>>>=32-this._current_word_bits_left;let r=e-this._current_word_bits_left;this._fillCurrentWord();let i=Math.min(r,this._current_word_bits_left),n=this._current_word>>>32-i;return this._current_word<<=i,this._current_word_bits_left-=i,t=t<<i|n,t}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){let e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){let e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}function nt(e){let{profile:t,sampleRate:r,channel:i}=e;return new Uint8Array([175,0,t<<3|(14&r)>>1,(1&r)<<7|i<<3])}function ot(e){return st(e)&&e[1]===Ve.sequenceHeader}function st(e){return e[0]>>4===Ce.AAC}const at=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],dt=at,lt=at,ut=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];class ct{constructor(e){this.data_=e,this.eof_flag_=!1,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&console.error("Could not found ADTS syncword until payload end")}findNextSyncwordOffset(e){let t=e,r=this.data_;for(;;){if(t+7>=r.byteLength)return this.eof_flag_=!0,r.byteLength;if(4095===(r[t+0]<<8|r[t+1])>>>4)return t;t++}}readNextAACFrame(){let e=this.data_,t=null;for(;null==t&&!this.eof_flag_;){let r=this.current_syncword_offset_,i=(8&e[r+1])>>>3,n=(6&e[r+1])>>>1,o=1&e[r+1],s=(192&e[r+2])>>>6,a=(60&e[r+2])>>>2,d=(1&e[r+2])<<2|(192&e[r+3])>>>6,l=(3&e[r+3])<<11|e[r+4]<<3|(224&e[r+5])>>>5;if(e[r+6],r+l>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}let u=1===o?7:9,c=l-u;r+=u;let f=this.findNextSyncwordOffset(r+c);if(this.current_syncword_offset_=f,0!==i&&1!==i||0!==n)continue;let h=e.subarray(r,r+c);t={},t.audio_object_type=s+1,t.sampling_freq_index=a,t.sampling_frequency=dt[a],t.channel_config=d,t.data=h}return t}hasIncompleteData(){return this.has_last_incomplete_data}getIncompleteData(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null}}class ft{constructor(e){this.data_=e,this.eof_flag_=!1,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&console.error("Could not found ADTS syncword until payload end")}findNextSyncwordOffset(e){let t=e,r=this.data_;for(;;){if(t+1>=r.byteLength)return this.eof_flag_=!0,r.byteLength;if(695===(r[t+0]<<3|r[t+1]>>>5))return t;t++}}getLATMValue(e){let t=e.readBits(2),r=0;for(let i=0;i<=t;i++)r<<=8,r|=e.readByte();return r}readNextAACFrame(e){let t=this.data_,r=null;for(;null==r&&!this.eof_flag_;){let i=this.current_syncword_offset_,n=(31&t[i+1])<<8|t[i+2];if(i+3+n>=this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}let o=new it(t.subarray(i+3,i+3+n)),s=null;if(o.readBool()){if(null==e){console.warn("StreamMuxConfig Missing"),this.current_syncword_offset_=this.findNextSyncwordOffset(i+3+n),o.destroy();continue}s=e}else{let e=o.readBool();if(e&&o.readBool()){console.error("audioMuxVersionA is Not Supported"),o.destroy();break}if(e&&this.getLATMValue(o),!o.readBool()){console.error("allStreamsSameTimeFraming zero is Not Supported"),o.destroy();break}if(0!==o.readBits(6)){console.error("more than 2 numSubFrames Not Supported"),o.destroy();break}if(0!==o.readBits(4)){console.error("more than 2 numProgram Not Supported"),o.destroy();break}if(0!==o.readBits(3)){console.error("more than 2 numLayer Not Supported"),o.destroy();break}let t=e?this.getLATMValue(o):0,r=o.readBits(5);t-=5;let i=o.readBits(4);t-=4;let n=o.readBits(4);t-=4,o.readBits(3),t-=3,t>0&&o.readBits(t);let a=o.readBits(3);if(0!==a){console.error(`frameLengthType = ${a}. Only frameLengthType = 0 Supported`),o.destroy();break}o.readByte();let d=o.readBool();if(d)if(e)this.getLATMValue(o);else{let e=0;for(;;){e<<=8;let t=o.readBool();if(e+=o.readByte(),!t)break}console.log(e)}o.readBool()&&o.readByte(),s={},s.audio_object_type=r,s.sampling_freq_index=i,s.sampling_frequency=dt[s.sampling_freq_index],s.channel_config=n,s.other_data_present=d}let a=0;for(;;){let e=o.readByte();if(a+=e,255!==e)break}let d=new Uint8Array(a);for(let e=0;e<a;e++)d[e]=o.readByte();r={},r.audio_object_type=s.audio_object_type,r.sampling_freq_index=s.sampling_freq_index,r.sampling_frequency=dt[s.sampling_freq_index],r.channel_config=s.channel_config,r.other_data_present=s.other_data_present,r.data=d,this.current_syncword_offset_=this.findNextSyncwordOffset(i+3+n)}return r}hasIncompleteData(){return this.has_last_incomplete_data}getIncompleteData(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null}}function ht(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function pt(e){const t=e.byteLength,r=new Uint8Array(4);r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t;const i=new Uint8Array(t+4);return i.set(r,0),i.set(e,4),i}function mt(e,t){let r=null;return t?e.length>=28&&(r=1+(3&e[26])):e.length>=12&&(r=1+(3&e[9])),r}function _t(){return(new Date).getTime()}function gt(){return performance&&"function"==typeof performance.now?performance.now():Date.now()}function yt(e){let t=0,r=gt();return i=>{if(n=i,"[object Number]"!==Object.prototype.toString.call(n))return;var n;t+=i;const o=gt(),s=o-r;s>=1e3&&(e(t/s*1e3),r=o,t=0)}}function vt(e){return null==e}function bt(e){return"function"==typeof e}function wt(e){e.close()}function St(e,t){t&&(e=e.filter((e=>e.type&&e.type===t)));let r=e[0],i=null,n=1;if(e.length>0){let t=e[1];t&&t.ts-r.ts>1e5&&(r=t,n=2)}if(r)for(let o=n;o<e.length;o++){let n=e[o];if(t&&n.type&&n.type!==t&&(n=null),n){if(n.ts-r.ts>=1e3){e[o-1].ts-r.ts<1e3&&(i=o+1)}}}return i}function Et(){return function(e){let t="";if("object"==typeof e)try{t=JSON.stringify(e),t=JSON.parse(t)}catch(r){t=e}else t=e;return t}(S)}function At(e){return e[0]>>4===We.keyFrame&&e[1]===Ve.sequenceHeader}function Ut(e){return!0===e||"true"===e}function Tt(e){return!0!==e&&"true"!==e}(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})();var xt=function(e,t,r,i){return new(r||(r=Promise))((function(n,o){function s(e){try{d(i.next(e))}catch(e){o(e)}}function a(e){try{d(i.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}d((i=i.apply(e,t||[])).next())}))};const Bt=Symbol(32),kt=Symbol(16),Ct=Symbol(8);class Dt{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(e){return xt(this,void 0,void 0,(function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise(((t,r)=>{var i;this.reject=r,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,t(e)};this.demand(e,!0)||null===(i=this.pull)||void 0===i||i.call(this,e)}))}))}readU32(){return this.read(Bt)}readU16(){return this.read(kt)}readU8(){return this.read(Ct)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null===(e=this.reject)||void 0===e||e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(!this.buffer||!this.need)return;let e=null;const t=this.buffer.subarray(this.consumed);let r=0;const i=e=>t.length<(r=e);if("number"==typeof this.need){if(i(this.need))return;e=t.subarray(0,r)}else if(this.need===Bt){if(i(4))return;e=t[0]<<24|t[1]<<16|t[2]<<8|t[3]}else if(this.need===kt){if(i(2))return;e=t[0]<<8|t[1]}else if(this.need===Ct){if(i(1))return;e=t[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(i(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(t.subarray(0,r)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(i(this.need.byteLength))return;new Uint8Array(this.need).set(t.subarray(0,r)),e=this.need}return this.consumed+=r,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise((e=>this.pull=e));this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){const t=this.buffer.length,r=t+e;if(r<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,r);else{const e=new Uint8Array(r);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,r)}return this.buffer=new Uint8Array(e),this.buffer}}Dt.U32=Bt,Dt.U16=kt,Dt.U8=Ct;class Pt{constructor(e){this.log=function(t){if(e._opt.debug&&e._opt.debugLevel==v){const o=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];console.log(`EasyPro${o}[✅✅✅][${t}]`,...i)}},this.warn=function(t){if(e._opt.debug&&(e._opt.debugLevel==v||e._opt.debugLevel==b)){const o=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];console.log(`EasyPro${o}[❗❗❗][${t}]`,...i)}},this.error=function(t){const r=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];console.error(`EasyPro${r}[❌❌❌][${t}]`,...n)}}}class Ft{static _ebsp2rbsp(e){let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)}static parseSPS(e){let t=Ft._ebsp2rbsp(e),r=new it(t);r.readByte();let i=r.readByte();r.readByte();let n=r.readByte();r.readUEG();let o=Ft.getProfileString(i),s=Ft.getLevelString(n),a=1,d=420,l=[0,420,422,444],u=8;if((100===i||110===i||122===i||244===i||44===i||83===i||86===i||118===i||128===i||138===i||144===i)&&(a=r.readUEG(),3===a&&r.readBits(1),a<=3&&(d=l[a]),u=r.readUEG()+8,r.readUEG(),r.readBits(1),r.readBool())){let e=3!==a?8:12;for(let t=0;t<e;t++)r.readBool()&&(t<6?Ft._skipScalingList(r,16):Ft._skipScalingList(r,64))}r.readUEG();let c=r.readUEG();if(0===c)r.readUEG();else if(1===c){r.readBits(1),r.readSEG(),r.readSEG();let e=r.readUEG();for(let t=0;t<e;t++)r.readSEG()}let f=r.readUEG();r.readBits(1);let h=r.readUEG(),p=r.readUEG(),m=r.readBits(1);0===m&&r.readBits(1),r.readBits(1);let _=0,g=0,y=0,v=0;r.readBool()&&(_=r.readUEG(),g=r.readUEG(),y=r.readUEG(),v=r.readUEG());let b=1,w=1,S=0,E=!0,A=0,U=0;if(r.readBool()){if(r.readBool()){let e=r.readByte();e>0&&e<16?(b=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],w=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(b=r.readByte()<<8|r.readByte(),w=r.readByte()<<8|r.readByte())}if(r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(4),r.readBool()&&r.readBits(24)),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool()){let e=r.readBits(32),t=r.readBits(32);E=r.readBool(),A=t,U=2*e,S=A/U}}let T=1;1===b&&1===w||(T=b/w);let x=0,B=0;if(0===a)x=1,B=2-m;else{x=3===a?1:2,B=(1===a?2:1)*(2-m)}let k=16*(h+1),C=16*(p+1)*(2-m);k-=(_+g)*x,C-=(y+v)*B;let D=Math.ceil(k*T);return r.destroy(),r=null,{profile_string:o,level_string:s,bit_depth:u,ref_frames:f,chroma_format:d,chroma_format_string:Ft.getChromaFormatString(d),frame_rate:{fixed:E,fps:S,fps_den:U,fps_num:A},sar_ratio:{width:b,height:w},codec_size:{width:k,height:C},present_size:{width:D,height:C}}}static parseSPS$2(e){let t=e.subarray(1,4),r="avc1.";for(let e=0;e<3;e++){let i=t[e].toString(16);i.length<2&&(i="0"+i),r+=i}let i=Ft._ebsp2rbsp(e),n=new it(i);n.readByte();let o=n.readByte();n.readByte();let s=n.readByte();n.readUEG();let a=Ft.getProfileString(o),d=Ft.getLevelString(s),l=1,u=420,c=[0,420,422,444],f=8,h=8;if((100===o||110===o||122===o||244===o||44===o||83===o||86===o||118===o||128===o||138===o||144===o)&&(l=n.readUEG(),3===l&&n.readBits(1),l<=3&&(u=c[l]),f=n.readUEG()+8,h=n.readUEG()+8,n.readBits(1),n.readBool())){let e=3!==l?8:12;for(let t=0;t<e;t++)n.readBool()&&(t<6?Ft._skipScalingList(n,16):Ft._skipScalingList(n,64))}n.readUEG();let p=n.readUEG();if(0===p)n.readUEG();else if(1===p){n.readBits(1),n.readSEG(),n.readSEG();let e=n.readUEG();for(let t=0;t<e;t++)n.readSEG()}let m=n.readUEG();n.readBits(1);let _=n.readUEG(),g=n.readUEG(),y=n.readBits(1);0===y&&n.readBits(1),n.readBits(1);let v=0,b=0,w=0,S=0;n.readBool()&&(v=n.readUEG(),b=n.readUEG(),w=n.readUEG(),S=n.readUEG());let E=1,A=1,U=0,T=!0,x=0,B=0;if(n.readBool()){if(n.readBool()){let e=n.readByte();e>0&&e<16?(E=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],A=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(E=n.readByte()<<8|n.readByte(),A=n.readByte()<<8|n.readByte())}if(n.readBool()&&n.readBool(),n.readBool()&&(n.readBits(4),n.readBool()&&n.readBits(24)),n.readBool()&&(n.readUEG(),n.readUEG()),n.readBool()){let e=n.readBits(32),t=n.readBits(32);T=n.readBool(),x=t,B=2*e,U=x/B}}let k=1;1===E&&1===A||(k=E/A);let C=0,D=0;if(0===l)C=1,D=2-y;else{C=3===l?1:2,D=(1===l?2:1)*(2-y)}let P=16*(_+1),F=16*(g+1)*(2-y);P-=(v+b)*C,F-=(w+S)*D;let I=Math.ceil(P*k);return n.destroy(),n=null,{codec_mimetype:r,profile_idc:o,level_idc:s,profile_string:a,level_string:d,chroma_format_idc:l,bit_depth:f,bit_depth_luma:f,bit_depth_chroma:h,ref_frames:m,chroma_format:u,chroma_format_string:Ft.getChromaFormatString(u),frame_rate:{fixed:T,fps:U,fps_den:B,fps_num:x},sar_ratio:{width:E,height:A},codec_size:{width:P,height:F},present_size:{width:I,height:F}}}static _skipScalingList(e,t){let r=8,i=8,n=0;for(let o=0;o<t;o++)0!==i&&(n=e.readSEG(),i=(r+n+256)%256),r=0===i?r:i}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class It{constructor(e){this.buffer=e,this.buflen=e.length,this.bufpos=0,this.bufoff=0,this.iserro=!1}read(e){let t=0,r=0;for(;e;){if(e<0||this.bufpos>=this.buflen)return this.iserro=!0,0;this.iserro=!1,r=this.bufoff+e>8?8-this.bufoff:e,t<<=r,t+=this.buffer[this.bufpos]>>8-this.bufoff-r&255>>8-r,this.bufoff+=r,e-=r,8==this.bufoff&&(this.bufpos++,this.bufoff=0)}return t}look(e){let t=this.bufpos,r=this.bufoff,i=this.read(e);return this.bufpos=t,this.bufoff=r,i}read_golomb(){let e;for(e=0;0===this.read(1)&&!this.iserro;e++);return(1<<e)+this.read(e)-1}}function Lt(e){const t={};let r=function(){let e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}();const i=new DataView(e.buffer);let n=i.getUint8(0),o=i.getUint8(1);if(i.getUint8(2),i.getUint8(3),1!==n||0===o)return{};const s=1+(3&i.getUint8(4));if(3!==s&&4!==s)return{};let a=31&i.getUint8(5);if(0===a)return{};let d=6;for(let n=0;n<a;n++){let o=i.getUint16(d,!r);if(d+=2,0===o)continue;let s=new Uint8Array(e.buffer,d,o);d+=o;let a=Ft.parseSPS(s);if(0!==n)continue;t.sps=s,t.timescale=1e3,t.codecWidth=a.codec_size.width,t.codecHeight=a.codec_size.height,t.presentWidth=a.present_size.width,t.presentHeight=a.present_size.height,t.profile=a.profile_string,t.level=a.level_string,t.bitDepth=a.bit_depth,t.chromaFormat=a.chroma_format,t.sarRatio=a.sar_ratio,t.frameRate=a.frame_rate,!1!==a.frame_rate.fixed&&0!==a.frame_rate.fps_num&&0!==a.frame_rate.fps_den||(t.frameRate={fixed:!0,fps:25,fps_num:25e3,fps_den:1e3});let l=t.frameRate.fps_den,u=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(l/u);let c=s.subarray(1,4),f="avc1.";for(let e=0;e<3;e++){let t=c[e].toString(16);t.length<2&&(t="0"+t),f+=t}t.codec=f}let l=i.getUint8(d);if(0===l)return{};d++;for(let n=0;n<l;n++){let n=i.getUint16(d,!r);if(d+=2,0===n)continue;let o=new Uint8Array(e.buffer,d,n);d+=n,t.pps=o}if(t.videoType=$e.h264,t.sps){const e=t.sps.byteLength,r=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),i=new Uint8Array(e+4);i.set(r,0),i.set(t.sps,4),t.sps=i}if(t.pps){const e=t.pps.byteLength,r=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),i=new Uint8Array(e+4);i.set(r,0),i.set(t.pps,4),t.pps=i}return t}function Mt(e){let{sps:t,pps:r}=e;const i=[23,0,0,0,0,1,66,0,30,255];i[0]=23,i[6]=t[1],i[7]=t[2],i[8]=t[3],i[10]=225,i[11]=t.byteLength>>8&255,i[12]=255&t.byteLength,i.push(...t,1,r.byteLength>>8&255,255&r.byteLength,...r);return new Uint8Array(i)}function Rt(e,t){let r=[];r[0]=t?23:39,r[1]=1,r[2]=0,r[3]=0,r[4]=0;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function Nt(e){return 31&e[0]}function zt(e){return e===De.kSliceSEI}function Ot(e){return!function(e){return e===De.sps||e===De.pps}(e)&&!zt(e)}function Gt(e){return e===De.iFrame}class $t{constructor(e){this.data=e,this.eofFlag=!1,this.currentStartcodeOffset=this.findNextStartCodeOffset(0),this.eofFlag&&console.error("Could not find H264 startcode until payload end!")}findNextStartCodeOffset(e){let t=e,r=this.data;for(;;){if(t+3>=r.byteLength)return this.eofFlag=!0,r.byteLength;let e=r[t+0]<<24|r[t+1]<<16|r[t+2]<<8|r[t+3],i=r[t+0]<<16|r[t+1]<<8|r[t+2];if(1===e||1===i)return t;t++}}readNextNaluPayload(){let e=this.data,t=null;for(;null==t&&!this.eofFlag;){let r=this.currentStartcodeOffset;r+=1===(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3])?4:3;let i=31&e[r],n=(128&e[r])>>>7,o=this.findNextStartCodeOffset(r);this.currentStartcodeOffset=o,i>=De.kReserved0||0===n&&(t={type:i,data:e.subarray(r,o)})}return t}}class Ht{constructor(e){let t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)}}const Vt=e=>{let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)},Wt=e=>{switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}},jt=e=>{let t=Vt(e),r=new it(t);r.readByte(),r.readByte();let i=0,n=0,o=0,s=0;r.readBits(4);let a=r.readBits(3);r.readBool();let d=r.readBits(2),l=r.readBool(),u=r.readBits(5),c=r.readByte(),f=r.readByte(),h=r.readByte(),p=r.readByte(),m=r.readByte(),_=r.readByte(),g=r.readByte(),y=r.readByte(),v=r.readByte(),b=r.readByte(),w=r.readByte(),S=[],E=[];for(let e=0;e<a;e++)S.push(r.readBool()),E.push(r.readBool());if(a>0)for(let e=a;e<8;e++)r.readBits(2);for(let e=0;e<a;e++)S[e]&&(r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte()),S[e]&&r.readByte();r.readUEG();let A=r.readUEG();3==A&&r.readBits(1);let U=r.readUEG(),T=r.readUEG();r.readBool()&&(i+=r.readUEG(),n+=r.readUEG(),o+=r.readUEG(),s+=r.readUEG());let x=r.readUEG(),B=r.readUEG(),k=r.readUEG();for(let e=r.readBool()?0:a;e<=a;e++)r.readUEG(),r.readUEG(),r.readUEG();if(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readBool()){if(r.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++){if(r.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&r.readSEG();for(let e=0;e<t;e++)r.readSEG()}else r.readUEG()}}r.readBool(),r.readBool(),r.readBool()&&(r.readByte(),r.readUEG(),r.readUEG(),r.readBool());let C=r.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=r.readBool()),t){e===C&&r.readUEG(),r.readBool(),r.readUEG();let t=0;for(let e=0;e<=D;e++){let e=r.readBool(),i=!1;e||(i=r.readBool()),(e||i)&&t++}D=t}else{let e=r.readUEG(),t=r.readUEG();D=e+t;for(let t=0;t<e;t++)r.readUEG(),r.readBool();for(let e=0;e<t;e++)r.readUEG(),r.readBool()}}if(r.readBool()){let e=r.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)r.readBits(1);r.readBits(1)}}let P=!1,F=0,I=1,L=1,M=!1,R=1,N=1;if(r.readBool(),r.readBool(),r.readBool()){if(r.readBool()){let e=r.readByte();e>0&&e<16?(I=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],L=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(I=r.readBits(16),L=r.readBits(16))}if(r.readBool()&&r.readBool(),r.readBool()){r.readBits(3),r.readBool(),r.readBool()&&(r.readByte(),r.readByte(),r.readByte())}if(r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool(),r.readBool(),r.readBool(),P=r.readBool(),P&&(i+=r.readUEG(),n+=r.readUEG(),o+=r.readUEG(),s+=r.readUEG()),r.readBool()){if(R=r.readBits(32),N=r.readBits(32),r.readBool()){if(r.readUEG(),r.readBool()){let e=!1,t=!1,i=!1;e=r.readBool(),t=r.readBool(),(e||t)&&(i=r.readBool(),i&&(r.readByte(),r.readBits(5),r.readBool(),r.readBits(5)),r.readBits(4),r.readBits(4),i&&r.readBits(4),r.readBits(5),r.readBits(5),r.readBits(5));for(let n=0;n<=a;n++){let n=r.readBool();M=n;let o=!1,s=1;n||(o=r.readBool());let a=!1;if(o?r.readSEG():a=r.readBool(),a||(cpbcnt=r.readUEG()+1),e)for(let e=0;e<s;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());if(t)for(let e=0;e<s;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG())}}}}r.readBool()&&(r.readBool(),r.readBool(),r.readBool(),F=r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG())}r.readBool();let z=`hvc1.${u}.1.L${w}.B0`,O=U,G=T,$=1;return 1!==I&&1!==L&&($=I/L),r.destroy(),r=null,{codec_mimetype:z,level_string:(H=w,(H/30).toFixed(1)),profile_idc:u,bit_depth:x+8,ref_frames:1,chroma_format:A,chroma_format_string:Wt(A),general_level_idc:w,general_profile_space:d,general_tier_flag:l,general_profile_idc:u,general_profile_compatibility_flags_1:c,general_profile_compatibility_flags_2:f,general_profile_compatibility_flags_3:h,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:m,general_constraint_indicator_flags_2:_,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:v,general_constraint_indicator_flags_6:b,min_spatial_segmentation_idc:F,constant_frame_rate:0,chroma_format_idc:A,bit_depth_luma_minus8:x,bit_depth_chroma_minus8:B,frame_rate:{fixed:M,fps:N/R,fps_den:R,fps_num:N},sar_ratio:{width:I,height:L},codec_size:{width:O,height:G},present_size:{width:O*$,height:G}};var H},qt=e=>{let t=Vt(e),r=new it(t);return r.readByte(),r.readByte(),r.readBits(4),r.readBits(2),r.readBits(6),{num_temporal_layers:r.readBits(3)+1,temporal_id_nested:r.readBool()}},Yt=e=>{let t=Vt(e),r=new it(t);r.readByte(),r.readByte(),r.readUEG(),r.readUEG(),r.readBool(),r.readBool(),r.readBits(3),r.readBool(),r.readBool(),r.readUEG(),r.readUEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool()&&r.readUEG(),r.readSEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool(),r.readBool();let i=r.readBool(),n=r.readBool(),o=1;return n&&i?o=0:n?o=3:i&&(o=2),{parallelismType:o}};class Kt{static _ebsp2rbsp(e){let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)}static parseVPS(e){let t=Kt._ebsp2rbsp(e),r=new it(t);return r.readByte(),r.readByte(),r.readBits(4),r.readBits(2),r.readBits(6),{num_temporal_layers:r.readBits(3)+1,temporal_id_nested:r.readBool()}}static parseSPS(e){let t=Kt._ebsp2rbsp(e),r=new it(t);r.readByte(),r.readByte();let i=0,n=0,o=0,s=0;r.readBits(4);let a=r.readBits(3);r.readBool();let d=r.readBits(2),l=r.readBool(),u=r.readBits(5),c=r.readByte(),f=r.readByte(),h=r.readByte(),p=r.readByte(),m=r.readByte(),_=r.readByte(),g=r.readByte(),y=r.readByte(),v=r.readByte(),b=r.readByte(),w=r.readByte(),S=[],E=[];for(let e=0;e<a;e++)S.push(r.readBool()),E.push(r.readBool());if(a>0)for(let e=a;e<8;e++)r.readBits(2);for(let e=0;e<a;e++)S[e]&&(r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte()),E[e]&&r.readByte();r.readUEG();let A=r.readUEG();3==A&&r.readBits(1);let U=r.readUEG(),T=r.readUEG();r.readBool()&&(i+=r.readUEG(),n+=r.readUEG(),o+=r.readUEG(),s+=r.readUEG());let x=r.readUEG(),B=r.readUEG(),k=r.readUEG();for(let e=r.readBool()?0:a;e<=a;e++)r.readUEG(),r.readUEG(),r.readUEG();if(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readBool()){if(r.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++){if(r.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&r.readSEG();for(let e=0;e<t;e++)r.readSEG()}else r.readUEG()}}r.readBool(),r.readBool(),r.readBool()&&(r.readByte(),r.readUEG(),r.readUEG(),r.readBool());let C=r.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=r.readBool()),t){e===C&&r.readUEG(),r.readBool(),r.readUEG();let t=0;for(let e=0;e<=D;e++){let e=r.readBool(),i=!1;e||(i=r.readBool()),(e||i)&&t++}D=t}else{let e=r.readUEG(),t=r.readUEG();D=e+t;for(let t=0;t<e;t++)r.readUEG(),r.readBool();for(let e=0;e<t;e++)r.readUEG(),r.readBool()}}if(r.readBool()){let e=r.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)r.readBits(1);r.readBits(1)}}let P=!1,F=0,I=1,L=1,M=!1,R=1,N=1;if(r.readBool(),r.readBool(),r.readBool()){if(r.readBool()){let e=r.readByte();e>0&&e<=16?(I=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],L=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(I=r.readBits(16),L=r.readBits(16))}if(r.readBool()&&r.readBool(),r.readBool()){r.readBits(3),r.readBool(),r.readBool()&&(r.readByte(),r.readByte(),r.readByte())}if(r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool(),r.readBool(),r.readBool(),P=r.readBool(),P&&(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG()),r.readBool()){if(R=r.readBits(32),N=r.readBits(32),r.readBool()&&r.readUEG(),r.readBool()){let e=!1,t=!1,i=!1;e=r.readBool(),t=r.readBool(),(e||t)&&(i=r.readBool(),i&&(r.readByte(),r.readBits(5),r.readBool(),r.readBits(5)),r.readBits(4),r.readBits(4),i&&r.readBits(4),r.readBits(5),r.readBits(5),r.readBits(5));for(let n=0;n<=a;n++){let n=r.readBool();M=n;let o=!0,s=1;n||(o=r.readBool());let a=!1;if(o?r.readUEG():a=r.readBool(),a||(s=r.readUEG()+1),e){for(let e=0;e<s;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());r.readBool()}if(t){for(let e=0;e<s;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());r.readBool()}}}}r.readBool()&&(r.readBool(),r.readBool(),r.readBool(),F=r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG())}r.readBool();let z=`hvc1.${u}.1.L${w}.B0`,O=U-(i+n)*(1===A||2===A?2:1),G=T-(o+s)*(1===A?2:1),$=1;return 1!==I&&1!==L&&($=I/L),r.destroy(),r=null,{codec_mimetype:z,profile_string:Kt.getProfileString(u),level_string:Kt.getLevelString(w),profile_idc:u,bit_depth:x+8,ref_frames:1,chroma_format:A,chroma_format_string:Kt.getChromaFormatString(A),general_level_idc:w,general_profile_space:d,general_tier_flag:l,general_profile_idc:u,general_profile_compatibility_flags_1:c,general_profile_compatibility_flags_2:f,general_profile_compatibility_flags_3:h,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:m,general_constraint_indicator_flags_2:_,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:v,general_constraint_indicator_flags_6:b,min_spatial_segmentation_idc:F,constant_frame_rate:0,chroma_format_idc:A,bit_depth_luma_minus8:x,bit_depth_chroma_minus8:B,frame_rate:{fixed:M,fps:N/R,fps_den:R,fps_num:N},sar_ratio:{width:I,height:L},codec_size:{width:O,height:G},present_size:{width:O*$,height:G}}}static parsePPS(e){let t=Kt._ebsp2rbsp(e),r=new it(t);r.readByte(),r.readByte(),r.readUEG(),r.readUEG(),r.readBool(),r.readBool(),r.readBits(3),r.readBool(),r.readBool(),r.readUEG(),r.readUEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool()&&r.readUEG(),r.readSEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool(),r.readBool();let i=r.readBool(),n=r.readBool(),o=1;return n&&i?o=0:n?o=3:i&&(o=2),{parallelismType:o}}static getChromaFormatString(e){switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}}static getProfileString(e){switch(e){case 1:return"Main";case 2:return"Main10";case 3:return"MainSP";case 4:return"Rext";case 9:return"SCC";default:return"Unknown"}}static getLevelString(e){return(e/30).toFixed(1)}}function Xt(e){let t={codecWidth:0,codecHeight:0,videoType:$e.h265,width:0,height:0,profile:0,level:0};e=e.slice(5);do{let r={};if(e.length<23){console.warn("parseHEVCDecoderConfigurationRecord$2",`arrayBuffer.length ${e.length} < 23`);break}if(r.configurationVersion=e[0],1!=r.configurationVersion)break;r.general_profile_space=e[1]>>6&3,r.general_tier_flag=e[1]>>5&1,r.general_profile_idc=31&e[1],r.general_profile_compatibility_flags=e[2]<<24|e[3]<<16|e[4]<<8|e[5],r.general_constraint_indicator_flags=e[6]<<24|e[7]<<16|e[8]<<8|e[9],r.general_constraint_indicator_flags=r.general_constraint_indicator_flags<<16|e[10]<<8|e[11],r.general_level_idc=e[12],r.min_spatial_segmentation_idc=(15&e[13])<<8|e[14],r.parallelismType=3&e[15],r.chromaFormat=3&e[16],r.bitDepthLumaMinus8=7&e[17],r.bitDepthChromaMinus8=7&e[18],r.avgFrameRate=e[19]<<8|e[20],r.constantFrameRate=e[21]>>6&3,r.numTemporalLayers=e[21]>>3&7,r.temporalIdNested=e[21]>>2&1,r.lengthSizeMinusOne=3&e[21];let i=e[22],n=e.slice(23);for(let e=0;e<i&&!(n.length<3);e++){let e=63&n[0],i=n[1]<<8|n[2];n=n.slice(3);for(let o=0;o<i&&!(n.length<2);o++){let i=n[0]<<8|n[1];if(n.length<2+i)break;if(n=n.slice(2),33==e){let e=new Uint8Array(i);e.set(n.slice(0,i),0),r.psps=Jt(e,r),t.profile=r.general_profile_idc,t.level=r.general_level_idc/30,t.width=r.psps.pic_width_in_luma_samples-(r.psps.conf_win_left_offset+r.psps.conf_win_right_offset),t.height=r.psps.pic_height_in_luma_samples-(r.psps.conf_win_top_offset+r.psps.conf_win_bottom_offset)}n=n.slice(i)}}}while(0);return t.codecWidth=t.width||1920,t.codecHeight=t.height||1080,t.presentHeight=t.codecHeight,t.presentWidth=t.codecWidth,t.timescale=1e3,t.refSampleDuration=1e3/23976*1e3,t}function Zt(e){const t=e;if(t.length<22)return console.error(`Invalid HEVCDecoderConfigurationRecord, lack of data! ${t.length} < 22`),{};let r={codecWidth:0,codecHeight:0,videoType:$e.h265},i=function(){let e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}(),n=new DataView(t.buffer),o=n.getUint8(0),s=31&n.getUint8(1);if(1!==o||0===s)return console.error(`Invalid HEVCDecoderConfigurationRecord,version is ${o}, hevcProfile is ${s}`),{};let a=1+(3&n.getUint8(21));if(3!==a&&4!==a)return console.error("Invalid HEVCDecoderConfigurationRecord, Strange NaluLengthSizeMinusOne: "+(a-1)),{};let d=n.getUint8(22);for(let e=0,o=23;e<d;e++){let e=63&n.getUint8(o+0),s=n.getUint16(o+1,!i);o+=3;for(let a=0;a<s;a++){let s=n.getUint16(o+0,!i);if(0===a)if(33===e){o+=2;let e=new Uint8Array(t.buffer,o,s),i=Kt.parseSPS(e);r.codecWidth=i.codec_size.width,r.codecHeight=i.codec_size.height,r.presentWidth=i.present_size.width,r.presentHeight=i.present_size.height,r.profile=i.profile_string,r.level=i.level_string,r.bitDepth=i.bit_depth,r.chromaFormat=i.chroma_format,r.sarRatio=i.sar_ratio,r.frameRate=i.frame_rate,!1!==i.frame_rate.fixed&&0!==i.frame_rate.fps_num&&0!==i.frame_rate.fps_den||(r.frameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3}),r.frameRate.fps_den,r.frameRate.fps_num,r.codec=i.codec_mimetype,o+=s}else o+=2+s;else o+=2+s}}return r.hvcc=new Uint8Array(t),r}function Jt(e,t){let r={},i=e.length,n=[],o=new It(e);o.read(1),o.read(6),o.read(6),o.read(3);for(let e=2;e<i;e++)e+2<i&&3==o.look(24)?(n.push(o.read(8)),n.push(o.read(8)),e+=2,o.read(8)):n.push(o.read(8));let s=new Uint8Array(n),a=new It(s);if(r.sps_video_parameter_set_id=a.read(4),r.sps_max_sub_layers_minus1=a.read(3),r.sps_temporal_id_nesting_flag=a.read(1),r.profile_tier_level=function(e,t,r){let i={};i.profile_space=e.read(2),i.tier_flag=e.read(1),i.profile_idc=e.read(5),i.profile_compatibility_flags=e.read(32),i.general_progressive_source_flag=e.read(1),i.general_interlaced_source_flag=e.read(1),i.general_non_packed_constraint_flag=e.read(1),i.general_frame_only_constraint_flag=e.read(1),e.read(32),e.read(12),i.level_idc=e.read(8),i.sub_layer_profile_present_flag=[],i.sub_layer_level_present_flag=[];for(let t=0;t<r;t++)i.sub_layer_profile_present_flag[t]=e.read(1),i.sub_layer_level_present_flag[t]=e.read(1);if(r>0)for(let t=r;t<8;t++)e.read(2);i.sub_layer_profile_space=[],i.sub_layer_tier_flag=[],i.sub_layer_profile_idc=[],i.sub_layer_profile_compatibility_flag=[],i.sub_layer_progressive_source_flag=[],i.sub_layer_interlaced_source_flag=[],i.sub_layer_non_packed_constraint_flag=[],i.sub_layer_frame_only_constraint_flag=[],i.sub_layer_level_idc=[];for(let t=0;t<r;t++)i.sub_layer_profile_present_flag[t]&&(i.sub_layer_profile_space[t]=e.read(2),i.sub_layer_tier_flag[t]=e.read(1),i.sub_layer_profile_idc[t]=e.read(5),i.sub_layer_profile_compatibility_flag[t]=e.read(32),i.sub_layer_progressive_source_flag[t]=e.read(1),i.sub_layer_interlaced_source_flag[t]=e.read(1),i.sub_layer_non_packed_constraint_flag[t]=e.read(1),i.sub_layer_frame_only_constraint_flag[t]=e.read(1),e.read(32),e.read(12)),i.sub_layer_level_present_flag[t]?i.sub_layer_level_idc[t]=e.read(8):i.sub_layer_level_idc[t]=1;return i}(a,0,r.sps_max_sub_layers_minus1),r.sps_seq_parameter_set_id=a.read_golomb(),r.chroma_format_idc=a.read_golomb(),3==r.chroma_format_idc?r.separate_colour_plane_flag=a.read(1):r.separate_colour_plane_flag=0,r.pic_width_in_luma_samples=a.read_golomb(),r.pic_height_in_luma_samples=a.read_golomb(),r.conformance_window_flag=a.read(1),r.conformance_window_flag){let e=1+(r.chroma_format_idc<2),t=1+(r.chroma_format_idc<3);r.conf_win_left_offset=a.read_golomb()*t,r.conf_win_right_offset=a.read_golomb()*t,r.conf_win_top_offset=a.read_golomb()*e,r.conf_win_bottom_offset=a.read_golomb()*e}else r.conf_win_left_offset=0,r.conf_win_right_offset=0,r.conf_win_top_offset=0,r.conf_win_bottom_offset=0;return r}function Qt(e){let{vps:t,pps:r,sps:i}=e,n={configurationVersion:1};const o=qt(t),s=jt(i),a=Yt(r);n=Object.assign(n,o,s,a);let d=23+(5+t.byteLength)+(5+i.byteLength)+(5+r.byteLength),l=new Uint8Array(d);l[0]=1,l[1]=(3&n.general_profile_space)<<6|(n.general_tier_flag?1:0)<<5|31&n.general_profile_idc,l[2]=n.general_profile_compatibility_flags_1||0,l[3]=n.general_profile_compatibility_flags_2||0,l[4]=n.general_profile_compatibility_flags_3||0,l[5]=n.general_profile_compatibility_flags_4||0,l[6]=n.general_constraint_indicator_flags_1||0,l[7]=n.general_constraint_indicator_flags_2||0,l[8]=n.general_constraint_indicator_flags_3||0,l[9]=n.general_constraint_indicator_flags_4||0,l[10]=n.general_constraint_indicator_flags_5||0,l[11]=n.general_constraint_indicator_flags_6||0,l[12]=60,l[13]=240|(3840&n.min_spatial_segmentation_idc)>>8,l[14]=255&n.min_spatial_segmentation_idc,l[15]=252|3&n.parallelismType,l[16]=252|3&n.chroma_format_idc,l[17]=248|7&n.bit_depth_luma_minus8,l[18]=248|7&n.bit_depth_chroma_minus8,l[19]=0,l[20]=0,l[21]=(3&n.constant_frame_rate)<<6|(7&n.num_temporal_layers)<<3|(n.temporal_id_nested?1:0)<<2|3,l[22]=3,l[23]=128|Pe.vps,l[24]=0,l[25]=1,l[26]=(65280&t.byteLength)>>8,l[27]=(255&t.byteLength)>>0,l.set(t,28),l[23+(5+t.byteLength)+0]=128|Pe.sps,l[23+(5+t.byteLength)+1]=0,l[23+(5+t.byteLength)+2]=1,l[23+(5+t.byteLength)+3]=(65280&i.byteLength)>>8,l[23+(5+t.byteLength)+4]=(255&i.byteLength)>>0,l.set(i,23+(5+t.byteLength)+5),l[23+(5+t.byteLength+5+i.byteLength)+0]=128|Pe.pps,l[23+(5+t.byteLength+5+i.byteLength)+1]=0,l[23+(5+t.byteLength+5+i.byteLength)+2]=1,l[23+(5+t.byteLength+5+i.byteLength)+3]=(65280&r.byteLength)>>8,l[23+(5+t.byteLength+5+i.byteLength)+4]=(255&r.byteLength)>>0,l.set(r,23+(5+t.byteLength+5+i.byteLength)+5);const u=[28,0,0,0,0],c=new Uint8Array(u.length+l.byteLength);return c.set(u,0),c.set(l,u.length),c}function er(e,t){let r=[];r[0]=t?28:44,r[1]=1,r[2]=0,r[3]=0,r[4]=0;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function tr(e){return(126&e[0])>>1}function rr(e){return!function(e){return e>=32&&e<=40}(e)}function ir(e){return e>=16&&e<=21}class nr{constructor(e){this.data=e,this.eofFlag=!1,this.currentStartcodeOffset=this.findNextStartCodeOffset(0),this.eofFlag&&console.error("Could not find H265 startcode until payload end!")}findNextStartCodeOffset(e){let t=e,r=this.data;for(;;){if(t+3>=r.byteLength)return this.eofFlag=!0,r.byteLength;let e=r[t+0]<<24|r[t+1]<<16|r[t+2]<<8|r[t+3],i=r[t+0]<<16|r[t+1]<<8|r[t+2];if(1===e||1===i)return t;t++}}readNextNaluPayload(){let e=this.data,t=null;for(;null==t&&!this.eofFlag;){let r=this.currentStartcodeOffset;r+=1===(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3])?4:3;let i=e[r]>>1&63,n=(128&e[r])>>>7,o=this.findNextStartCodeOffset(r);this.currentStartcodeOffset=o,0===n&&(t={type:i,data:e.subarray(r,o)})}return t}}class or{constructor(e){let t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)}}function sr(e){return parseInt(e)===e}function ar(e){if(!sr(e.length))return!1;for(var t=0;t<e.length;t++)if(!sr(e[t])||e[t]<0||e[t]>255)return!1;return!0}function dr(e,t){if(e.buffer&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!ar(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(sr(e.length)&&ar(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function lr(e){return new Uint8Array(e)}function ur(e,t,r,i,n){null==i&&null==n||(e=e.slice?e.slice(i,n):Array.prototype.slice.call(e,i,n)),t.set(e,r)}var cr,fr={toBytes:function(e){var t=[],r=0;for(e=encodeURI(e);r<e.length;){var i=e.charCodeAt(r++);37===i?(t.push(parseInt(e.substr(r,2),16)),r+=2):t.push(i)}return dr(t)},fromBytes:function(e){for(var t=[],r=0;r<e.length;){var i=e[r];i<128?(t.push(String.fromCharCode(i)),r++):i>191&&i<224?(t.push(String.fromCharCode((31&i)<<6|63&e[r+1])),r+=2):(t.push(String.fromCharCode((15&i)<<12|(63&e[r+1])<<6|63&e[r+2])),r+=3)}return t.join("")}},hr=(cr="0123456789abcdef",{toBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},fromBytes:function(e){for(var t=[],r=0;r<e.length;r++){var i=e[r];t.push(cr[(240&i)>>4]+cr[15&i])}return t.join("")}}),pr={16:10,24:12,32:14},mr=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],_r=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],gr=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],yr=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],vr=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],br=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],wr=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],Sr=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],Er=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],Ar=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],Ur=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],Tr=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],xr=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],Br=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],kr=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function Cr(e){for(var t=[],r=0;r<e.length;r+=4)t.push(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]);return t}var Dr=function(e){if(!(this instanceof Dr))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:dr(e,!0)}),this._prepare()};Dr.prototype._prepare=function(){var e=pr[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);var r,i=4*(e+1),n=this.key.length/4,o=Cr(this.key);for(t=0;t<n;t++)r=t>>2,this._Ke[r][t%4]=o[t],this._Kd[e-r][t%4]=o[t];for(var s,a=0,d=n;d<i;){if(s=o[n-1],o[0]^=_r[s>>16&255]<<24^_r[s>>8&255]<<16^_r[255&s]<<8^_r[s>>24&255]^mr[a]<<24,a+=1,8!=n)for(t=1;t<n;t++)o[t]^=o[t-1];else{for(t=1;t<n/2;t++)o[t]^=o[t-1];s=o[n/2-1],o[n/2]^=_r[255&s]^_r[s>>8&255]<<8^_r[s>>16&255]<<16^_r[s>>24&255]<<24;for(t=n/2+1;t<n;t++)o[t]^=o[t-1]}for(t=0;t<n&&d<i;)l=d>>2,u=d%4,this._Ke[l][u]=o[t],this._Kd[e-l][u]=o[t++],d++}for(var l=1;l<e;l++)for(var u=0;u<4;u++)s=this._Kd[l][u],this._Kd[l][u]=Tr[s>>24&255]^xr[s>>16&255]^Br[s>>8&255]^kr[255&s]},Dr.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,r=[0,0,0,0],i=Cr(e),n=0;n<4;n++)i[n]^=this._Ke[0][n];for(var o=1;o<t;o++){for(n=0;n<4;n++)r[n]=yr[i[n]>>24&255]^vr[i[(n+1)%4]>>16&255]^br[i[(n+2)%4]>>8&255]^wr[255&i[(n+3)%4]]^this._Ke[o][n];i=r.slice()}var s,a=lr(16);for(n=0;n<4;n++)s=this._Ke[t][n],a[4*n]=255&(_r[i[n]>>24&255]^s>>24),a[4*n+1]=255&(_r[i[(n+1)%4]>>16&255]^s>>16),a[4*n+2]=255&(_r[i[(n+2)%4]>>8&255]^s>>8),a[4*n+3]=255&(_r[255&i[(n+3)%4]]^s);return a},Dr.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,r=[0,0,0,0],i=Cr(e),n=0;n<4;n++)i[n]^=this._Kd[0][n];for(var o=1;o<t;o++){for(n=0;n<4;n++)r[n]=Sr[i[n]>>24&255]^Er[i[(n+3)%4]>>16&255]^Ar[i[(n+2)%4]>>8&255]^Ur[255&i[(n+1)%4]]^this._Kd[o][n];i=r.slice()}var s,a=lr(16);for(n=0;n<4;n++)s=this._Kd[t][n],a[4*n]=255&(gr[i[n]>>24&255]^s>>24),a[4*n+1]=255&(gr[i[(n+3)%4]>>16&255]^s>>16),a[4*n+2]=255&(gr[i[(n+2)%4]>>8&255]^s>>8),a[4*n+3]=255&(gr[255&i[(n+1)%4]]^s);return a};var Pr=function(e){if(!(this instanceof Pr))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new Dr(e)};Pr.prototype.encrypt=function(e){if((e=dr(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=lr(e.length),r=lr(16),i=0;i<e.length;i+=16)ur(e,r,0,i,i+16),ur(r=this._aes.encrypt(r),t,i);return t},Pr.prototype.decrypt=function(e){if((e=dr(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=lr(e.length),r=lr(16),i=0;i<e.length;i+=16)ur(e,r,0,i,i+16),ur(r=this._aes.decrypt(r),t,i);return t};var Fr=function(e,t){if(!(this instanceof Fr))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=lr(16);this._lastCipherblock=dr(t,!0),this._aes=new Dr(e)};Fr.prototype.encrypt=function(e){if((e=dr(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=lr(e.length),r=lr(16),i=0;i<e.length;i+=16){ur(e,r,0,i,i+16);for(var n=0;n<16;n++)r[n]^=this._lastCipherblock[n];this._lastCipherblock=this._aes.encrypt(r),ur(this._lastCipherblock,t,i)}return t},Fr.prototype.decrypt=function(e){if((e=dr(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=lr(e.length),r=lr(16),i=0;i<e.length;i+=16){ur(e,r,0,i,i+16),r=this._aes.decrypt(r);for(var n=0;n<16;n++)t[i+n]=r[n]^this._lastCipherblock[n];ur(e,this._lastCipherblock,0,i,i+16)}return t};var Ir=function(e,t,r){if(!(this instanceof Ir))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=lr(16);r||(r=1),this.segmentSize=r,this._shiftRegister=dr(t,!0),this._aes=new Dr(e)};Ir.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t,r=dr(e,!0),i=0;i<r.length;i+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var n=0;n<this.segmentSize;n++)r[i+n]^=t[n];ur(this._shiftRegister,this._shiftRegister,0,this.segmentSize),ur(r,this._shiftRegister,16-this.segmentSize,i,i+this.segmentSize)}return r},Ir.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t,r=dr(e,!0),i=0;i<r.length;i+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var n=0;n<this.segmentSize;n++)r[i+n]^=t[n];ur(this._shiftRegister,this._shiftRegister,0,this.segmentSize),ur(e,this._shiftRegister,16-this.segmentSize,i,i+this.segmentSize)}return r};var Lr=function(e,t){if(!(this instanceof Lr))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=lr(16);this._lastPrecipher=dr(t,!0),this._lastPrecipherIndex=16,this._aes=new Dr(e)};Lr.prototype.encrypt=function(e){for(var t=dr(e,!0),r=0;r<t.length;r++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[r]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},Lr.prototype.decrypt=Lr.prototype.encrypt;var Mr=function(e){if(!(this instanceof Mr))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=lr(16),this.setValue(e)):this.setBytes(e)};Mr.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)},Mr.prototype.setBytes=function(e){if(16!=(e=dr(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},Mr.prototype.increment=function(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};var Rr=function(e,t){if(!(this instanceof Rr))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof Mr||(t=new Mr(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new Dr(e)};Rr.prototype.encrypt=function(e){for(var t=dr(e,!0),r=0;r<t.length;r++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[r]^=this._remainingCounter[this._remainingCounterIndex++];return t},Rr.prototype.decrypt=Rr.prototype.encrypt;const Nr={AES:Dr,Counter:Mr,ModeOfOperation:{ecb:Pr,cbc:Fr,cfb:Ir,ofb:Lr,ctr:Rr},utils:{hex:hr,utf8:fr},padding:{pkcs7:{pad:function(e){var t=16-(e=dr(e,!0)).length%16,r=lr(e.length+t);ur(e,r);for(var i=e.length;i<r.length;i++)r[i]=t;return r},strip:function(e){if((e=dr(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var r=e.length-t,i=0;i<t;i++)if(e[r+i]!==t)throw new Error("PKCS#7 invalid padding byte");var n=lr(r);return ur(e,n,0,0,r),n}}},_arrayTest:{coerceArray:dr,createArray:lr,copyArray:ur}};var zr=rt((function(e,t){var r,n,o,s=(r=new Date,n=4,o={setLogLevel:function(e){n=e==this.debug?1:e==this.info?2:e==this.warn?3:(this.error,4)},debug:function(e,t){void 0===console.debug&&(console.debug=console.log),1>=n&&console.debug("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},log:function(e,t){this.debug(e.msg)},info:function(e,t){2>=n&&console.info("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},warn:function(e,t){3>=n&&console.warn("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},error:function(e,t){4>=n&&console.error("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)}},o);s.getDurationString=function(e,t){var r;function i(e,t){for(var r=(""+e).split(".");r[0].length<t;)r[0]="0"+r[0];return r.join(".")}e<0?(r=!0,e=-e):r=!1;var n=e/(t||1),o=Math.floor(n/3600);n-=3600*o;var s=Math.floor(n/60),a=1e3*(n-=60*s);return a-=1e3*(n=Math.floor(n)),a=Math.floor(a),(r?"-":"")+o+":"+i(s,2)+":"+i(n,2)+"."+i(a,3)},s.printRanges=function(e){var t=e.length;if(t>0){for(var r="",i=0;i<t;i++)i>0&&(r+=","),r+="["+s.getDurationString(e.start(i))+","+s.getDurationString(e.end(i))+"]";return r}return"(empty)"},t.Log=s;var a=function(e){if(!(e instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=e,this.dataview=new DataView(e),this.position=0};a.prototype.getPosition=function(){return this.position},a.prototype.getEndPosition=function(){return this.buffer.byteLength},a.prototype.getLength=function(){return this.buffer.byteLength},a.prototype.seek=function(e){var t=Math.max(0,Math.min(this.buffer.byteLength,e));return this.position=isNaN(t)||!isFinite(t)?0:t,!0},a.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},a.prototype.readAnyInt=function(e,t){var r=0;if(this.position+e<=this.buffer.byteLength){switch(e){case 1:r=t?this.dataview.getInt8(this.position):this.dataview.getUint8(this.position);break;case 2:r=t?this.dataview.getInt16(this.position):this.dataview.getUint16(this.position);break;case 3:if(t)throw"No method for reading signed 24 bits values";r=this.dataview.getUint8(this.position)<<16,r|=this.dataview.getUint8(this.position+1)<<8,r|=this.dataview.getUint8(this.position+2);break;case 4:r=t?this.dataview.getInt32(this.position):this.dataview.getUint32(this.position);break;case 8:if(t)throw"No method for reading signed 64 bits values";r=this.dataview.getUint32(this.position)<<32,r|=this.dataview.getUint32(this.position+4);break;default:throw"readInt method not implemented for size: "+e}return this.position+=e,r}throw"Not enough bytes in buffer"},a.prototype.readUint8=function(){return this.readAnyInt(1,!1)},a.prototype.readUint16=function(){return this.readAnyInt(2,!1)},a.prototype.readUint24=function(){return this.readAnyInt(3,!1)},a.prototype.readUint32=function(){return this.readAnyInt(4,!1)},a.prototype.readUint64=function(){return this.readAnyInt(8,!1)},a.prototype.readString=function(e){if(this.position+e<=this.buffer.byteLength){for(var t="",r=0;r<e;r++)t+=String.fromCharCode(this.readUint8());return t}throw"Not enough bytes in buffer"},a.prototype.readCString=function(){for(var e=[];;){var t=this.readUint8();if(0===t)break;e.push(t)}return String.fromCharCode.apply(null,e)},a.prototype.readInt8=function(){return this.readAnyInt(1,!0)},a.prototype.readInt16=function(){return this.readAnyInt(2,!0)},a.prototype.readInt32=function(){return this.readAnyInt(4,!0)},a.prototype.readInt64=function(){return this.readAnyInt(8,!1)},a.prototype.readUint8Array=function(e){for(var t=new Uint8Array(e),r=0;r<e;r++)t[r]=this.readUint8();return t},a.prototype.readInt16Array=function(e){for(var t=new Int16Array(e),r=0;r<e;r++)t[r]=this.readInt16();return t},a.prototype.readUint16Array=function(e){for(var t=new Int16Array(e),r=0;r<e;r++)t[r]=this.readUint16();return t},a.prototype.readUint32Array=function(e){for(var t=new Uint32Array(e),r=0;r<e;r++)t[r]=this.readUint32();return t},a.prototype.readInt32Array=function(e){for(var t=new Int32Array(e),r=0;r<e;r++)t[r]=this.readInt32();return t},t.MP4BoxStream=a;var d=function(e,t,r){this._byteOffset=t||0,e instanceof ArrayBuffer?this.buffer=e:"object"==typeof e?(this.dataView=e,t&&(this._byteOffset+=t)):this.buffer=new ArrayBuffer(e||0),this.position=0,this.endianness=null==r?d.LITTLE_ENDIAN:r};d.prototype={},d.prototype.getPosition=function(){return this.position},d.prototype._realloc=function(e){if(this._dynamicSize){var t=this._byteOffset+this.position+e,r=this._buffer.byteLength;if(t<=r)t>this._byteLength&&(this._byteLength=t);else{for(r<1&&(r=1);t>r;)r*=2;var i=new ArrayBuffer(r),n=new Uint8Array(this._buffer);new Uint8Array(i,0,n.length).set(n),this.buffer=i,this._byteLength=t}}},d.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var e=new ArrayBuffer(this._byteLength),t=new Uint8Array(e),r=new Uint8Array(this._buffer,0,t.length);t.set(r),this.buffer=e}},d.BIG_ENDIAN=!1,d.LITTLE_ENDIAN=!0,d.prototype._byteLength=0,Object.defineProperty(d.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(d.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(e){this._buffer=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(d.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(e){this._byteOffset=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(d.prototype,"dataView",{get:function(){return this._dataView},set:function(e){this._byteOffset=e.byteOffset,this._buffer=e.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+e.byteLength}}),d.prototype.seek=function(e){var t=Math.max(0,Math.min(this.byteLength,e));this.position=isNaN(t)||!isFinite(t)?0:t},d.prototype.isEof=function(){return this.position>=this._byteLength},d.prototype.mapUint8Array=function(e){this._realloc(1*e);var t=new Uint8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},d.prototype.readInt32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Int32Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readInt16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var r=new Int16Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readInt8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Int8Array(e);return d.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},d.prototype.readUint32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Uint32Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readUint16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var r=new Uint16Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readUint8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Uint8Array(e);return d.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},d.prototype.readFloat64Array=function(e,t){e=null==e?this.byteLength-this.position/8:e;var r=new Float64Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readFloat32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Float32Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readInt32=function(e){var t=this._dataView.getInt32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readInt16=function(e){var t=this._dataView.getInt16(this.position,null==e?this.endianness:e);return this.position+=2,t},d.prototype.readInt8=function(){var e=this._dataView.getInt8(this.position);return this.position+=1,e},d.prototype.readUint32=function(e){var t=this._dataView.getUint32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readUint16=function(e){var t=this._dataView.getUint16(this.position,null==e?this.endianness:e);return this.position+=2,t},d.prototype.readUint8=function(){var e=this._dataView.getUint8(this.position);return this.position+=1,e},d.prototype.readFloat32=function(e){var t=this._dataView.getFloat32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readFloat64=function(e){var t=this._dataView.getFloat64(this.position,null==e?this.endianness:e);return this.position+=8,t},d.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,d.memcpy=function(e,t,r,i,n){var o=new Uint8Array(e,t,n),s=new Uint8Array(r,i,n);o.set(s)},d.arrayToNative=function(e,t){return t==this.endianness?e:this.flipArrayEndianness(e)},d.nativeToEndian=function(e,t){return this.endianness==t?e:this.flipArrayEndianness(e)},d.flipArrayEndianness=function(e){for(var t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),r=0;r<e.byteLength;r+=e.BYTES_PER_ELEMENT)for(var i=r+e.BYTES_PER_ELEMENT-1,n=r;i>n;i--,n++){var o=t[n];t[n]=t[i],t[i]=o}return e},d.prototype.failurePosition=0,String.fromCharCodeUint8=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e[r];return String.fromCharCode.apply(null,t)},d.prototype.readString=function(e,t){return null==t||"ASCII"==t?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==e?this.byteLength-this.position:e)]):new TextDecoder(t).decode(this.mapUint8Array(e))},d.prototype.readCString=function(e){var t=this.byteLength-this.position,r=new Uint8Array(this._buffer,this._byteOffset+this.position),i=t;null!=e&&(i=Math.min(e,t));for(var n=0;n<i&&0!==r[n];n++);var o=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(n)]);return null!=e?this.position+=i-n:n!=t&&(this.position+=1),o};var l=Math.pow(2,32);d.prototype.readInt64=function(){return this.readInt32()*l+this.readUint32()},d.prototype.readUint64=function(){return this.readUint32()*l+this.readUint32()},d.prototype.readInt64=function(){return this.readUint32()*l+this.readUint32()},d.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},t.DataStream=d,d.prototype.save=function(e){var t=new Blob([this.buffer]);if(!window.URL||!URL.createObjectURL)throw"DataStream.save: Can't create object URL.";var r=window.URL.createObjectURL(t),i=document.createElement("a");document.body.appendChild(i),i.setAttribute("href",r),i.setAttribute("download",e),i.setAttribute("target","_self"),i.click(),window.URL.revokeObjectURL(r)},d.prototype._dynamicSize=!0,Object.defineProperty(d.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(e){e||this._trimAlloc(),this._dynamicSize=e}}),d.prototype.shift=function(e){var t=new ArrayBuffer(this._byteLength-e),r=new Uint8Array(t),i=new Uint8Array(this._buffer,e,r.length);r.set(i),this.buffer=t,this.position-=e},d.prototype.writeInt32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Int32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeInt32(e[r],t)},d.prototype.writeInt16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Int16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt16Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeInt16(e[r],t)},d.prototype.writeInt8Array=function(e){if(this._realloc(1*e.length),e instanceof Int8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt8Array(e.length);else for(var t=0;t<e.length;t++)this.writeInt8(e[t])},d.prototype.writeUint32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Uint32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeUint32(e[r],t)},d.prototype.writeUint16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Uint16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint16Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeUint16(e[r],t)},d.prototype.writeUint8Array=function(e){if(this._realloc(1*e.length),e instanceof Uint8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint8Array(e.length);else for(var t=0;t<e.length;t++)this.writeUint8(e[t])},d.prototype.writeFloat64Array=function(e,t){if(this._realloc(8*e.length),e instanceof Float64Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat64Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeFloat64(e[r],t)},d.prototype.writeFloat32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Float32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeFloat32(e[r],t)},d.prototype.writeInt32=function(e,t){this._realloc(4),this._dataView.setInt32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeInt16=function(e,t){this._realloc(2),this._dataView.setInt16(this.position,e,null==t?this.endianness:t),this.position+=2},d.prototype.writeInt8=function(e){this._realloc(1),this._dataView.setInt8(this.position,e),this.position+=1},d.prototype.writeUint32=function(e,t){this._realloc(4),this._dataView.setUint32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeUint16=function(e,t){this._realloc(2),this._dataView.setUint16(this.position,e,null==t?this.endianness:t),this.position+=2},d.prototype.writeUint8=function(e){this._realloc(1),this._dataView.setUint8(this.position,e),this.position+=1},d.prototype.writeFloat32=function(e,t){this._realloc(4),this._dataView.setFloat32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeFloat64=function(e,t){this._realloc(8),this._dataView.setFloat64(this.position,e,null==t?this.endianness:t),this.position+=8},d.prototype.writeUCS2String=function(e,t,r){null==r&&(r=e.length);for(var i=0;i<e.length&&i<r;i++)this.writeUint16(e.charCodeAt(i),t);for(;i<r;i++)this.writeUint16(0)},d.prototype.writeString=function(e,t,r){var i=0;if(null==t||"ASCII"==t)if(null!=r){var n=Math.min(e.length,r);for(i=0;i<n;i++)this.writeUint8(e.charCodeAt(i));for(;i<r;i++)this.writeUint8(0)}else for(i=0;i<e.length;i++)this.writeUint8(e.charCodeAt(i));else this.writeUint8Array(new TextEncoder(t).encode(e.substring(0,r)))},d.prototype.writeCString=function(e,t){var r=0;if(null!=t){var i=Math.min(e.length,t);for(r=0;r<i;r++)this.writeUint8(e.charCodeAt(r));for(;r<t;r++)this.writeUint8(0)}else{for(r=0;r<e.length;r++)this.writeUint8(e.charCodeAt(r));this.writeUint8(0)}},d.prototype.writeStruct=function(e,t){for(var r=0;r<e.length;r+=2){var i=e[r+1];this.writeType(i,t[e[r]],t)}},d.prototype.writeType=function(e,t,r){var i;if("function"==typeof e)return e(this,t);if("object"==typeof e&&!(e instanceof Array))return e.set(this,t,r);var n=null,o="ASCII",s=this.position;switch("string"==typeof e&&/:/.test(e)&&(i=e.split(":"),e=i[0],n=parseInt(i[1])),"string"==typeof e&&/,/.test(e)&&(i=e.split(","),e=i[0],o=parseInt(i[1])),e){case"uint8":this.writeUint8(t);break;case"int8":this.writeInt8(t);break;case"uint16":this.writeUint16(t,this.endianness);break;case"int16":this.writeInt16(t,this.endianness);break;case"uint32":this.writeUint32(t,this.endianness);break;case"int32":this.writeInt32(t,this.endianness);break;case"float32":this.writeFloat32(t,this.endianness);break;case"float64":this.writeFloat64(t,this.endianness);break;case"uint16be":this.writeUint16(t,d.BIG_ENDIAN);break;case"int16be":this.writeInt16(t,d.BIG_ENDIAN);break;case"uint32be":this.writeUint32(t,d.BIG_ENDIAN);break;case"int32be":this.writeInt32(t,d.BIG_ENDIAN);break;case"float32be":this.writeFloat32(t,d.BIG_ENDIAN);break;case"float64be":this.writeFloat64(t,d.BIG_ENDIAN);break;case"uint16le":this.writeUint16(t,d.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(t,d.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(t,d.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(t,d.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(t,d.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(t,d.LITTLE_ENDIAN);break;case"cstring":this.writeCString(t,n);break;case"string":this.writeString(t,o,n);break;case"u16string":this.writeUCS2String(t,this.endianness,n);break;case"u16stringle":this.writeUCS2String(t,d.LITTLE_ENDIAN,n);break;case"u16stringbe":this.writeUCS2String(t,d.BIG_ENDIAN,n);break;default:if(3==e.length){for(var a=e[1],l=0;l<t.length;l++)this.writeType(a,t[l]);break}this.writeStruct(e,t)}null!=n&&(this.position=s,this._realloc(n),this.position=s+n)},d.prototype.writeUint64=function(e){var t=Math.floor(e/l);this.writeUint32(t),this.writeUint32(4294967295&e)},d.prototype.writeUint24=function(e){this.writeUint8((16711680&e)>>16),this.writeUint8((65280&e)>>8),this.writeUint8(255&e)},d.prototype.adjustUint32=function(e,t){var r=this.position;this.seek(e),this.writeUint32(t),this.seek(r)},d.prototype.mapInt32Array=function(e,t){this._realloc(4*e);var r=new Int32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r},d.prototype.mapInt16Array=function(e,t){this._realloc(2*e);var r=new Int16Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=2*e,r},d.prototype.mapInt8Array=function(e){this._realloc(1*e);var t=new Int8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},d.prototype.mapUint32Array=function(e,t){this._realloc(4*e);var r=new Uint32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r},d.prototype.mapUint16Array=function(e,t){this._realloc(2*e);var r=new Uint16Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=2*e,r},d.prototype.mapFloat64Array=function(e,t){this._realloc(8*e);var r=new Float64Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=8*e,r},d.prototype.mapFloat32Array=function(e,t){this._realloc(4*e);var r=new Float32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r};var u=function(e){this.buffers=[],this.bufferIndex=-1,e&&(this.insertBuffer(e),this.bufferIndex=0)};(u.prototype=new d(new ArrayBuffer,0,d.BIG_ENDIAN)).initialized=function(){var e;return this.bufferIndex>-1||(this.buffers.length>0?0===(e=this.buffers[0]).fileStart?(this.buffer=e,this.bufferIndex=0,s.debug("MultiBufferStream","Stream ready for parsing"),!0):(s.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1):(s.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1))},ArrayBuffer.concat=function(e,t){s.debug("ArrayBuffer","Trying to create a new buffer of size: "+(e.byteLength+t.byteLength));var r=new Uint8Array(e.byteLength+t.byteLength);return r.set(new Uint8Array(e),0),r.set(new Uint8Array(t),e.byteLength),r.buffer},u.prototype.reduceBuffer=function(e,t,r){var i;return(i=new Uint8Array(r)).set(new Uint8Array(e,t,r)),i.buffer.fileStart=e.fileStart+t,i.buffer.usedBytes=0,i.buffer},u.prototype.insertBuffer=function(e){for(var t=!0,r=0;r<this.buffers.length;r++){var i=this.buffers[r];if(e.fileStart<=i.fileStart){if(e.fileStart===i.fileStart){if(e.byteLength>i.byteLength){this.buffers.splice(r,1),r--;continue}s.warn("MultiBufferStream","Buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+") already appended, ignoring")}else e.fileStart+e.byteLength<=i.fileStart||(e=this.reduceBuffer(e,0,i.fileStart-e.fileStart)),s.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.splice(r,0,e),0===r&&(this.buffer=e);t=!1;break}if(e.fileStart<i.fileStart+i.byteLength){var n=i.fileStart+i.byteLength-e.fileStart,o=e.byteLength-n;if(!(o>0)){t=!1;break}e=this.reduceBuffer(e,n,o)}}t&&(s.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.push(e),0===r&&(this.buffer=e))},u.prototype.logBufferLevel=function(e){var t,r,i,n,o,a=[],d="";for(i=0,n=0,t=0;t<this.buffers.length;t++)r=this.buffers[t],0===t?(o={},a.push(o),o.start=r.fileStart,o.end=r.fileStart+r.byteLength,d+="["+o.start+"-"):o.end===r.fileStart?o.end=r.fileStart+r.byteLength:((o={}).start=r.fileStart,d+=a[a.length-1].end-1+"], ["+o.start+"-",o.end=r.fileStart+r.byteLength,a.push(o)),i+=r.usedBytes,n+=r.byteLength;a.length>0&&(d+=o.end-1+"]");var l=e?s.info:s.debug;0===this.buffers.length?l("MultiBufferStream","No more buffer in memory"):l("MultiBufferStream",this.buffers.length+" stored buffer(s) ("+i+"/"+n+" bytes), continuous ranges: "+d)},u.prototype.cleanBuffers=function(){var e,t;for(e=0;e<this.buffers.length;e++)(t=this.buffers[e]).usedBytes===t.byteLength&&(s.debug("MultiBufferStream","Removing buffer #"+e),this.buffers.splice(e,1),e--)},u.prototype.mergeNextBuffer=function(){var e;if(this.bufferIndex+1<this.buffers.length){if((e=this.buffers[this.bufferIndex+1]).fileStart===this.buffer.fileStart+this.buffer.byteLength){var t=this.buffer.byteLength,r=this.buffer.usedBytes,i=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,e),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=r,this.buffer.fileStart=i,s.debug("ISOFile","Concatenating buffer for box parsing (length: "+t+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},u.prototype.findPosition=function(e,t,r){var i,n=null,o=-1;for(i=!0===e?0:this.bufferIndex;i<this.buffers.length&&(n=this.buffers[i]).fileStart<=t;)o=i,r&&(n.fileStart+n.byteLength<=t?n.usedBytes=n.byteLength:n.usedBytes=t-n.fileStart,this.logBufferLevel()),i++;return-1!==o&&(n=this.buffers[o]).fileStart+n.byteLength>=t?(s.debug("MultiBufferStream","Found position in existing buffer #"+o),o):-1},u.prototype.findEndContiguousBuf=function(e){var t,r,i,n=void 0!==e?e:this.bufferIndex;if(r=this.buffers[n],this.buffers.length>n+1)for(t=n+1;t<this.buffers.length&&(i=this.buffers[t]).fileStart===r.fileStart+r.byteLength;t++)r=i;return r.fileStart+r.byteLength},u.prototype.getEndFilePositionAfter=function(e){var t=this.findPosition(!0,e,!1);return-1!==t?this.findEndContiguousBuf(t):e},u.prototype.addUsedBytes=function(e){this.buffer.usedBytes+=e,this.logBufferLevel()},u.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},u.prototype.seek=function(e,t,r){var i;return-1!==(i=this.findPosition(t,e,r))?(this.buffer=this.buffers[i],this.bufferIndex=i,this.position=e-this.buffer.fileStart,s.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(s.debug("MultiBufferStream","Position "+e+" not found in buffered data"),!1)},u.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},u.prototype.getLength=function(){return this.byteLength},u.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength},t.MultiBufferStream=u;var c=function(){var e=[];e[3]="ES_Descriptor",e[4]="DecoderConfigDescriptor",e[5]="DecoderSpecificInfo",e[6]="SLConfigDescriptor",this.getDescriptorName=function(t){return e[t]};var t=this,r={};return this.parseOneDescriptor=function(t){var i,n,o,a=0;for(i=t.readUint8(),o=t.readUint8();128&o;)a=(127&o)<<7,o=t.readUint8();return a+=127&o,s.debug("MPEG4DescriptorParser","Found "+(e[i]||"Descriptor "+i)+", size "+a+" at position "+t.getPosition()),(n=e[i]?new r[e[i]](a):new r.Descriptor(a)).parse(t),n},r.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},r.Descriptor.prototype.parse=function(e){this.data=e.readUint8Array(this.size)},r.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag==e)return this.descs[t];return null},r.Descriptor.prototype.parseRemainingDescriptors=function(e){for(var r=e.position;e.position<r+this.size;){var i=t.parseOneDescriptor(e);this.descs.push(i)}},r.ES_Descriptor=function(e){r.Descriptor.call(this,3,e)},r.ES_Descriptor.prototype=new r.Descriptor,r.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readUint16(),this.flags=e.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=e.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var t=e.readUint8();this.URL=e.readString(t),this.size-=t+1}else this.URL="";32&this.flags?(this.OCR_ES_ID=e.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},r.ES_Descriptor.prototype.getOTI=function(e){var t=this.findDescriptor(4);return t?t.oti:0},r.ES_Descriptor.prototype.getAudioConfig=function(e){var t=this.findDescriptor(4);if(!t)return null;var r=t.findDescriptor(5);if(r&&r.data){var i=(248&r.data[0])>>3;return 31===i&&r.data.length>=2&&(i=32+((7&r.data[0])<<3)+((224&r.data[1])>>5)),i}return null},r.DecoderConfigDescriptor=function(e){r.Descriptor.call(this,4,e)},r.DecoderConfigDescriptor.prototype=new r.Descriptor,r.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readUint8(),this.streamType=e.readUint8(),this.upStream=0!=(this.streamType>>1&1),this.streamType=this.streamType>>>2,this.bufferSize=e.readUint24(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),this.size-=13,this.parseRemainingDescriptors(e)},r.DecoderSpecificInfo=function(e){r.Descriptor.call(this,5,e)},r.DecoderSpecificInfo.prototype=new r.Descriptor,r.SLConfigDescriptor=function(e){r.Descriptor.call(this,6,e)},r.SLConfigDescriptor.prototype=new r.Descriptor,this};t.MPEG4DescriptorParser=c;var f={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,BASIC_BOXES:["mdat","idat","free","skip","meco","strk"],FULL_BOXES:["hmhd","nmhd","iods","xml ","bxml","ipro","mere"],CONTAINER_BOXES:[["moov",["trak","pssh"]],["trak"],["edts"],["mdia"],["minf"],["dinf"],["stbl",["sgpd","sbgp"]],["mvex",["trex"]],["moof",["traf"]],["traf",["trun","sgpd","sbgp"]],["vttc"],["tref"],["iref"],["mfra",["tfra"]],["meco"],["hnti"],["hinf"],["strk"],["strd"],["sinf"],["rinf"],["schi"],["trgr"],["udta",["kind"]],["iprp",["ipma"]],["ipco"],["grpl"],["j2kH"],["etyp",["tyco"]]],boxCodes:[],fullBoxCodes:[],containerBoxCodes:[],sampleEntryCodes:{},sampleGroupEntryCodes:[],trackGroupTypes:[],UUIDBoxes:{},UUIDs:[],initialize:function(){f.FullBox.prototype=new f.Box,f.ContainerBox.prototype=new f.Box,f.SampleEntry.prototype=new f.Box,f.TrackGroupTypeBox.prototype=new f.FullBox,f.BASIC_BOXES.forEach((function(e){f.createBoxCtor(e)})),f.FULL_BOXES.forEach((function(e){f.createFullBoxCtor(e)})),f.CONTAINER_BOXES.forEach((function(e){f.createContainerBoxCtor(e[0],null,e[1])}))},Box:function(e,t,r){this.type=e,this.size=t,this.uuid=r},FullBox:function(e,t,r){f.Box.call(this,e,t,r),this.flags=0,this.version=0},ContainerBox:function(e,t,r){f.Box.call(this,e,t,r),this.boxes=[]},SampleEntry:function(e,t,r,i){f.ContainerBox.call(this,e,t),this.hdr_size=r,this.start=i},SampleGroupEntry:function(e){this.grouping_type=e},TrackGroupTypeBox:function(e,t){f.FullBox.call(this,e,t)},createBoxCtor:function(e,t){f.boxCodes.push(e),f[e+"Box"]=function(t){f.Box.call(this,e,t)},f[e+"Box"].prototype=new f.Box,t&&(f[e+"Box"].prototype.parse=t)},createFullBoxCtor:function(e,t){f[e+"Box"]=function(t){f.FullBox.call(this,e,t)},f[e+"Box"].prototype=new f.FullBox,f[e+"Box"].prototype.parse=function(e){this.parseFullHeader(e),t&&t.call(this,e)}},addSubBoxArrays:function(e){if(e){this.subBoxNames=e;for(var t=e.length,r=0;r<t;r++)this[e[r]+"s"]=[]}},createContainerBoxCtor:function(e,t,r){f[e+"Box"]=function(t){f.ContainerBox.call(this,e,t),f.addSubBoxArrays.call(this,r)},f[e+"Box"].prototype=new f.ContainerBox,t&&(f[e+"Box"].prototype.parse=t)},createMediaSampleEntryCtor:function(e,t,r){f.sampleEntryCodes[e]=[],f[e+"SampleEntry"]=function(e,t){f.SampleEntry.call(this,e,t),f.addSubBoxArrays.call(this,r)},f[e+"SampleEntry"].prototype=new f.SampleEntry,t&&(f[e+"SampleEntry"].prototype.parse=t)},createSampleEntryCtor:function(e,t,r,i){f.sampleEntryCodes[e].push(t),f[t+"SampleEntry"]=function(r){f[e+"SampleEntry"].call(this,t,r),f.addSubBoxArrays.call(this,i)},f[t+"SampleEntry"].prototype=new f[e+"SampleEntry"],r&&(f[t+"SampleEntry"].prototype.parse=r)},createEncryptedSampleEntryCtor:function(e,t,r){f.createSampleEntryCtor.call(this,e,t,r,["sinf"])},createSampleGroupCtor:function(e,t){f[e+"SampleGroupEntry"]=function(t){f.SampleGroupEntry.call(this,e,t)},f[e+"SampleGroupEntry"].prototype=new f.SampleGroupEntry,t&&(f[e+"SampleGroupEntry"].prototype.parse=t)},createTrackGroupCtor:function(e,t){f[e+"TrackGroupTypeBox"]=function(t){f.TrackGroupTypeBox.call(this,e,t)},f[e+"TrackGroupTypeBox"].prototype=new f.TrackGroupTypeBox,t&&(f[e+"TrackGroupTypeBox"].prototype.parse=t)},createUUIDBox:function(e,t,r,i){f.UUIDs.push(e),f.UUIDBoxes[e]=function(i){t?f.FullBox.call(this,"uuid",i,e):r?f.ContainerBox.call(this,"uuid",i,e):f.Box.call(this,"uuid",i,e)},f.UUIDBoxes[e].prototype=t?new f.FullBox:r?new f.ContainerBox:new f.Box,i&&(f.UUIDBoxes[e].prototype.parse=t?function(e){this.parseFullHeader(e),i&&i.call(this,e)}:i)}};function h(e,t){this.x=e,this.y=t}function p(e,t){this.bad_pixel_row=e,this.bad_pixel_column=t}f.initialize(),f.TKHD_FLAG_ENABLED=1,f.TKHD_FLAG_IN_MOVIE=2,f.TKHD_FLAG_IN_PREVIEW=4,f.TFHD_FLAG_BASE_DATA_OFFSET=1,f.TFHD_FLAG_SAMPLE_DESC=2,f.TFHD_FLAG_SAMPLE_DUR=8,f.TFHD_FLAG_SAMPLE_SIZE=16,f.TFHD_FLAG_SAMPLE_FLAGS=32,f.TFHD_FLAG_DUR_EMPTY=65536,f.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,f.TRUN_FLAGS_DATA_OFFSET=1,f.TRUN_FLAGS_FIRST_FLAG=4,f.TRUN_FLAGS_DURATION=256,f.TRUN_FLAGS_SIZE=512,f.TRUN_FLAGS_FLAGS=1024,f.TRUN_FLAGS_CTS_OFFSET=2048,f.Box.prototype.add=function(e){return this.addBox(new f[e+"Box"])},f.Box.prototype.addBox=function(e){return this.boxes.push(e),this[e.type+"s"]?this[e.type+"s"].push(e):this[e.type]=e,e},f.Box.prototype.set=function(e,t){return this[e]=t,this},f.Box.prototype.addEntry=function(e,t){var r=t||"entries";return this[r]||(this[r]=[]),this[r].push(e),this},t.BoxParser=f,f.parseUUID=function(e){return f.parseHex16(e)},f.parseHex16=function(e){for(var t="",r=0;r<16;r++){var i=e.readUint8().toString(16);t+=1===i.length?"0"+i:i}return t},f.parseOneBox=function(e,t,r){var i,n,o,a=e.getPosition(),d=0;if(e.getEndPosition()-a<8)return s.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:f.ERR_NOT_ENOUGH_DATA};if(r&&r<8)return s.debug("BoxParser","Not enough bytes left in the parent box to parse a new box"),{code:f.ERR_NOT_ENOUGH_DATA};var l=e.readUint32(),u=e.readString(4),c=u;if(s.debug("BoxParser","Found box of type '"+u+"' and size "+l+" at position "+a),d=8,"uuid"==u){if(e.getEndPosition()-e.getPosition()<16||r-d<16)return e.seek(a),s.debug("BoxParser","Not enough bytes left in the parent box to parse a UUID box"),{code:f.ERR_NOT_ENOUGH_DATA};d+=16,c=o=f.parseUUID(e)}if(1==l){if(e.getEndPosition()-e.getPosition()<8||r&&r-d<8)return e.seek(a),s.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+u+'" box'),{code:f.ERR_NOT_ENOUGH_DATA};l=e.readUint64(),d+=8}else if(0===l)if(r)l=r;else if("mdat"!==u)return s.error("BoxParser","Unlimited box size not supported for type: '"+u+"'"),i=new f.Box(u,l),{code:f.OK,box:i,size:i.size};return 0!==l&&l<d?(s.error("BoxParser","Box of type "+u+" has an invalid size "+l+" (too small to be a box)"),{code:f.ERR_NOT_ENOUGH_DATA,type:u,size:l,hdr_size:d,start:a}):0!==l&&r&&l>r?(s.error("BoxParser","Box of type '"+u+"' has a size "+l+" greater than its container size "+r),{code:f.ERR_NOT_ENOUGH_DATA,type:u,size:l,hdr_size:d,start:a}):0!==l&&a+l>e.getEndPosition()?(e.seek(a),s.info("BoxParser","Not enough data in stream to parse the entire '"+u+"' box"),{code:f.ERR_NOT_ENOUGH_DATA,type:u,size:l,hdr_size:d,start:a}):t?{code:f.OK,type:u,size:l,hdr_size:d,start:a}:(f[u+"Box"]?i=new f[u+"Box"](l):"uuid"!==u?(s.warn("BoxParser","Unknown box type: '"+u+"'"),(i=new f.Box(u,l)).has_unparsed_data=!0):f.UUIDBoxes[o]?i=new f.UUIDBoxes[o](l):(s.warn("BoxParser","Unknown uuid type: '"+o+"'"),(i=new f.Box(u,l)).uuid=o,i.has_unparsed_data=!0),i.hdr_size=d,i.start=a,i.write===f.Box.prototype.write&&"mdat"!==i.type&&(s.info("BoxParser","'"+c+"' box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),(n=e.getPosition()-(i.start+i.size))<0?(s.warn("BoxParser","Parsing of box '"+c+"' did not read the entire indicated box data size (missing "+-n+" bytes), seeking forward"),e.seek(i.start+i.size)):n>0&&(s.error("BoxParser","Parsing of box '"+c+"' read "+n+" more bytes than the indicated box data size, seeking backwards"),0!==i.size&&e.seek(i.start+i.size)),{code:f.OK,box:i,size:i.size})},f.Box.prototype.parse=function(e){"mdat"!=this.type?this.data=e.readUint8Array(this.size-this.hdr_size):0===this.size?e.seek(e.getEndPosition()):e.seek(this.start+this.size)},f.Box.prototype.parseDataAndRewind=function(e){this.data=e.readUint8Array(this.size-this.hdr_size),e.position-=this.size-this.hdr_size},f.FullBox.prototype.parseDataAndRewind=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,e.position-=this.size-this.hdr_size},f.FullBox.prototype.parseFullHeader=function(e){this.version=e.readUint8(),this.flags=e.readUint24(),this.hdr_size+=4},f.FullBox.prototype.parse=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},f.ContainerBox.prototype.parse=function(e){for(var t,r;e.getPosition()<this.start+this.size;){if((t=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;if(r=t.box,this.boxes.push(r),this.subBoxNames&&-1!=this.subBoxNames.indexOf(r.type))this[this.subBoxNames[this.subBoxNames.indexOf(r.type)]+"s"].push(r);else{var i="uuid"!==r.type?r.type:r.uuid;this[i]?s.warn("Box of type "+i+" already stored in field of this type"):this[i]=r}}},f.Box.prototype.parseLanguage=function(e){this.language=e.readUint16();var t=[];t[0]=this.language>>10&31,t[1]=this.language>>5&31,t[2]=31&this.language,this.languageString=String.fromCharCode(t[0]+96,t[1]+96,t[2]+96)},f.SAMPLE_ENTRY_TYPE_VISUAL="Visual",f.SAMPLE_ENTRY_TYPE_AUDIO="Audio",f.SAMPLE_ENTRY_TYPE_HINT="Hint",f.SAMPLE_ENTRY_TYPE_METADATA="Metadata",f.SAMPLE_ENTRY_TYPE_SUBTITLE="Subtitle",f.SAMPLE_ENTRY_TYPE_SYSTEM="System",f.SAMPLE_ENTRY_TYPE_TEXT="Text",f.SampleEntry.prototype.parseHeader=function(e){e.readUint8Array(6),this.data_reference_index=e.readUint16(),this.hdr_size+=8},f.SampleEntry.prototype.parse=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},f.SampleEntry.prototype.parseDataAndRewind=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,e.position-=this.size-this.hdr_size},f.SampleEntry.prototype.parseFooter=function(e){f.ContainerBox.prototype.parse.call(this,e)},f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_HINT),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SYSTEM),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_TEXT),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,(function(e){var t;this.parseHeader(e),e.readUint16(),e.readUint16(),e.readUint32Array(3),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.readUint32(),this.frame_count=e.readUint16(),t=Math.min(31,e.readUint8()),this.compressorname=e.readString(t),t<31&&e.readString(31-t),this.depth=e.readUint16(),e.readUint16(),this.parseFooter(e)})),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,(function(e){this.parseHeader(e),e.readUint32Array(2),this.channel_count=e.readUint16(),this.samplesize=e.readUint16(),e.readUint16(),e.readUint16(),this.samplerate=e.readUint32()/65536,this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc2"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc4"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"av01"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"dav1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"hvc1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"hev1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"hvt1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"lhe1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"dvh1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"dvhe"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvc1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvi1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvs1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvcN"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vp08"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vp09"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avs3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"j2ki"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"mjp2"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"mjpg"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"uncv"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"mp4a"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"ac-3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"ac-4"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"ec-3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"Opus"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"mha1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"mha2"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"mhm1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"mhm2"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"encv"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"enca"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"encu"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SYSTEM,"encs"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_TEXT,"enct"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"encm"),f.createBoxCtor("a1lx",(function(e){var t=16*(1+(1&(1&e.readUint8())));this.layer_size=[];for(var r=0;r<3;r++)this.layer_size[r]=16==t?e.readUint16():e.readUint32()})),f.createBoxCtor("a1op",(function(e){this.op_index=e.readUint8()})),f.createFullBoxCtor("auxC",(function(e){this.aux_type=e.readCString();var t=this.size-this.hdr_size-(this.aux_type.length+1);this.aux_subtype=e.readUint8Array(t)})),f.createBoxCtor("av1C",(function(e){var t=e.readUint8();if(t>>7&!1)s.error("av1C marker problem");else if(this.version=127&t,1===this.version)if(t=e.readUint8(),this.seq_profile=t>>5&7,this.seq_level_idx_0=31&t,t=e.readUint8(),this.seq_tier_0=t>>7&1,this.high_bitdepth=t>>6&1,this.twelve_bit=t>>5&1,this.monochrome=t>>4&1,this.chroma_subsampling_x=t>>3&1,this.chroma_subsampling_y=t>>2&1,this.chroma_sample_position=3&t,t=e.readUint8(),this.reserved_1=t>>5&7,0===this.reserved_1){if(this.initial_presentation_delay_present=t>>4&1,1===this.initial_presentation_delay_present)this.initial_presentation_delay_minus_one=15&t;else if(this.reserved_2=15&t,0!==this.reserved_2)return void s.error("av1C reserved_2 parsing problem");var r=this.size-this.hdr_size-4;this.configOBUs=e.readUint8Array(r)}else s.error("av1C reserved_1 parsing problem");else s.error("av1C version "+this.version+" not supported")})),f.createBoxCtor("avcC",(function(e){var t,r;for(this.configurationVersion=e.readUint8(),this.AVCProfileIndication=e.readUint8(),this.profile_compatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=3&e.readUint8(),this.nb_SPS_nalus=31&e.readUint8(),r=this.size-this.hdr_size-6,this.SPS=[],t=0;t<this.nb_SPS_nalus;t++)this.SPS[t]={},this.SPS[t].length=e.readUint16(),this.SPS[t].nalu=e.readUint8Array(this.SPS[t].length),r-=2+this.SPS[t].length;for(this.nb_PPS_nalus=e.readUint8(),r--,this.PPS=[],t=0;t<this.nb_PPS_nalus;t++)this.PPS[t]={},this.PPS[t].length=e.readUint16(),this.PPS[t].nalu=e.readUint8Array(this.PPS[t].length),r-=2+this.PPS[t].length;r>0&&(this.ext=e.readUint8Array(r))})),f.createBoxCtor("btrt",(function(e){this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32()})),f.createFullBoxCtor("ccst",(function(e){var t=e.readUint8();this.all_ref_pics_intra=128==(128&t),this.intra_pred_used=64==(64&t),this.max_ref_per_pic=(63&t)>>2,e.readUint24()})),f.createBoxCtor("cdef",(function(e){var t;for(this.channel_count=e.readUint16(),this.channel_indexes=[],this.channel_types=[],this.channel_associations=[],t=0;t<this.channel_count;t++)this.channel_indexes.push(e.readUint16()),this.channel_types.push(e.readUint16()),this.channel_associations.push(e.readUint16())})),f.createBoxCtor("clap",(function(e){this.cleanApertureWidthN=e.readUint32(),this.cleanApertureWidthD=e.readUint32(),this.cleanApertureHeightN=e.readUint32(),this.cleanApertureHeightD=e.readUint32(),this.horizOffN=e.readUint32(),this.horizOffD=e.readUint32(),this.vertOffN=e.readUint32(),this.vertOffD=e.readUint32()})),f.createBoxCtor("clli",(function(e){this.max_content_light_level=e.readUint16(),this.max_pic_average_light_level=e.readUint16()})),f.createFullBoxCtor("cmex",(function(e){1&this.flags&&(this.pos_x=e.readInt32()),2&this.flags&&(this.pos_y=e.readInt32()),4&this.flags&&(this.pos_z=e.readInt32()),8&this.flags&&(0==this.version?16&this.flags?(this.quat_x=e.readInt32(),this.quat_y=e.readInt32(),this.quat_z=e.readInt32()):(this.quat_x=e.readInt16(),this.quat_y=e.readInt16(),this.quat_z=e.readInt16()):this.version),32&this.flags&&(this.id=e.readUint32())})),f.createFullBoxCtor("cmin",(function(e){this.focal_length_x=e.readInt32(),this.principal_point_x=e.readInt32(),this.principal_point_y=e.readInt32(),1&this.flags&&(this.focal_length_y=e.readInt32(),this.skew_factor=e.readInt32())})),f.createBoxCtor("cmpd",(function(e){for(this.component_count=e.readUint32(),this.component_types=[],this.component_type_urls=[],i=0;i<this.component_count;i++){var t=e.readUint16();this.component_types.push(t),t>=32768&&this.component_type_urls.push(e.readCString())}})),f.createFullBoxCtor("co64",(function(e){var t,r;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(r=0;r<t;r++)this.chunk_offsets.push(e.readUint64())})),f.createFullBoxCtor("CoLL",(function(e){this.maxCLL=e.readUint16(),this.maxFALL=e.readUint16()})),f.createBoxCtor("colr",(function(e){if(this.colour_type=e.readString(4),"nclx"===this.colour_type){this.colour_primaries=e.readUint16(),this.transfer_characteristics=e.readUint16(),this.matrix_coefficients=e.readUint16();var t=e.readUint8();this.full_range_flag=t>>7}else("rICC"===this.colour_type||"prof"===this.colour_type)&&(this.ICC_profile=e.readUint8Array(this.size-4))})),f.createFullBoxCtor("cprt",(function(e){this.parseLanguage(e),this.notice=e.readCString()})),f.createFullBoxCtor("cslg",(function(e){0===this.version&&(this.compositionToDTSShift=e.readInt32(),this.leastDecodeToDisplayDelta=e.readInt32(),this.greatestDecodeToDisplayDelta=e.readInt32(),this.compositionStartTime=e.readInt32(),this.compositionEndTime=e.readInt32())})),f.createFullBoxCtor("ctts",(function(e){var t,r;if(t=e.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(r=0;r<t;r++){this.sample_counts.push(e.readUint32());var i=e.readInt32();i<0&&s.warn("BoxParser","ctts box uses negative values without using version 1"),this.sample_offsets.push(i)}else if(1==this.version)for(r=0;r<t;r++)this.sample_counts.push(e.readUint32()),this.sample_offsets.push(e.readInt32())})),f.createBoxCtor("dac3",(function(e){var t=e.readUint8(),r=e.readUint8(),i=e.readUint8();this.fscod=t>>6,this.bsid=t>>1&31,this.bsmod=(1&t)<<2|r>>6&3,this.acmod=r>>3&7,this.lfeon=r>>2&1,this.bit_rate_code=3&r|i>>5&7})),f.createBoxCtor("dec3",(function(e){var t=e.readUint16();this.data_rate=t>>3,this.num_ind_sub=7&t,this.ind_subs=[];for(var r=0;r<this.num_ind_sub+1;r++){var i={};this.ind_subs.push(i);var n=e.readUint8(),o=e.readUint8(),s=e.readUint8();i.fscod=n>>6,i.bsid=n>>1&31,i.bsmod=(1&n)<<4|o>>4&15,i.acmod=o>>1&7,i.lfeon=1&o,i.num_dep_sub=s>>1&15,i.num_dep_sub>0&&(i.chan_loc=(1&s)<<8|e.readUint8())}})),f.createFullBoxCtor("dfLa",(function(e){var t=[],r=["STREAMINFO","PADDING","APPLICATION","SEEKTABLE","VORBIS_COMMENT","CUESHEET","PICTURE","RESERVED"];for(this.parseFullHeader(e);;){var i=e.readUint8(),n=Math.min(127&i,r.length-1);if(n?e.readUint8Array(e.readUint24()):(e.readUint8Array(13),this.samplerate=e.readUint32()>>12,e.readUint8Array(20)),t.push(r[n]),128&i)break}this.numMetadataBlocks=t.length+" ("+t.join(", ")+")"})),f.createBoxCtor("dimm",(function(e){this.bytessent=e.readUint64()})),f.createBoxCtor("dmax",(function(e){this.time=e.readUint32()})),f.createBoxCtor("dmed",(function(e){this.bytessent=e.readUint64()})),f.createBoxCtor("dOps",(function(e){if(this.Version=e.readUint8(),this.OutputChannelCount=e.readUint8(),this.PreSkip=e.readUint16(),this.InputSampleRate=e.readUint32(),this.OutputGain=e.readInt16(),this.ChannelMappingFamily=e.readUint8(),0!==this.ChannelMappingFamily){this.StreamCount=e.readUint8(),this.CoupledCount=e.readUint8(),this.ChannelMapping=[];for(var t=0;t<this.OutputChannelCount;t++)this.ChannelMapping[t]=e.readUint8()}})),f.createFullBoxCtor("dref",(function(e){var t,r;this.entries=[];for(var i=e.readUint32(),n=0;n<i;n++){if((t=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;r=t.box,this.entries.push(r)}})),f.createBoxCtor("drep",(function(e){this.bytessent=e.readUint64()})),f.createFullBoxCtor("elng",(function(e){this.extended_language=e.readString(this.size-this.hdr_size)})),f.createFullBoxCtor("elst",(function(e){this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.entries.push(i),1===this.version?(i.segment_duration=e.readUint64(),i.media_time=e.readInt64()):(i.segment_duration=e.readUint32(),i.media_time=e.readInt32()),i.media_rate_integer=e.readInt16(),i.media_rate_fraction=e.readInt16()}})),f.createFullBoxCtor("emsg",(function(e){1==this.version?(this.timescale=e.readUint32(),this.presentation_time=e.readUint64(),this.event_duration=e.readUint32(),this.id=e.readUint32(),this.scheme_id_uri=e.readCString(),this.value=e.readCString()):(this.scheme_id_uri=e.readCString(),this.value=e.readCString(),this.timescale=e.readUint32(),this.presentation_time_delta=e.readUint32(),this.event_duration=e.readUint32(),this.id=e.readUint32());var t=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));1==this.version&&(t-=4),this.message_data=e.readUint8Array(t)})),f.createEntityToGroupCtor=function(e,t){f[e+"Box"]=function(t){f.FullBox.call(this,e,t)},f[e+"Box"].prototype=new f.FullBox,f[e+"Box"].prototype.parse=function(e){if(this.parseFullHeader(e),t)t.call(this,e);else for(this.group_id=e.readUint32(),this.num_entities_in_group=e.readUint32(),this.entity_ids=[],i=0;i<this.num_entities_in_group;i++){var r=e.readUint32();this.entity_ids.push(r)}}},f.createEntityToGroupCtor("aebr"),f.createEntityToGroupCtor("afbr"),f.createEntityToGroupCtor("albc"),f.createEntityToGroupCtor("altr"),f.createEntityToGroupCtor("brst"),f.createEntityToGroupCtor("dobr"),f.createEntityToGroupCtor("eqiv"),f.createEntityToGroupCtor("favc"),f.createEntityToGroupCtor("fobr"),f.createEntityToGroupCtor("iaug"),f.createEntityToGroupCtor("pano"),f.createEntityToGroupCtor("slid"),f.createEntityToGroupCtor("ster"),f.createEntityToGroupCtor("tsyn"),f.createEntityToGroupCtor("wbbr"),f.createEntityToGroupCtor("prgr"),f.createFullBoxCtor("esds",(function(e){var t=e.readUint8Array(this.size-this.hdr_size);if(void 0!==c){var r=new c;this.esd=r.parseOneDescriptor(new d(t.buffer,0,d.BIG_ENDIAN))}})),f.createBoxCtor("fiel",(function(e){this.fieldCount=e.readUint8(),this.fieldOrdering=e.readUint8()})),f.createBoxCtor("frma",(function(e){this.data_format=e.readString(4)})),f.createBoxCtor("ftyp",(function(e){var t=this.size-this.hdr_size;this.major_brand=e.readString(4),this.minor_version=e.readUint32(),t-=8,this.compatible_brands=[];for(var r=0;t>=4;)this.compatible_brands[r]=e.readString(4),t-=4,r++})),f.createFullBoxCtor("hdlr",(function(e){0===this.version&&(e.readUint32(),this.handler=e.readString(4),e.readUint32Array(3),this.name=e.readString(this.size-this.hdr_size-20),"\0"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))})),f.createBoxCtor("hvcC",(function(e){var t,r,i,n;this.configurationVersion=e.readUint8(),n=e.readUint8(),this.general_profile_space=n>>6,this.general_tier_flag=(32&n)>>5,this.general_profile_idc=31&n,this.general_profile_compatibility=e.readUint32(),this.general_constraint_indicator=e.readUint8Array(6),this.general_level_idc=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),this.chroma_format_idc=3&e.readUint8(),this.bit_depth_luma_minus8=7&e.readUint8(),this.bit_depth_chroma_minus8=7&e.readUint8(),this.avgFrameRate=e.readUint16(),n=e.readUint8(),this.constantFrameRate=n>>6,this.numTemporalLayers=(13&n)>>3,this.temporalIdNested=(4&n)>>2,this.lengthSizeMinusOne=3&n,this.nalu_arrays=[];var o=e.readUint8();for(t=0;t<o;t++){var s=[];this.nalu_arrays.push(s),n=e.readUint8(),s.completeness=(128&n)>>7,s.nalu_type=63&n;var a=e.readUint16();for(r=0;r<a;r++){var d={};s.push(d),i=e.readUint16(),d.data=e.readUint8Array(i)}}})),f.createFullBoxCtor("iinf",(function(e){var t;0===this.version?this.entry_count=e.readUint16():this.entry_count=e.readUint32(),this.item_infos=[];for(var r=0;r<this.entry_count;r++){if((t=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;"infe"!==t.box.type&&s.error("BoxParser","Expected 'infe' box, got "+t.box.type),this.item_infos[r]=t.box}})),f.createFullBoxCtor("iloc",(function(e){var t;t=e.readUint8(),this.offset_size=t>>4&15,this.length_size=15&t,t=e.readUint8(),this.base_offset_size=t>>4&15,1===this.version||2===this.version?this.index_size=15&t:this.index_size=0,this.items=[];var r=0;if(this.version<2)r=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";r=e.readUint32()}for(var i=0;i<r;i++){var n={};if(this.items.push(n),this.version<2)n.item_ID=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";n.item_ID=e.readUint32()}switch(1===this.version||2===this.version?n.construction_method=15&e.readUint16():n.construction_method=0,n.data_reference_index=e.readUint16(),this.base_offset_size){case 0:n.base_offset=0;break;case 4:n.base_offset=e.readUint32();break;case 8:n.base_offset=e.readUint64();break;default:throw"Error reading base offset size"}var o=e.readUint16();n.extents=[];for(var s=0;s<o;s++){var a={};if(n.extents.push(a),1===this.version||2===this.version)switch(this.index_size){case 0:a.extent_index=0;break;case 4:a.extent_index=e.readUint32();break;case 8:a.extent_index=e.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:a.extent_offset=0;break;case 4:a.extent_offset=e.readUint32();break;case 8:a.extent_offset=e.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:a.extent_length=0;break;case 4:a.extent_length=e.readUint32();break;case 8:a.extent_length=e.readUint64();break;default:throw"Error reading extent index"}}}})),f.createBoxCtor("imir",(function(e){var t=e.readUint8();this.reserved=t>>7,this.axis=1&t})),f.createFullBoxCtor("infe",(function(e){if(0!==this.version&&1!==this.version||(this.item_ID=e.readUint16(),this.item_protection_index=e.readUint16(),this.item_name=e.readCString(),this.content_type=e.readCString(),this.content_encoding=e.readCString()),1===this.version)return this.extension_type=e.readString(4),s.warn("BoxParser","Cannot parse extension type"),void e.seek(this.start+this.size);this.version>=2&&(2===this.version?this.item_ID=e.readUint16():3===this.version&&(this.item_ID=e.readUint32()),this.item_protection_index=e.readUint16(),this.item_type=e.readString(4),this.item_name=e.readCString(),"mime"===this.item_type?(this.content_type=e.readCString(),this.content_encoding=e.readCString()):"uri "===this.item_type&&(this.item_uri_type=e.readCString()))})),f.createFullBoxCtor("ipma",(function(e){var t,r;for(entry_count=e.readUint32(),this.associations=[],t=0;t<entry_count;t++){var i={};this.associations.push(i),this.version<1?i.id=e.readUint16():i.id=e.readUint32();var n=e.readUint8();for(i.props=[],r=0;r<n;r++){var o=e.readUint8(),s={};i.props.push(s),s.essential=(128&o)>>7==1,1&this.flags?s.property_index=(127&o)<<8|e.readUint8():s.property_index=127&o}}})),f.createFullBoxCtor("iref",(function(e){var t,r;for(this.references=[];e.getPosition()<this.start+this.size;){if((t=f.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==f.OK)return;(r=0===this.version?new f.SingleItemTypeReferenceBox(t.type,t.size,t.hdr_size,t.start):new f.SingleItemTypeReferenceBoxLarge(t.type,t.size,t.hdr_size,t.start)).write===f.Box.prototype.write&&"mdat"!==r.type&&(s.warn("BoxParser",r.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),this.references.push(r)}})),f.createBoxCtor("irot",(function(e){this.angle=3&e.readUint8()})),f.createFullBoxCtor("ispe",(function(e){this.image_width=e.readUint32(),this.image_height=e.readUint32()})),f.createFullBoxCtor("kind",(function(e){this.schemeURI=e.readCString(),this.value=e.readCString()})),f.createFullBoxCtor("leva",(function(e){var t=e.readUint8();this.levels=[];for(var r=0;r<t;r++){var i={};this.levels[r]=i,i.track_ID=e.readUint32();var n=e.readUint8();switch(i.padding_flag=n>>7,i.assignment_type=127&n,i.assignment_type){case 0:i.grouping_type=e.readString(4);break;case 1:i.grouping_type=e.readString(4),i.grouping_type_parameter=e.readUint32();break;case 2:case 3:break;case 4:i.sub_track_id=e.readUint32();break;default:s.warn("BoxParser","Unknown leva assignement type")}}})),f.createBoxCtor("lsel",(function(e){this.layer_id=e.readUint16()})),f.createBoxCtor("maxr",(function(e){this.period=e.readUint32(),this.bytes=e.readUint32()})),h.prototype.toString=function(){return"("+this.x+","+this.y+")"},f.createBoxCtor("mdcv",(function(e){this.display_primaries=[],this.display_primaries[0]=new h(e.readUint16(),e.readUint16()),this.display_primaries[1]=new h(e.readUint16(),e.readUint16()),this.display_primaries[2]=new h(e.readUint16(),e.readUint16()),this.white_point=new h(e.readUint16(),e.readUint16()),this.max_display_mastering_luminance=e.readUint32(),this.min_display_mastering_luminance=e.readUint32()})),f.createFullBoxCtor("mdhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.parseLanguage(e),e.readUint16()})),f.createFullBoxCtor("mehd",(function(e){1&this.flags&&(s.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=e.readUint64():this.fragment_duration=e.readUint32()})),f.createFullBoxCtor("meta",(function(e){this.boxes=[],f.ContainerBox.prototype.parse.call(this,e)})),f.createFullBoxCtor("mfhd",(function(e){this.sequence_number=e.readUint32()})),f.createFullBoxCtor("mfro",(function(e){this._size=e.readUint32()})),f.createFullBoxCtor("mskC",(function(e){this.bits_per_pixel=e.readUint8()})),f.createFullBoxCtor("mvhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.rate=e.readUint32(),this.volume=e.readUint16()>>8,e.readUint16(),e.readUint32Array(2),this.matrix=e.readUint32Array(9),e.readUint32Array(6),this.next_track_id=e.readUint32()})),f.createBoxCtor("npck",(function(e){this.packetssent=e.readUint32()})),f.createBoxCtor("nump",(function(e){this.packetssent=e.readUint64()})),f.createFullBoxCtor("padb",(function(e){var t=e.readUint32();this.padbits=[];for(var r=0;r<Math.floor((t+1)/2);r++)this.padbits=e.readUint8()})),f.createBoxCtor("pasp",(function(e){this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()})),f.createBoxCtor("payl",(function(e){this.text=e.readString(this.size-this.hdr_size)})),f.createBoxCtor("payt",(function(e){this.payloadID=e.readUint32();var t=e.readUint8();this.rtpmap_string=e.readString(t)})),f.createFullBoxCtor("pdin",(function(e){var t=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var r=0;r<t;r++)this.rate[r]=e.readUint32(),this.initial_delay[r]=e.readUint32()})),f.createFullBoxCtor("pitm",(function(e){0===this.version?this.item_id=e.readUint16():this.item_id=e.readUint32()})),f.createFullBoxCtor("pixi",(function(e){var t;for(this.num_channels=e.readUint8(),this.bits_per_channels=[],t=0;t<this.num_channels;t++)this.bits_per_channels[t]=e.readUint8()})),f.createBoxCtor("pmax",(function(e){this.bytes=e.readUint32()})),f.createFullBoxCtor("prdi",(function(e){if(this.step_count=e.readUint16(),this.item_count=[],2&this.flags)for(var t=0;t<this.step_count;t++)this.item_count[t]=e.readUint16()})),f.createFullBoxCtor("prft",(function(e){this.ref_track_id=e.readUint32(),this.ntp_timestamp=e.readUint64(),0===this.version?this.media_time=e.readUint32():this.media_time=e.readUint64()})),f.createFullBoxCtor("pssh",(function(e){if(this.system_id=f.parseHex16(e),this.version>0){var t=e.readUint32();this.kid=[];for(var r=0;r<t;r++)this.kid[r]=f.parseHex16(e)}var i=e.readUint32();i>0&&(this.data=e.readUint8Array(i))})),f.createFullBoxCtor("clef",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),f.createFullBoxCtor("enof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),f.createFullBoxCtor("prof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),f.createContainerBoxCtor("tapt",null,["clef","prof","enof"]),f.createBoxCtor("rtp ",(function(e){this.descriptionformat=e.readString(4),this.sdptext=e.readString(this.size-this.hdr_size-4)})),f.createFullBoxCtor("saio",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32());var t=e.readUint32();this.offset=[];for(var r=0;r<t;r++)0===this.version?this.offset[r]=e.readUint32():this.offset[r]=e.readUint64()})),f.createFullBoxCtor("saiz",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32()),this.default_sample_info_size=e.readUint8();var t=e.readUint32();if(this.sample_info_size=[],0===this.default_sample_info_size)for(var r=0;r<t;r++)this.sample_info_size[r]=e.readUint8()})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"mett",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"metx",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"sbtt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"stpp",(function(e){this.parseHeader(e),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.auxiliary_mime_types=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"stxt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"tx3g",(function(e){this.parseHeader(e),this.displayFlags=e.readUint32(),this.horizontal_justification=e.readInt8(),this.vertical_justification=e.readInt8(),this.bg_color_rgba=e.readUint8Array(4),this.box_record=e.readInt16Array(4),this.style_record=e.readUint8Array(12),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"wvtt",(function(e){this.parseHeader(e),this.parseFooter(e)})),f.createSampleGroupCtor("alst",(function(e){var t,r=e.readUint16();for(this.first_output_sample=e.readUint16(),this.sample_offset=[],t=0;t<r;t++)this.sample_offset[t]=e.readUint32();var i=this.description_length-4-4*r;for(this.num_output_samples=[],this.num_total_samples=[],t=0;t<i/4;t++)this.num_output_samples[t]=e.readUint16(),this.num_total_samples[t]=e.readUint16()})),f.createSampleGroupCtor("avll",(function(e){this.layerNumber=e.readUint8(),this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()})),f.createSampleGroupCtor("avss",(function(e){this.subSequenceIdentifier=e.readUint16(),this.layerNumber=e.readUint8();var t=e.readUint8();this.durationFlag=t>>7,this.avgRateFlag=t>>6&1,this.durationFlag&&(this.duration=e.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()),this.dependency=[];for(var r=e.readUint8(),i=0;i<r;i++){var n={};this.dependency.push(n),n.subSeqDirectionFlag=e.readUint8(),n.layerNumber=e.readUint8(),n.subSequenceIdentifier=e.readUint16()}})),f.createSampleGroupCtor("dtrt",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("mvif",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("prol",(function(e){this.roll_distance=e.readInt16()})),f.createSampleGroupCtor("rap ",(function(e){var t=e.readUint8();this.num_leading_samples_known=t>>7,this.num_leading_samples=127&t})),f.createSampleGroupCtor("rash",(function(e){if(this.operation_point_count=e.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)s.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=e.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=e.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var t=0;t<this.operation_point_count;t++)this.available_bitrate[t]=e.readUint32(),this.target_rate_share[t]=e.readUint16()}this.maximum_bitrate=e.readUint32(),this.minimum_bitrate=e.readUint32(),this.discard_priority=e.readUint8()}})),f.createSampleGroupCtor("roll",(function(e){this.roll_distance=e.readInt16()})),f.SampleGroupEntry.prototype.parse=function(e){s.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=e.readUint8Array(this.description_length)},f.createSampleGroupCtor("scif",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("scnm",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("seig",(function(e){this.reserved=e.readUint8();var t=e.readUint8();this.crypt_byte_block=t>>4,this.skip_byte_block=15&t,this.isProtected=e.readUint8(),this.Per_Sample_IV_Size=e.readUint8(),this.KID=f.parseHex16(e),this.constant_IV_size=0,this.constant_IV=0,1===this.isProtected&&0===this.Per_Sample_IV_Size&&(this.constant_IV_size=e.readUint8(),this.constant_IV=e.readUint8Array(this.constant_IV_size))})),f.createSampleGroupCtor("stsa",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("sync",(function(e){var t=e.readUint8();this.NAL_unit_type=63&t})),f.createSampleGroupCtor("tele",(function(e){var t=e.readUint8();this.level_independently_decodable=t>>7})),f.createSampleGroupCtor("tsas",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("tscl",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("vipr",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createFullBoxCtor("sbgp",(function(e){this.grouping_type=e.readString(4),1===this.version?this.grouping_type_parameter=e.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.entries.push(i),i.sample_count=e.readInt32(),i.group_description_index=e.readInt32()}})),p.prototype.toString=function(){return"[row: "+this.bad_pixel_row+", column: "+this.bad_pixel_column+"]"},f.createFullBoxCtor("sbpm",(function(e){var t;for(this.component_count=e.readUint16(),this.component_index=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16());var r=e.readUint8();for(this.correction_applied=128==(128&r),this.num_bad_rows=e.readUint32(),this.num_bad_cols=e.readUint32(),this.num_bad_pixels=e.readUint32(),this.bad_rows=[],this.bad_columns=[],this.bad_pixels=[],t=0;t<this.num_bad_rows;t++)this.bad_rows.push(e.readUint32());for(t=0;t<this.num_bad_cols;t++)this.bad_columns.push(e.readUint32());for(t=0;t<this.num_bad_pixels;t++){var i=e.readUint32(),n=e.readUint32();this.bad_pixels.push(new p(i,n))}})),f.createFullBoxCtor("schm",(function(e){this.scheme_type=e.readString(4),this.scheme_version=e.readUint32(),1&this.flags&&(this.scheme_uri=e.readString(this.size-this.hdr_size-8))})),f.createBoxCtor("sdp ",(function(e){this.sdptext=e.readString(this.size-this.hdr_size)})),f.createFullBoxCtor("sdtp",(function(e){var t,r=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var i=0;i<r;i++)t=e.readUint8(),this.is_leading[i]=t>>6,this.sample_depends_on[i]=t>>4&3,this.sample_is_depended_on[i]=t>>2&3,this.sample_has_redundancy[i]=3&t})),f.createFullBoxCtor("senc"),f.createFullBoxCtor("sgpd",(function(e){this.grouping_type=e.readString(4),s.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=e.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=e.readUint32()),this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i;i=f[this.grouping_type+"SampleGroupEntry"]?new f[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new f.SampleGroupEntry(this.grouping_type),this.entries.push(i),1===this.version&&0===this.default_length?i.description_length=e.readUint32():i.description_length=this.default_length,i.write===f.SampleGroupEntry.prototype.write&&(s.info("BoxParser","SampleGroup for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),i.data=e.readUint8Array(i.description_length),e.position-=i.description_length),i.parse(e)}})),f.createFullBoxCtor("sidx",(function(e){this.reference_ID=e.readUint32(),this.timescale=e.readUint32(),0===this.version?(this.earliest_presentation_time=e.readUint32(),this.first_offset=e.readUint32()):(this.earliest_presentation_time=e.readUint64(),this.first_offset=e.readUint64()),e.readUint16(),this.references=[];for(var t=e.readUint16(),r=0;r<t;r++){var i={};this.references.push(i);var n=e.readUint32();i.reference_type=n>>31&1,i.referenced_size=2147483647&n,i.subsegment_duration=e.readUint32(),n=e.readUint32(),i.starts_with_SAP=n>>31&1,i.SAP_type=n>>28&7,i.SAP_delta_time=268435455&n}})),f.SingleItemTypeReferenceBox=function(e,t,r,i){f.Box.call(this,e,t),this.hdr_size=r,this.start=i},f.SingleItemTypeReferenceBox.prototype=new f.Box,f.SingleItemTypeReferenceBox.prototype.parse=function(e){this.from_item_ID=e.readUint16();var t=e.readUint16();this.references=[];for(var r=0;r<t;r++)this.references[r]={},this.references[r].to_item_ID=e.readUint16()},f.SingleItemTypeReferenceBoxLarge=function(e,t,r,i){f.Box.call(this,e,t),this.hdr_size=r,this.start=i},f.SingleItemTypeReferenceBoxLarge.prototype=new f.Box,f.SingleItemTypeReferenceBoxLarge.prototype.parse=function(e){this.from_item_ID=e.readUint32();var t=e.readUint16();this.references=[];for(var r=0;r<t;r++)this.references[r]={},this.references[r].to_item_ID=e.readUint32()},f.createFullBoxCtor("SmDm",(function(e){this.primaryRChromaticity_x=e.readUint16(),this.primaryRChromaticity_y=e.readUint16(),this.primaryGChromaticity_x=e.readUint16(),this.primaryGChromaticity_y=e.readUint16(),this.primaryBChromaticity_x=e.readUint16(),this.primaryBChromaticity_y=e.readUint16(),this.whitePointChromaticity_x=e.readUint16(),this.whitePointChromaticity_y=e.readUint16(),this.luminanceMax=e.readUint32(),this.luminanceMin=e.readUint32()})),f.createFullBoxCtor("smhd",(function(e){this.balance=e.readUint16(),e.readUint16()})),f.createFullBoxCtor("ssix",(function(e){this.subsegments=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.subsegments.push(i),i.ranges=[];for(var n=e.readUint32(),o=0;o<n;o++){var s={};i.ranges.push(s),s.level=e.readUint8(),s.range_size=e.readUint24()}}})),f.createFullBoxCtor("stco",(function(e){var t;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(var r=0;r<t;r++)this.chunk_offsets.push(e.readUint32())})),f.createFullBoxCtor("stdp",(function(e){var t=(this.size-this.hdr_size)/2;this.priority=[];for(var r=0;r<t;r++)this.priority[r]=e.readUint16()})),f.createFullBoxCtor("sthd"),f.createFullBoxCtor("stri",(function(e){this.switch_group=e.readUint16(),this.alternate_group=e.readUint16(),this.sub_track_id=e.readUint32();var t=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var r=0;r<t;r++)this.attribute_list[r]=e.readUint32()})),f.createFullBoxCtor("stsc",(function(e){var t,r;if(t=e.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(r=0;r<t;r++)this.first_chunk.push(e.readUint32()),this.samples_per_chunk.push(e.readUint32()),this.sample_description_index.push(e.readUint32())})),f.createFullBoxCtor("stsd",(function(e){var t,r,i,n;for(this.entries=[],i=e.readUint32(),t=1;t<=i;t++){if((r=f.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==f.OK)return;f[r.type+"SampleEntry"]?((n=new f[r.type+"SampleEntry"](r.size)).hdr_size=r.hdr_size,n.start=r.start):(s.warn("BoxParser","Unknown sample entry type: "+r.type),n=new f.SampleEntry(r.type,r.size,r.hdr_size,r.start)),n.write===f.SampleEntry.prototype.write&&(s.info("BoxParser","SampleEntry "+n.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),n.parseDataAndRewind(e)),n.parse(e),this.entries.push(n)}})),f.createFullBoxCtor("stsg",(function(e){this.grouping_type=e.readUint32();var t=e.readUint16();this.group_description_index=[];for(var r=0;r<t;r++)this.group_description_index[r]=e.readUint32()})),f.createFullBoxCtor("stsh",(function(e){var t,r;if(t=e.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(r=0;r<t;r++)this.shadowed_sample_numbers.push(e.readUint32()),this.sync_sample_numbers.push(e.readUint32())})),f.createFullBoxCtor("stss",(function(e){var t,r;if(r=e.readUint32(),0===this.version)for(this.sample_numbers=[],t=0;t<r;t++)this.sample_numbers.push(e.readUint32())})),f.createFullBoxCtor("stsz",(function(e){var t;if(this.sample_sizes=[],0===this.version)for(this.sample_size=e.readUint32(),this.sample_count=e.readUint32(),t=0;t<this.sample_count;t++)0===this.sample_size?this.sample_sizes.push(e.readUint32()):this.sample_sizes[t]=this.sample_size})),f.createFullBoxCtor("stts",(function(e){var t,r,i;if(t=e.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(r=0;r<t;r++)this.sample_counts.push(e.readUint32()),(i=e.readInt32())<0&&(s.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),i=1),this.sample_deltas.push(i)})),f.createFullBoxCtor("stvi",(function(e){var t=e.readUint32();this.single_view_allowed=3&t,this.stereo_scheme=e.readUint32();var r,i,n=e.readUint32();for(this.stereo_indication_type=e.readString(n),this.boxes=[];e.getPosition()<this.start+this.size;){if((r=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;i=r.box,this.boxes.push(i),this[i.type]=i}})),f.createBoxCtor("styp",(function(e){f.ftypBox.prototype.parse.call(this,e)})),f.createFullBoxCtor("stz2",(function(e){var t,r;if(this.sample_sizes=[],0===this.version)if(this.reserved=e.readUint24(),this.field_size=e.readUint8(),r=e.readUint32(),4===this.field_size)for(t=0;t<r;t+=2){var i=e.readUint8();this.sample_sizes[t]=i>>4&15,this.sample_sizes[t+1]=15&i}else if(8===this.field_size)for(t=0;t<r;t++)this.sample_sizes[t]=e.readUint8();else if(16===this.field_size)for(t=0;t<r;t++)this.sample_sizes[t]=e.readUint16();else s.error("BoxParser","Error in length field in stz2 box")})),f.createFullBoxCtor("subs",(function(e){var t,r,i,n;for(i=e.readUint32(),this.entries=[],t=0;t<i;t++){var o={};if(this.entries[t]=o,o.sample_delta=e.readUint32(),o.subsamples=[],(n=e.readUint16())>0)for(r=0;r<n;r++){var s={};o.subsamples.push(s),1==this.version?s.size=e.readUint32():s.size=e.readUint16(),s.priority=e.readUint8(),s.discardable=e.readUint8(),s.codec_specific_parameters=e.readUint32()}}})),f.createFullBoxCtor("tenc",(function(e){if(e.readUint8(),0===this.version)e.readUint8();else{var t=e.readUint8();this.default_crypt_byte_block=t>>4&15,this.default_skip_byte_block=15&t}this.default_isProtected=e.readUint8(),this.default_Per_Sample_IV_Size=e.readUint8(),this.default_KID=f.parseHex16(e),1===this.default_isProtected&&0===this.default_Per_Sample_IV_Size&&(this.default_constant_IV_size=e.readUint8(),this.default_constant_IV=e.readUint8Array(this.default_constant_IV_size))})),f.createFullBoxCtor("tfdt",(function(e){1==this.version?this.baseMediaDecodeTime=e.readUint64():this.baseMediaDecodeTime=e.readUint32()})),f.createFullBoxCtor("tfhd",(function(e){var t=0;this.track_id=e.readUint32(),this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=e.readUint64(),t+=8):this.base_data_offset=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=e.readUint32(),t+=4):this.default_sample_description_index=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=e.readUint32(),t+=4):this.default_sample_duration=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=e.readUint32(),t+=4):this.default_sample_size=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=e.readUint32(),t+=4):this.default_sample_flags=0})),f.createFullBoxCtor("tfra",(function(e){this.track_ID=e.readUint32(),e.readUint24();var t=e.readUint8();this.length_size_of_traf_num=t>>4&3,this.length_size_of_trun_num=t>>2&3,this.length_size_of_sample_num=3&t,this.entries=[];for(var r=e.readUint32(),i=0;i<r;i++)1===this.version?(this.time=e.readUint64(),this.moof_offset=e.readUint64()):(this.time=e.readUint32(),this.moof_offset=e.readUint32()),this.traf_number=e["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=e["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=e["readUint"+8*(this.length_size_of_sample_num+1)]()})),f.createFullBoxCtor("tkhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint32()),e.readUint32Array(2),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),this.matrix=e.readInt32Array(9),this.width=e.readUint32(),this.height=e.readUint32()})),f.createBoxCtor("tmax",(function(e){this.time=e.readUint32()})),f.createBoxCtor("tmin",(function(e){this.time=e.readUint32()})),f.createBoxCtor("totl",(function(e){this.bytessent=e.readUint32()})),f.createBoxCtor("tpay",(function(e){this.bytessent=e.readUint32()})),f.createBoxCtor("tpyl",(function(e){this.bytessent=e.readUint64()})),f.TrackGroupTypeBox.prototype.parse=function(e){this.parseFullHeader(e),this.track_group_id=e.readUint32()},f.createTrackGroupCtor("msrc"),f.TrackReferenceTypeBox=function(e,t,r,i){f.Box.call(this,e,t),this.hdr_size=r,this.start=i},f.TrackReferenceTypeBox.prototype=new f.Box,f.TrackReferenceTypeBox.prototype.parse=function(e){this.track_ids=e.readUint32Array((this.size-this.hdr_size)/4)},f.trefBox.prototype.parse=function(e){for(var t,r;e.getPosition()<this.start+this.size;){if((t=f.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==f.OK)return;(r=new f.TrackReferenceTypeBox(t.type,t.size,t.hdr_size,t.start)).write===f.Box.prototype.write&&"mdat"!==r.type&&(s.info("BoxParser","TrackReference "+r.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),this.boxes.push(r)}},f.createFullBoxCtor("trep",(function(e){for(this.track_ID=e.readUint32(),this.boxes=[];e.getPosition()<this.start+this.size;){if(ret=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),ret.code!==f.OK)return;box=ret.box,this.boxes.push(box)}})),f.createFullBoxCtor("trex",(function(e){this.track_id=e.readUint32(),this.default_sample_description_index=e.readUint32(),this.default_sample_duration=e.readUint32(),this.default_sample_size=e.readUint32(),this.default_sample_flags=e.readUint32()})),f.createBoxCtor("trpy",(function(e){this.bytessent=e.readUint64()})),f.createFullBoxCtor("trun",(function(e){var t=0;if(this.sample_count=e.readUint32(),t+=4,this.size-this.hdr_size>t&&this.flags&f.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=e.readInt32(),t+=4):this.data_offset=0,this.size-this.hdr_size>t&&this.flags&f.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=e.readUint32(),t+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>t)for(var r=0;r<this.sample_count;r++)this.flags&f.TRUN_FLAGS_DURATION&&(this.sample_duration[r]=e.readUint32()),this.flags&f.TRUN_FLAGS_SIZE&&(this.sample_size[r]=e.readUint32()),this.flags&f.TRUN_FLAGS_FLAGS&&(this.sample_flags[r]=e.readUint32()),this.flags&f.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[r]=e.readUint32():this.sample_composition_time_offset[r]=e.readInt32())})),f.createFullBoxCtor("tsel",(function(e){this.switch_group=e.readUint32();var t=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var r=0;r<t;r++)this.attribute_list[r]=e.readUint32()})),f.createFullBoxCtor("txtC",(function(e){this.config=e.readCString()})),f.createBoxCtor("tyco",(function(e){var t=(this.size-this.hdr_size)/4;this.compatible_brands=[];for(var r=0;r<t;r++)this.compatible_brands[r]=e.readString(4)})),f.createFullBoxCtor("udes",(function(e){this.lang=e.readCString(),this.name=e.readCString(),this.description=e.readCString(),this.tags=e.readCString()})),f.createFullBoxCtor("uncC",(function(e){var t;if(this.profile=e.readUint32(),1==this.version);else if(0==this.version){for(this.component_count=e.readUint32(),this.component_index=[],this.component_bit_depth_minus_one=[],this.component_format=[],this.component_align_size=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16()),this.component_bit_depth_minus_one.push(e.readUint8()),this.component_format.push(e.readUint8()),this.component_align_size.push(e.readUint8());this.sampling_type=e.readUint8(),this.interleave_type=e.readUint8(),this.block_size=e.readUint8();var r=e.readUint8();this.component_little_endian=r>>7&1,this.block_pad_lsb=r>>6&1,this.block_little_endian=r>>5&1,this.block_reversed=r>>4&1,this.pad_unknown=r>>3&1,this.pixel_size=e.readUint32(),this.row_align_size=e.readUint32(),this.tile_align_size=e.readUint32(),this.num_tile_cols_minus_one=e.readUint32(),this.num_tile_rows_minus_one=e.readUint32()}})),f.createFullBoxCtor("url ",(function(e){1!==this.flags&&(this.location=e.readCString())})),f.createFullBoxCtor("urn ",(function(e){this.name=e.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=e.readCString())})),f.createUUIDBox("********************************",!0,!1,(function(e){this.LiveServerManifest=e.readString(this.size-this.hdr_size).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})),f.createUUIDBox("********************************",!0,!1,(function(e){this.system_id=f.parseHex16(e);var t=e.readUint32();t>0&&(this.data=e.readUint8Array(t))})),f.createUUIDBox("********************************",!0,!1),f.createUUIDBox("********************************",!0,!1,(function(e){this.default_AlgorithmID=e.readUint24(),this.default_IV_size=e.readUint8(),this.default_KID=f.parseHex16(e)})),f.createUUIDBox("********************************",!0,!1,(function(e){this.fragment_count=e.readUint8(),this.entries=[];for(var t=0;t<this.fragment_count;t++){var r={},i=0,n=0;1===this.version?(i=e.readUint64(),n=e.readUint64()):(i=e.readUint32(),n=e.readUint32()),r.absolute_time=i,r.absolute_duration=n,this.entries.push(r)}})),f.createUUIDBox("********************************",!0,!1,(function(e){1===this.version?(this.absolute_time=e.readUint64(),this.duration=e.readUint64()):(this.absolute_time=e.readUint32(),this.duration=e.readUint32())})),f.createFullBoxCtor("vmhd",(function(e){this.graphicsmode=e.readUint16(),this.opcolor=e.readUint16Array(3)})),f.createFullBoxCtor("vpcC",(function(e){var t;1===this.version?(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4,this.chromaSubsampling=t>>1&7,this.videoFullRangeFlag=1&t,this.colourPrimaries=e.readUint8(),this.transferCharacteristics=e.readUint8(),this.matrixCoefficients=e.readUint8(),this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize)):(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4&15,this.colorSpace=15&t,t=e.readUint8(),this.chromaSubsampling=t>>4&15,this.transferFunction=t>>1&7,this.videoFullRangeFlag=1&t,this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize))})),f.createBoxCtor("vttC",(function(e){this.text=e.readString(this.size-this.hdr_size)})),f.createFullBoxCtor("vvcC",(function(e){var t,r,i={held_bits:void 0,num_held_bits:0,stream_read_1_bytes:function(e){this.held_bits=e.readUint8(),this.num_held_bits=8},stream_read_2_bytes:function(e){this.held_bits=e.readUint16(),this.num_held_bits=16},extract_bits:function(e){var t=this.held_bits>>this.num_held_bits-e&(1<<e)-1;return this.num_held_bits-=e,t}};if(i.stream_read_1_bytes(e),i.extract_bits(5),this.lengthSizeMinusOne=i.extract_bits(2),this.ptl_present_flag=i.extract_bits(1),this.ptl_present_flag){if(i.stream_read_2_bytes(e),this.ols_idx=i.extract_bits(9),this.num_sublayers=i.extract_bits(3),this.constant_frame_rate=i.extract_bits(2),this.chroma_format_idc=i.extract_bits(2),i.stream_read_1_bytes(e),this.bit_depth_minus8=i.extract_bits(3),i.extract_bits(5),i.stream_read_2_bytes(e),i.extract_bits(2),this.num_bytes_constraint_info=i.extract_bits(6),this.general_profile_idc=i.extract_bits(7),this.general_tier_flag=i.extract_bits(1),this.general_level_idc=e.readUint8(),i.stream_read_1_bytes(e),this.ptl_frame_only_constraint_flag=i.extract_bits(1),this.ptl_multilayer_enabled_flag=i.extract_bits(1),this.general_constraint_info=new Uint8Array(this.num_bytes_constraint_info),this.num_bytes_constraint_info){for(t=0;t<this.num_bytes_constraint_info-1;t++){var n=i.extract_bits(6);i.stream_read_1_bytes(e);var o=i.extract_bits(2);this.general_constraint_info[t]=n<<2|o}this.general_constraint_info[this.num_bytes_constraint_info-1]=i.extract_bits(6)}else i.extract_bits(6);if(this.num_sublayers>1){for(i.stream_read_1_bytes(e),this.ptl_sublayer_present_mask=0,r=this.num_sublayers-2;r>=0;--r){var s=i.extract_bits(1);this.ptl_sublayer_present_mask|=s<<r}for(r=this.num_sublayers;r<=8&&this.num_sublayers>1;++r)i.extract_bits(1);for(this.sublayer_level_idc=[],r=this.num_sublayers-2;r>=0;--r)this.ptl_sublayer_present_mask&1<<r&&(this.sublayer_level_idc[r]=e.readUint8())}if(this.ptl_num_sub_profiles=e.readUint8(),this.general_sub_profile_idc=[],this.ptl_num_sub_profiles)for(t=0;t<this.ptl_num_sub_profiles;t++)this.general_sub_profile_idc.push(e.readUint32());this.max_picture_width=e.readUint16(),this.max_picture_height=e.readUint16(),this.avg_frame_rate=e.readUint16()}this.nalu_arrays=[];var a=e.readUint8();for(t=0;t<a;t++){var d=[];this.nalu_arrays.push(d),i.stream_read_1_bytes(e),d.completeness=i.extract_bits(1),i.extract_bits(2),d.nalu_type=i.extract_bits(5);var l=1;for(13!=d.nalu_type&&12!=d.nalu_type&&(l=e.readUint16()),r=0;r<l;r++){var u=e.readUint16();d.push({data:e.readUint8Array(u),length:u})}}})),f.createFullBoxCtor("vvnC",(function(e){var t=strm.readUint8();this.lengthSizeMinusOne=3&t})),f.SampleEntry.prototype.isVideo=function(){return!1},f.SampleEntry.prototype.isAudio=function(){return!1},f.SampleEntry.prototype.isSubtitle=function(){return!1},f.SampleEntry.prototype.isMetadata=function(){return!1},f.SampleEntry.prototype.isHint=function(){return!1},f.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},f.SampleEntry.prototype.getWidth=function(){return""},f.SampleEntry.prototype.getHeight=function(){return""},f.SampleEntry.prototype.getChannelCount=function(){return""},f.SampleEntry.prototype.getSampleRate=function(){return""},f.SampleEntry.prototype.getSampleSize=function(){return""},f.VisualSampleEntry.prototype.isVideo=function(){return!0},f.VisualSampleEntry.prototype.getWidth=function(){return this.width},f.VisualSampleEntry.prototype.getHeight=function(){return this.height},f.AudioSampleEntry.prototype.isAudio=function(){return!0},f.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},f.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},f.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},f.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},f.MetadataSampleEntry.prototype.isMetadata=function(){return!0},f.decimalToHex=function(e,t){var r=Number(e).toString(16);for(t=null==t?t=2:t;r.length<t;)r="0"+r;return r},f.avc1SampleEntry.prototype.getCodec=f.avc2SampleEntry.prototype.getCodec=f.avc3SampleEntry.prototype.getCodec=f.avc4SampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this);return this.avcC?e+"."+f.decimalToHex(this.avcC.AVCProfileIndication)+f.decimalToHex(this.avcC.profile_compatibility)+f.decimalToHex(this.avcC.AVCLevelIndication):e},f.hev1SampleEntry.prototype.getCodec=f.hvc1SampleEntry.prototype.getCodec=function(){var e,t=f.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(t+=".",this.hvcC.general_profile_space){case 0:t+="";break;case 1:t+="A";break;case 2:t+="B";break;case 3:t+="C"}t+=this.hvcC.general_profile_idc,t+=".";var r=this.hvcC.general_profile_compatibility,i=0;for(e=0;e<32&&(i|=1&r,31!=e);e++)i<<=1,r>>=1;t+=f.decimalToHex(i,0),t+=".",0===this.hvcC.general_tier_flag?t+="L":t+="H",t+=this.hvcC.general_level_idc;var n=!1,o="";for(e=5;e>=0;e--)(this.hvcC.general_constraint_indicator[e]||n)&&(o="."+f.decimalToHex(this.hvcC.general_constraint_indicator[e],0)+o,n=!0);t+=o}return t},f.vvc1SampleEntry.prototype.getCodec=f.vvi1SampleEntry.prototype.getCodec=function(){var e,t=f.SampleEntry.prototype.getCodec.call(this);if(this.vvcC){t+="."+this.vvcC.general_profile_idc,this.vvcC.general_tier_flag?t+=".H":t+=".L",t+=this.vvcC.general_level_idc;var r="";if(this.vvcC.general_constraint_info){var i,n=[],o=0;for(o|=this.vvcC.ptl_frame_only_constraint<<7,o|=this.vvcC.ptl_multilayer_enabled<<6,e=0;e<this.vvcC.general_constraint_info.length;++e)o|=this.vvcC.general_constraint_info[e]>>2&63,n.push(o),o&&(i=e),o=this.vvcC.general_constraint_info[e]>>2&3;if(void 0===i)r=".CA";else{r=".C";var s="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",a=0,d=0;for(e=0;e<=i;++e)for(a=a<<8|n[e],d+=8;d>=5;){r+=s[a>>d-5&31],a&=(1<<(d-=5))-1}d&&(r+=s[31&(a<<=5-d)])}}t+=r}return t},f.mp4aSampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var t=this.esds.esd.getOTI(),r=this.esds.esd.getAudioConfig();return e+"."+f.decimalToHex(t)+(r?"."+r:"")}return e},f.stxtSampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this);return this.mime_format?e+"."+this.mime_format:e},f.vp08SampleEntry.prototype.getCodec=f.vp09SampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this),t=this.vpcC.level;0==t&&(t="00");var r=this.vpcC.bitDepth;return 8==r&&(r="08"),e+".0"+this.vpcC.profile+"."+t+"."+r},f.av01SampleEntry.prototype.getCodec=function(){var e,t=f.SampleEntry.prototype.getCodec.call(this),r=this.av1C.seq_level_idx_0;return r<10&&(r="0"+r),2===this.av1C.seq_profile&&1===this.av1C.high_bitdepth?e=1===this.av1C.twelve_bit?"12":"10":this.av1C.seq_profile<=2&&(e=1===this.av1C.high_bitdepth?"10":"08"),t+"."+this.av1C.seq_profile+"."+r+(this.av1C.seq_tier_0?"H":"M")+"."+e},f.Box.prototype.writeHeader=function(e,t){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),s.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+e.getPosition()+(t||"")),this.size>l?e.writeUint32(1):(this.sizePosition=e.getPosition(),e.writeUint32(this.size)),e.writeString(this.type,null,4),"uuid"===this.type&&e.writeUint8Array(this.uuid),this.size>l&&e.writeUint64(this.size)},f.FullBox.prototype.writeHeader=function(e){this.size+=4,f.Box.prototype.writeHeader.call(this,e," v="+this.version+" f="+this.flags),e.writeUint8(this.version),e.writeUint24(this.flags)},f.Box.prototype.write=function(e){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(e),e.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(e),this.data&&e.writeUint8Array(this.data))},f.ContainerBox.prototype.write=function(e){this.size=0,this.writeHeader(e);for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&(this.boxes[t].write(e),this.size+=this.boxes[t].size);s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.TrackReferenceTypeBox.prototype.write=function(e){this.size=4*this.track_ids.length,this.writeHeader(e),e.writeUint32Array(this.track_ids)},f.avcCBox.prototype.write=function(e){var t;for(this.size=7,t=0;t<this.SPS.length;t++)this.size+=2+this.SPS[t].length;for(t=0;t<this.PPS.length;t++)this.size+=2+this.PPS[t].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.AVCProfileIndication),e.writeUint8(this.profile_compatibility),e.writeUint8(this.AVCLevelIndication),e.writeUint8(this.lengthSizeMinusOne+252),e.writeUint8(this.SPS.length+224),t=0;t<this.SPS.length;t++)e.writeUint16(this.SPS[t].length),e.writeUint8Array(this.SPS[t].nalu);for(e.writeUint8(this.PPS.length),t=0;t<this.PPS.length;t++)e.writeUint16(this.PPS[t].length),e.writeUint8Array(this.PPS[t].nalu);this.ext&&e.writeUint8Array(this.ext)},f.co64Box.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),t=0;t<this.chunk_offsets.length;t++)e.writeUint64(this.chunk_offsets[t])},f.cslgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeInt32(this.compositionToDTSShift),e.writeInt32(this.leastDecodeToDisplayDelta),e.writeInt32(this.greatestDecodeToDisplayDelta),e.writeInt32(this.compositionStartTime),e.writeInt32(this.compositionEndTime)},f.cttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),1===this.version?e.writeInt32(this.sample_offsets[t]):e.writeUint32(this.sample_offsets[t])},f.drefBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.elngBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(e),e.writeString(this.extended_language)},f.elstBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var r=this.entries[t];e.writeUint32(r.segment_duration),e.writeInt32(r.media_time),e.writeInt16(r.media_rate_integer),e.writeInt16(r.media_rate_fraction)}},f.emsgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(e),e.writeCString(this.scheme_id_uri),e.writeCString(this.value),e.writeUint32(this.timescale),e.writeUint32(this.presentation_time_delta),e.writeUint32(this.event_duration),e.writeUint32(this.id),e.writeUint8Array(this.message_data)},f.ftypBox.prototype.write=function(e){this.size=8+4*this.compatible_brands.length,this.writeHeader(e),e.writeString(this.major_brand,null,4),e.writeUint32(this.minor_version);for(var t=0;t<this.compatible_brands.length;t++)e.writeString(this.compatible_brands[t],null,4)},f.hdlrBox.prototype.write=function(e){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(e),e.writeUint32(0),e.writeString(this.handler,null,4),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeCString(this.name)},f.hvcCBox.prototype.write=function(e){var t,r;for(this.size=23,t=0;t<this.nalu_arrays.length;t++)for(this.size+=3,r=0;r<this.nalu_arrays[t].length;r++)this.size+=2+this.nalu_arrays[t][r].data.length;for(this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.general_profile_space<<6+this.general_tier_flag<<5+this.general_profile_idc),e.writeUint32(this.general_profile_compatibility),e.writeUint8Array(this.general_constraint_indicator),e.writeUint8(this.general_level_idc),e.writeUint16(this.min_spatial_segmentation_idc+(15<<24)),e.writeUint8(this.parallelismType+252),e.writeUint8(this.chroma_format_idc+252),e.writeUint8(this.bit_depth_luma_minus8+248),e.writeUint8(this.bit_depth_chroma_minus8+248),e.writeUint16(this.avgFrameRate),e.writeUint8((this.constantFrameRate<<6)+(this.numTemporalLayers<<3)+(this.temporalIdNested<<2)+this.lengthSizeMinusOne),e.writeUint8(this.nalu_arrays.length),t=0;t<this.nalu_arrays.length;t++)for(e.writeUint8((this.nalu_arrays[t].completeness<<7)+this.nalu_arrays[t].nalu_type),e.writeUint16(this.nalu_arrays[t].length),r=0;r<this.nalu_arrays[t].length;r++)e.writeUint16(this.nalu_arrays[t][r].data.length),e.writeUint8Array(this.nalu_arrays[t][r].data)},f.kindBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(e),e.writeCString(this.schemeURI),e.writeCString(this.value)},f.mdhdBox.prototype.write=function(e){this.size=20,this.flags=0,this.version=0,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint16(this.language),e.writeUint16(0)},f.mehdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.fragment_duration)},f.mfhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.sequence_number)},f.mvhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=96,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint32(this.rate),e.writeUint16(this.volume<<8),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32Array(this.matrix),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(this.next_track_id)},f.SampleEntry.prototype.writeHeader=function(e){this.size=8,f.Box.prototype.writeHeader.call(this,e),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint16(this.data_reference_index)},f.SampleEntry.prototype.writeFooter=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e),this.size+=this.boxes[t].size;s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.SampleEntry.prototype.write=function(e){this.writeHeader(e),e.writeUint8Array(this.data),this.size+=this.data.length,s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.VisualSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=70,e.writeUint16(0),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.width),e.writeUint16(this.height),e.writeUint32(this.horizresolution),e.writeUint32(this.vertresolution),e.writeUint32(0),e.writeUint16(this.frame_count),e.writeUint8(Math.min(31,this.compressorname.length)),e.writeString(this.compressorname,null,31),e.writeUint16(this.depth),e.writeInt16(-1),this.writeFooter(e)},f.AudioSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=20,e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.channel_count),e.writeUint16(this.samplesize),e.writeUint16(0),e.writeUint16(0),e.writeUint32(this.samplerate<<16),this.writeFooter(e)},f.stppSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=this.namespace.length+1+this.schema_location.length+1+this.auxiliary_mime_types.length+1,e.writeCString(this.namespace),e.writeCString(this.schema_location),e.writeCString(this.auxiliary_mime_types),this.writeFooter(e)},f.SampleGroupEntry.prototype.write=function(e){e.writeUint8Array(this.data)},f.sbgpBox.prototype.write=function(e){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(e),e.writeString(this.grouping_type,null,4),e.writeUint32(this.grouping_type_parameter),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var r=this.entries[t];e.writeInt32(r.sample_count),e.writeInt32(r.group_description_index)}},f.sgpdBox.prototype.write=function(e){var t,r;for(this.flags=0,this.size=12,t=0;t<this.entries.length;t++)r=this.entries[t],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=r.data.length);for(this.writeHeader(e),e.writeString(this.grouping_type,null,4),1===this.version&&e.writeUint32(this.default_length),this.version>=2&&e.writeUint32(this.default_sample_description_index),e.writeUint32(this.entries.length),t=0;t<this.entries.length;t++)r=this.entries[t],1===this.version&&0===this.default_length&&e.writeUint32(r.description_length),r.write(e)},f.sidxBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(e),e.writeUint32(this.reference_ID),e.writeUint32(this.timescale),e.writeUint32(this.earliest_presentation_time),e.writeUint32(this.first_offset),e.writeUint16(0),e.writeUint16(this.references.length);for(var t=0;t<this.references.length;t++){var r=this.references[t];e.writeUint32(r.reference_type<<31|r.referenced_size),e.writeUint32(r.subsegment_duration),e.writeUint32(r.starts_with_SAP<<31|r.SAP_type<<28|r.SAP_delta_time)}},f.smhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=4,this.writeHeader(e),e.writeUint16(this.balance),e.writeUint16(0)},f.stcoBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),e.writeUint32Array(this.chunk_offsets)},f.stscBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(e),e.writeUint32(this.first_chunk.length),t=0;t<this.first_chunk.length;t++)e.writeUint32(this.first_chunk[t]),e.writeUint32(this.samples_per_chunk[t]),e.writeUint32(this.sample_description_index[t])},f.stsdBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(e),e.writeUint32(this.entries.length),this.size+=4,t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.stshBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(e),e.writeUint32(this.shadowed_sample_numbers.length),t=0;t<this.shadowed_sample_numbers.length;t++)e.writeUint32(this.shadowed_sample_numbers[t]),e.writeUint32(this.sync_sample_numbers[t])},f.stssBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(e),e.writeUint32(this.sample_numbers.length),e.writeUint32Array(this.sample_numbers)},f.stszBox.prototype.write=function(e){var t,r=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0)for(t=0;t+1<this.sample_sizes.length;){if(this.sample_sizes[t+1]!==this.sample_sizes[0]){r=!1;break}t++}else r=!1;this.size=8,r||(this.size+=4*this.sample_sizes.length),this.writeHeader(e),r?e.writeUint32(this.sample_sizes[0]):e.writeUint32(0),e.writeUint32(this.sample_sizes.length),r||e.writeUint32Array(this.sample_sizes)},f.sttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),e.writeUint32(this.sample_deltas[t])},f.tfdtBox.prototype.write=function(e){var t=Math.pow(2,32)-1;this.version=this.baseMediaDecodeTime>t?1:0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(e),1===this.version?e.writeUint64(this.baseMediaDecodeTime):e.writeUint32(this.baseMediaDecodeTime)},f.tfhdBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&f.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&f.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&f.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&f.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&f.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(e),e.writeUint32(this.track_id),this.flags&f.TFHD_FLAG_BASE_DATA_OFFSET&&e.writeUint64(this.base_data_offset),this.flags&f.TFHD_FLAG_SAMPLE_DESC&&e.writeUint32(this.default_sample_description_index),this.flags&f.TFHD_FLAG_SAMPLE_DUR&&e.writeUint32(this.default_sample_duration),this.flags&f.TFHD_FLAG_SAMPLE_SIZE&&e.writeUint32(this.default_sample_size),this.flags&f.TFHD_FLAG_SAMPLE_FLAGS&&e.writeUint32(this.default_sample_flags)},f.tkhdBox.prototype.write=function(e){this.version=0,this.size=80,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.track_id),e.writeUint32(0),e.writeUint32(this.duration),e.writeUint32(0),e.writeUint32(0),e.writeInt16(this.layer),e.writeInt16(this.alternate_group),e.writeInt16(this.volume<<8),e.writeUint16(0),e.writeInt32Array(this.matrix),e.writeUint32(this.width),e.writeUint32(this.height)},f.trexBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeUint32(this.track_id),e.writeUint32(this.default_sample_description_index),e.writeUint32(this.default_sample_duration),e.writeUint32(this.default_sample_size),e.writeUint32(this.default_sample_flags)},f.trunBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&f.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&f.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&f.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&f.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&f.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&f.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(e),e.writeUint32(this.sample_count),this.flags&f.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=e.getPosition(),e.writeInt32(this.data_offset)),this.flags&f.TRUN_FLAGS_FIRST_FLAG&&e.writeUint32(this.first_sample_flags);for(var t=0;t<this.sample_count;t++)this.flags&f.TRUN_FLAGS_DURATION&&e.writeUint32(this.sample_duration[t]),this.flags&f.TRUN_FLAGS_SIZE&&e.writeUint32(this.sample_size[t]),this.flags&f.TRUN_FLAGS_FLAGS&&e.writeUint32(this.sample_flags[t]),this.flags&f.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?e.writeUint32(this.sample_composition_time_offset[t]):e.writeInt32(this.sample_composition_time_offset[t]))},f["url Box"].prototype.write=function(e){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(e),this.location&&e.writeCString(this.location)},f["urn Box"].prototype.write=function(e){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(e),e.writeCString(this.name),this.location&&e.writeCString(this.location)},f.vmhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=8,this.writeHeader(e),e.writeUint16(this.graphicsmode),e.writeUint16Array(this.opcolor)},f.cttsBox.prototype.unpack=function(e){var t,r,i;for(i=0,t=0;t<this.sample_counts.length;t++)for(r=0;r<this.sample_counts[t];r++)e[i].pts=e[i].dts+this.sample_offsets[t],i++},f.sttsBox.prototype.unpack=function(e){var t,r,i;for(i=0,t=0;t<this.sample_counts.length;t++)for(r=0;r<this.sample_counts[t];r++)e[i].dts=0===i?0:e[i-1].dts+this.sample_deltas[t],i++},f.stcoBox.prototype.unpack=function(e){var t;for(t=0;t<this.chunk_offsets.length;t++)e[t].offset=this.chunk_offsets[t]},f.stscBox.prototype.unpack=function(e){var t,r,i,n,o;for(n=0,o=0,t=0;t<this.first_chunk.length;t++)for(r=0;r<(t+1<this.first_chunk.length?this.first_chunk[t+1]:1/0);r++)for(o++,i=0;i<this.samples_per_chunk[t];i++){if(!e[n])return;e[n].description_index=this.sample_description_index[t],e[n].chunk_index=o,n++}},f.stszBox.prototype.unpack=function(e){var t;for(t=0;t<this.sample_sizes.length;t++)e[t].size=this.sample_sizes[t]},f.DIFF_BOXES_PROP_NAMES=["boxes","entries","references","subsamples","items","item_infos","extents","associations","subsegments","ranges","seekLists","seekPoints","esd","levels"],f.DIFF_PRIMITIVE_ARRAY_PROP_NAMES=["compatible_brands","matrix","opcolor","sample_counts","sample_counts","sample_deltas","first_chunk","samples_per_chunk","sample_sizes","chunk_offsets","sample_offsets","sample_description_index","sample_duration"],f.boxEqualFields=function(e,t){if(e&&!t)return!1;var r;for(r in e)if(!(f.DIFF_BOXES_PROP_NAMES.indexOf(r)>-1||e[r]instanceof f.Box||t[r]instanceof f.Box||void 0===e[r]||void 0===t[r]||"function"==typeof e[r]||"function"==typeof t[r]||e.subBoxNames&&e.subBoxNames.indexOf(r.slice(0,4))>-1||t.subBoxNames&&t.subBoxNames.indexOf(r.slice(0,4))>-1||"data"===r||"start"===r||"size"===r||"creation_time"===r||"modification_time"===r||f.DIFF_PRIMITIVE_ARRAY_PROP_NAMES.indexOf(r)>-1||e[r]===t[r]))return!1;return!0},f.boxEqual=function(e,t){if(!f.boxEqualFields(e,t))return!1;for(var r=0;r<f.DIFF_BOXES_PROP_NAMES.length;r++){var i=f.DIFF_BOXES_PROP_NAMES[r];if(e[i]&&t[i]&&!f.boxEqual(e[i],t[i]))return!1}return!0};var m=function(){};m.prototype.parseSample=function(e){var t,r={};r.resources=[];var i=new a(e.data.buffer);if(e.subsamples&&0!==e.subsamples.length){if(r.documentString=i.readString(e.subsamples[0].size),e.subsamples.length>1)for(t=1;t<e.subsamples.length;t++)r.resources[t]=i.readUint8Array(e.subsamples[t].size)}else r.documentString=i.readString(e.data.length);return"undefined"!=typeof DOMParser&&(r.document=(new DOMParser).parseFromString(r.documentString,"application/xml")),r};var _=function(){};_.prototype.parseSample=function(e){return new a(e.data.buffer).readString(e.data.length)},_.prototype.parseConfig=function(e){var t=new a(e.buffer);return t.readUint32(),t.readCString()},t.XMLSubtitlein4Parser=m,t.Textin4Parser=_;var g=function(e){this.stream=e||new u,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1};g.prototype.setSegmentOptions=function(e,t,r){var i=this.getTrackById(e);if(i){var n={};this.fragmentedTracks.push(n),n.id=e,n.user=t,n.trak=i,i.nextSample=0,n.segmentStream=null,n.nb_samples=1e3,n.rapAlignement=!0,r&&(r.nbSamples&&(n.nb_samples=r.nbSamples),r.rapAlignement&&(n.rapAlignement=r.rapAlignement))}},g.prototype.unsetSegmentOptions=function(e){for(var t=-1,r=0;r<this.fragmentedTracks.length;r++){this.fragmentedTracks[r].id==e&&(t=r)}t>-1&&this.fragmentedTracks.splice(t,1)},g.prototype.setExtractionOptions=function(e,t,r){var i=this.getTrackById(e);if(i){var n={};this.extractedTracks.push(n),n.id=e,n.user=t,n.trak=i,i.nextSample=0,n.nb_samples=1e3,n.samples=[],r&&r.nbSamples&&(n.nb_samples=r.nbSamples)}},g.prototype.unsetExtractionOptions=function(e){for(var t=-1,r=0;r<this.extractedTracks.length;r++){this.extractedTracks[r].id==e&&(t=r)}t>-1&&this.extractedTracks.splice(t,1)},g.prototype.parse=function(){var e,t;if(!this.restoreParsePosition||this.restoreParsePosition())for(;;){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),(e=f.parseOneBox(this.stream,false)).code===f.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(e))continue;return}return}var r;switch(r="uuid"!==(t=e.box).type?t.type:t.uuid,this.boxes.push(t),r){case"mdat":this.mdats.push(t);break;case"moof":this.moofs.push(t);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[r]&&s.warn("ISOFile","Duplicate Box of type: "+r+", overriding previous occurrence"),this[r]=t}this.updateUsedBytes&&this.updateUsedBytes(t,e)}},g.prototype.checkBuffer=function(e){if(null==e)throw"Buffer must be defined and non empty";if(void 0===e.fileStart)throw"Buffer must have a fileStart property";return 0===e.byteLength?(s.warn("ISOFile","Ignoring empty buffer (fileStart: "+e.fileStart+")"),this.stream.logBufferLevel(),!1):(s.info("ISOFile","Processing buffer (fileStart: "+e.fileStart+")"),e.usedBytes=0,this.stream.insertBuffer(e),this.stream.logBufferLevel(),!!this.stream.initialized()||(s.warn("ISOFile","Not ready to start parsing"),!1))},g.prototype.appendBuffer=function(e,t){var r;if(this.checkBuffer(e))return this.parse(),this.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.moov?(this.sampleListBuilt||(this.buildSampleLists(),this.sampleListBuilt=!0),this.updateSampleLists(),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples(t),this.nextSeekPosition?(r=this.nextSeekPosition,this.nextSeekPosition=void 0):r=this.nextParsePosition,this.stream.getEndFilePositionAfter&&(r=this.stream.getEndFilePositionAfter(r))):r=this.nextParsePosition?this.nextParsePosition:0,this.sidx&&this.onSidx&&!this.sidxSent&&(this.onSidx(this.sidx),this.sidxSent=!0),this.meta&&(this.flattenItemInfo&&!this.itemListBuilt&&(this.flattenItemInfo(),this.itemListBuilt=!0),this.processItems&&this.processItems(this.onItem)),this.stream.cleanBuffers&&(s.info("ISOFile","Done processing buffer (fileStart: "+e.fileStart+") - next buffer to fetch should have a fileStart position of "+r),this.stream.logBufferLevel(),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0),s.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize())),r},g.prototype.getInfo=function(){var e,t,r,i,n,o,s={},a=new Date("1904-01-01T00:00:00Z").getTime();if(this.moov)for(s.hasMoov=!0,s.duration=this.moov.mvhd.duration,s.timescale=this.moov.mvhd.timescale,s.isFragmented=null!=this.moov.mvex,s.isFragmented&&this.moov.mvex.mehd&&(s.fragment_duration=this.moov.mvex.mehd.fragment_duration),s.isProgressive=this.isProgressive,s.hasIOD=null!=this.moov.iods,s.brands=[],s.brands.push(this.ftyp.major_brand),s.brands=s.brands.concat(this.ftyp.compatible_brands),s.created=new Date(a+1e3*this.moov.mvhd.creation_time),s.modified=new Date(a+1e3*this.moov.mvhd.modification_time),s.tracks=[],s.audioTracks=[],s.videoTracks=[],s.subtitleTracks=[],s.metadataTracks=[],s.hintTracks=[],s.otherTracks=[],e=0;e<this.moov.traks.length;e++){if(o=(r=this.moov.traks[e]).mdia.minf.stbl.stsd.entries[0],i={},s.tracks.push(i),i.id=r.tkhd.track_id,i.name=r.mdia.hdlr.name,i.references=[],r.tref)for(t=0;t<r.tref.boxes.length;t++)n={},i.references.push(n),n.type=r.tref.boxes[t].type,n.track_ids=r.tref.boxes[t].track_ids;r.edts&&(i.edits=r.edts.elst.entries),i.created=new Date(a+1e3*r.tkhd.creation_time),i.modified=new Date(a+1e3*r.tkhd.modification_time),i.movie_duration=r.tkhd.duration,i.movie_timescale=s.timescale,i.layer=r.tkhd.layer,i.alternate_group=r.tkhd.alternate_group,i.volume=r.tkhd.volume,i.matrix=r.tkhd.matrix,i.track_width=r.tkhd.width/65536,i.track_height=r.tkhd.height/65536,i.timescale=r.mdia.mdhd.timescale,i.cts_shift=r.mdia.minf.stbl.cslg,i.duration=r.mdia.mdhd.duration,i.samples_duration=r.samples_duration,i.codec=o.getCodec(),i.kind=r.udta&&r.udta.kinds.length?r.udta.kinds[0]:{schemeURI:"",value:""},i.language=r.mdia.elng?r.mdia.elng.extended_language:r.mdia.mdhd.languageString,i.nb_samples=r.samples.length,i.size=r.samples_size,i.bitrate=8*i.size*i.timescale/i.samples_duration,o.isAudio()?(i.type="audio",s.audioTracks.push(i),i.audio={},i.audio.sample_rate=o.getSampleRate(),i.audio.channel_count=o.getChannelCount(),i.audio.sample_size=o.getSampleSize()):o.isVideo()?(i.type="video",s.videoTracks.push(i),i.video={},i.video.width=o.getWidth(),i.video.height=o.getHeight()):o.isSubtitle()?(i.type="subtitles",s.subtitleTracks.push(i)):o.isHint()?(i.type="metadata",s.hintTracks.push(i)):o.isMetadata()?(i.type="metadata",s.metadataTracks.push(i)):(i.type="metadata",s.otherTracks.push(i))}else s.hasMoov=!1;if(s.mime="",s.hasMoov&&s.tracks){for(s.videoTracks&&s.videoTracks.length>0?s.mime+='video/mp4; codecs="':s.audioTracks&&s.audioTracks.length>0?s.mime+='audio/mp4; codecs="':s.mime+='application/mp4; codecs="',e=0;e<s.tracks.length;e++)0!==e&&(s.mime+=","),s.mime+=s.tracks[e].codec;s.mime+='"; profiles="',s.mime+=this.ftyp.compatible_brands.join(),s.mime+='"'}return s},g.prototype.setNextSeekPositionFromSample=function(e){e&&(this.nextSeekPosition?this.nextSeekPosition=Math.min(e.offset+e.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=e.offset+e.alreadyRead)},g.prototype.processSamples=function(e){var t,r;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(t=0;t<this.fragmentedTracks.length;t++){var i=this.fragmentedTracks[t];for(r=i.trak;r.nextSample<r.samples.length&&this.sampleProcessingStarted;){s.debug("ISOFile","Creating media fragment on track #"+i.id+" for sample "+r.nextSample);var n=this.createFragment(i.id,r.nextSample,i.segmentStream);if(!n)break;if(i.segmentStream=n,r.nextSample++,(r.nextSample%i.nb_samples==0||e||r.nextSample>=r.samples.length)&&(s.info("ISOFile","Sending fragmented data on track #"+i.id+" for samples ["+Math.max(0,r.nextSample-i.nb_samples)+","+(r.nextSample-1)+"]"),s.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(i.id,i.user,i.segmentStream.buffer,r.nextSample,e||r.nextSample>=r.samples.length),i.segmentStream=null,i!==this.fragmentedTracks[t]))break}}if(null!==this.onSamples)for(t=0;t<this.extractedTracks.length;t++){var o=this.extractedTracks[t];for(r=o.trak;r.nextSample<r.samples.length&&this.sampleProcessingStarted;){s.debug("ISOFile","Exporting on track #"+o.id+" sample #"+r.nextSample);var a=this.getSample(r,r.nextSample);if(!a){this.setNextSeekPositionFromSample(r.samples[r.nextSample]);break}if(r.nextSample++,o.samples.push(a),(r.nextSample%o.nb_samples==0||r.nextSample>=r.samples.length)&&(s.debug("ISOFile","Sending samples on track #"+o.id+" for sample "+r.nextSample),this.onSamples&&this.onSamples(o.id,o.user,o.samples),o.samples=[],o!==this.extractedTracks[t]))break}}}},g.prototype.getBox=function(e){var t=this.getBoxes(e,!0);return t.length?t[0]:null},g.prototype.getBoxes=function(e,t){var r=[];return g._sweep.call(this,e,r,t),r},g._sweep=function(e,t,r){for(var i in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&r)return;g._sweep.call(this.boxes[i],e,t,r)}},g.prototype.getTrackSamplesInfo=function(e){var t=this.getTrackById(e);return t?t.samples:void 0},g.prototype.getTrackSample=function(e,t){var r=this.getTrackById(e);return this.getSample(r,t)},g.prototype.releaseUsedSamples=function(e,t){var r=0,i=this.getTrackById(e);i.lastValidSample||(i.lastValidSample=0);for(var n=i.lastValidSample;n<t;n++)r+=this.releaseSample(i,n);s.info("ISOFile","Track #"+e+" released samples up to "+t+" (released size: "+r+", remaining: "+this.samplesDataSize+")"),i.lastValidSample=t},g.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples(!1)},g.prototype.stop=function(){this.sampleProcessingStarted=!1},g.prototype.flush=function(){s.info("ISOFile","Flushing remaining samples"),this.updateSampleLists(),this.processSamples(!0),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0)},g.prototype.seekTrack=function(e,t,r){var i,n,o,a,d=0,l=0;if(0===r.samples.length)return s.info("ISOFile","No sample in track, cannot seek! Using time "+s.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(i=0;i<r.samples.length;i++){if(n=r.samples[i],0===i)l=0,a=n.timescale;else if(n.cts>e*n.timescale){l=i-1;break}t&&n.is_sync&&(d=i)}for(t&&(l=d),e=r.samples[l].cts,r.nextSample=l;r.samples[l].alreadyRead===r.samples[l].size&&r.samples[l+1];)l++;return o=r.samples[l].offset+r.samples[l].alreadyRead,s.info("ISOFile","Seeking to "+(t?"RAP":"")+" sample #"+r.nextSample+" on track "+r.tkhd.track_id+", time "+s.getDurationString(e,a)+" and offset: "+o),{offset:o,time:e/a}},g.prototype.getTrackDuration=function(e){var t;return e.samples?((t=e.samples[e.samples.length-1]).cts+t.duration)/t.timescale:1/0},g.prototype.seek=function(e,t){var r,i,n,o=this.moov,a={offset:1/0,time:1/0};if(this.moov){for(n=0;n<o.traks.length;n++)r=o.traks[n],e>this.getTrackDuration(r)||((i=this.seekTrack(e,t,r)).offset<a.offset&&(a.offset=i.offset),i.time<a.time&&(a.time=i.time));return s.info("ISOFile","Seeking at time "+s.getDurationString(a.time,1)+" needs a buffer with a fileStart position of "+a.offset),a.offset===1/0?a={offset:this.nextParsePosition,time:0}:a.offset=this.stream.getEndFilePositionAfter(a.offset),s.info("ISOFile","Adjusted seek position (after checking data already in buffer): "+a.offset),a}throw"Cannot seek: moov not received!"},g.prototype.equal=function(e){for(var t=0;t<this.boxes.length&&t<e.boxes.length;){var r=this.boxes[t],i=e.boxes[t];if(!f.boxEqual(r,i))return!1;t++}return!0},t.ISOFile=g,g.prototype.lastBoxStartPosition=0,g.prototype.parsingMdat=null,g.prototype.nextParsePosition=0,g.prototype.discardMdatData=!1,g.prototype.processIncompleteBox=function(e){var t;return"mdat"===e.type?(t=new f[e.type+"Box"](e.size),this.parsingMdat=t,this.boxes.push(t),this.mdats.push(t),t.start=e.start,t.hdr_size=e.hdr_size,this.stream.addUsedBytes(t.hdr_size),this.lastBoxStartPosition=t.start+t.size,this.stream.seek(t.start+t.size,!1,this.discardMdatData)?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=t.start+t.size,!1)):("moov"===e.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),!!this.stream.mergeNextBuffer&&this.stream.mergeNextBuffer()?(this.nextParsePosition=this.stream.getEndPosition(),!0):(e.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+e.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},g.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},g.prototype.processIncompleteMdat=function(){var e;return e=this.parsingMdat,this.stream.seek(e.start+e.size,!1,this.discardMdatData)?(s.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},g.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},g.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},g.prototype.updateUsedBytes=function(e,t){this.stream.addUsedBytes&&("mdat"===e.type?(this.stream.addUsedBytes(e.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(e.size-e.hdr_size)):this.stream.addUsedBytes(e.size))},g.prototype.add=f.Box.prototype.add,g.prototype.addBox=f.Box.prototype.addBox,g.prototype.init=function(e){var t=e||{};this.add("ftyp").set("major_brand",t.brands&&t.brands[0]||"iso4").set("minor_version",0).set("compatible_brands",t.brands||["iso4"]);var r=this.add("moov");return r.add("mvhd").set("timescale",t.timescale||600).set("rate",t.rate||65536).set("creation_time",0).set("modification_time",0).set("duration",t.duration||0).set("volume",t.width?0:256).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("next_track_id",1),r.add("mvex"),this},g.prototype.addTrack=function(e){this.moov||this.init(e);var t=e||{};t.width=t.width||320,t.height=t.height||320,t.id=t.id||this.moov.mvhd.next_track_id,t.type=t.type||"avc1";var r=this.moov.add("trak");this.moov.mvhd.next_track_id=t.id+1,r.add("tkhd").set("flags",f.TKHD_FLAG_ENABLED|f.TKHD_FLAG_IN_MOVIE|f.TKHD_FLAG_IN_PREVIEW).set("creation_time",0).set("modification_time",0).set("track_id",t.id).set("duration",t.duration||0).set("layer",t.layer||0).set("alternate_group",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("width",t.width<<16).set("height",t.height<<16);var i=r.add("mdia");i.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",t.timescale||1).set("duration",t.media_duration||0).set("language",t.language||"und"),i.add("hdlr").set("handler",t.hdlr||"vide").set("name",t.name||"Track created with MP4Box.js"),i.add("elng").set("extended_language",t.language||"fr-FR");var n=i.add("minf");if(void 0!==f[t.type+"SampleEntry"]){var o=new f[t.type+"SampleEntry"];o.data_reference_index=1;var s="";for(var d in f.sampleEntryCodes)for(var l=f.sampleEntryCodes[d],u=0;u<l.length;u++)if(l.indexOf(t.type)>-1){s=d;break}switch(s){case"Visual":if(n.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),o.set("width",t.width).set("height",t.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",t.type+" Compressor").set("depth",24),t.avcDecoderConfigRecord){var c=new f.avcCBox;c.parse(new a(t.avcDecoderConfigRecord)),o.addBox(c)}else if(t.hevcDecoderConfigRecord){var h=new f.hvcCBox;h.parse(new a(t.hevcDecoderConfigRecord)),o.addBox(h)}break;case"Audio":n.add("smhd").set("balance",t.balance||0),o.set("channel_count",t.channel_count||2).set("samplesize",t.samplesize||16).set("samplerate",t.samplerate||65536);break;case"Hint":n.add("hmhd");break;case"Subtitle":if(n.add("sthd"),"stpp"===t.type)o.set("namespace",t.namespace||"nonamespace").set("schema_location",t.schema_location||"").set("auxiliary_mime_types",t.auxiliary_mime_types||"");break;default:n.add("nmhd")}t.description&&o.addBox(t.description),t.description_boxes&&t.description_boxes.forEach((function(e){o.addBox(e)})),n.add("dinf").add("dref").addEntry((new f["url Box"]).set("flags",1));var p=n.add("stbl");return p.add("stsd").addEntry(o),p.add("stts").set("sample_counts",[]).set("sample_deltas",[]),p.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),p.add("stco").set("chunk_offsets",[]),p.add("stsz").set("sample_sizes",[]),this.moov.mvex.add("trex").set("track_id",t.id).set("default_sample_description_index",t.default_sample_description_index||1).set("default_sample_duration",t.default_sample_duration||0).set("default_sample_size",t.default_sample_size||0).set("default_sample_flags",t.default_sample_flags||0),this.buildTrakSampleLists(r),t.id}},f.Box.prototype.computeSize=function(e){var t=e||new d;t.endianness=d.BIG_ENDIAN,this.write(t)},g.prototype.addSample=function(e,t,r){var i=r||{},n={},o=this.getTrackById(e);if(null!==o){n.number=o.samples.length,n.track_id=o.tkhd.track_id,n.timescale=o.mdia.mdhd.timescale,n.description_index=i.sample_description_index?i.sample_description_index-1:0,n.description=o.mdia.minf.stbl.stsd.entries[n.description_index],n.data=t,n.size=t.byteLength,n.alreadyRead=n.size,n.duration=i.duration||1,n.cts=i.cts||0,n.dts=i.dts||0,n.is_sync=i.is_sync||!1,n.is_leading=i.is_leading||0,n.depends_on=i.depends_on||0,n.is_depended_on=i.is_depended_on||0,n.has_redundancy=i.has_redundancy||0,n.degradation_priority=i.degradation_priority||0,n.offset=0,n.subsamples=i.subsamples,o.samples.push(n),o.samples_size+=n.size,o.samples_duration+=n.duration,void 0===o.first_dts&&(o.first_dts=i.dts),this.processSamples();var s=this.createSingleSampleMoof(n);return this.addBox(s),s.computeSize(),s.trafs[0].truns[0].data_offset=s.size+8,this.add("mdat").data=new Uint8Array(t),n}},g.prototype.createSingleSampleMoof=function(e){var t=0;t=e.is_sync?1<<25:65536;var r=new f.moofBox;r.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var i=r.add("traf"),n=this.getTrackById(e.track_id);return i.add("tfhd").set("track_id",e.track_id).set("flags",f.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),i.add("tfdt").set("baseMediaDecodeTime",e.dts-(n.first_dts||0)),i.add("trun").set("flags",f.TRUN_FLAGS_DATA_OFFSET|f.TRUN_FLAGS_DURATION|f.TRUN_FLAGS_SIZE|f.TRUN_FLAGS_FLAGS|f.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[e.duration]).set("sample_size",[e.size]).set("sample_flags",[t]).set("sample_composition_time_offset",[e.cts-e.dts]),r},g.prototype.lastMoofIndex=0,g.prototype.samplesDataSize=0,g.prototype.resetTables=function(){var e,t,r,i,n,o;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,e=0;e<this.moov.traks.length;e++){(t=this.moov.traks[e]).tkhd.duration=0,t.mdia.mdhd.duration=0,(t.mdia.minf.stbl.stco||t.mdia.minf.stbl.co64).chunk_offsets=[],(r=t.mdia.minf.stbl.stsc).first_chunk=[],r.samples_per_chunk=[],r.sample_description_index=[],(t.mdia.minf.stbl.stsz||t.mdia.minf.stbl.stz2).sample_sizes=[],(i=t.mdia.minf.stbl.stts).sample_counts=[],i.sample_deltas=[],(n=t.mdia.minf.stbl.ctts)&&(n.sample_counts=[],n.sample_offsets=[]),o=t.mdia.minf.stbl.stss;var s=t.mdia.minf.stbl.boxes.indexOf(o);-1!=s&&(t.mdia.minf.stbl.boxes[s]=null)}},g.initSampleGroups=function(e,t,r,i,n){var o,s,a,d;function l(e,t,r){this.grouping_type=e,this.grouping_type_parameter=t,this.sbgp=r,this.last_sample_in_run=-1,this.entry_index=-1}for(t&&(t.sample_groups_info=[]),e.sample_groups_info||(e.sample_groups_info=[]),s=0;s<r.length;s++){for(d=r[s].grouping_type+"/"+r[s].grouping_type_parameter,a=new l(r[s].grouping_type,r[s].grouping_type_parameter,r[s]),t&&(t.sample_groups_info[d]=a),e.sample_groups_info[d]||(e.sample_groups_info[d]=a),o=0;o<i.length;o++)i[o].grouping_type===r[s].grouping_type&&(a.description=i[o],a.description.used=!0);if(n)for(o=0;o<n.length;o++)n[o].grouping_type===r[s].grouping_type&&(a.fragment_description=n[o],a.fragment_description.used=!0,a.is_fragment=!0)}if(t){if(n)for(s=0;s<n.length;s++)!n[s].used&&n[s].version>=2&&(d=n[s].grouping_type+"/0",(a=new l(n[s].grouping_type,0)).is_fragment=!0,t.sample_groups_info[d]||(t.sample_groups_info[d]=a))}else for(s=0;s<i.length;s++)!i[s].used&&i[s].version>=2&&(d=i[s].grouping_type+"/0",a=new l(i[s].grouping_type,0),e.sample_groups_info[d]||(e.sample_groups_info[d]=a))},g.setSampleGroupProperties=function(e,t,r,i){var n,o;for(n in t.sample_groups=[],i){var s;if(t.sample_groups[n]={},t.sample_groups[n].grouping_type=i[n].grouping_type,t.sample_groups[n].grouping_type_parameter=i[n].grouping_type_parameter,r>=i[n].last_sample_in_run&&(i[n].last_sample_in_run<0&&(i[n].last_sample_in_run=0),i[n].entry_index++,i[n].entry_index<=i[n].sbgp.entries.length-1&&(i[n].last_sample_in_run+=i[n].sbgp.entries[i[n].entry_index].sample_count)),i[n].entry_index<=i[n].sbgp.entries.length-1?t.sample_groups[n].group_description_index=i[n].sbgp.entries[i[n].entry_index].group_description_index:t.sample_groups[n].group_description_index=-1,0!==t.sample_groups[n].group_description_index)s=i[n].fragment_description?i[n].fragment_description:i[n].description,t.sample_groups[n].group_description_index>0?(o=t.sample_groups[n].group_description_index>65535?(t.sample_groups[n].group_description_index>>16)-1:t.sample_groups[n].group_description_index-1,s&&o>=0&&(t.sample_groups[n].description=s.entries[o])):s&&s.version>=2&&s.default_group_description_index>0&&(t.sample_groups[n].description=s.entries[s.default_group_description_index-1])}},g.process_sdtp=function(e,t,r){t&&(e?(t.is_leading=e.is_leading[r],t.depends_on=e.sample_depends_on[r],t.is_depended_on=e.sample_is_depended_on[r],t.has_redundancy=e.sample_has_redundancy[r]):(t.is_leading=0,t.depends_on=0,t.is_depended_on=0,t.has_redundancy=0))},g.prototype.buildSampleLists=function(){var e,t;for(e=0;e<this.moov.traks.length;e++)t=this.moov.traks[e],this.buildTrakSampleLists(t)},g.prototype.buildTrakSampleLists=function(e){var t,r,i,n,o,s,a,d,l,u,c,f,h,p,m,_,y,v,b,w,S,E,A,U;if(e.samples=[],e.samples_duration=0,e.samples_size=0,r=e.mdia.minf.stbl.stco||e.mdia.minf.stbl.co64,i=e.mdia.minf.stbl.stsc,n=e.mdia.minf.stbl.stsz||e.mdia.minf.stbl.stz2,o=e.mdia.minf.stbl.stts,s=e.mdia.minf.stbl.ctts,a=e.mdia.minf.stbl.stss,d=e.mdia.minf.stbl.stsd,l=e.mdia.minf.stbl.subs,f=e.mdia.minf.stbl.stdp,u=e.mdia.minf.stbl.sbgps,c=e.mdia.minf.stbl.sgpds,v=-1,b=-1,w=-1,S=-1,E=0,A=0,U=0,g.initSampleGroups(e,null,u,c),void 0!==n){for(t=0;t<n.sample_sizes.length;t++){var T={};T.number=t,T.track_id=e.tkhd.track_id,T.timescale=e.mdia.mdhd.timescale,T.alreadyRead=0,e.samples[t]=T,T.size=n.sample_sizes[t],e.samples_size+=T.size,0===t?(p=1,h=0,T.chunk_index=p,T.chunk_run_index=h,y=i.samples_per_chunk[h],_=0,m=h+1<i.first_chunk.length?i.first_chunk[h+1]-1:1/0):t<y?(T.chunk_index=p,T.chunk_run_index=h):(p++,T.chunk_index=p,_=0,p<=m||(m=++h+1<i.first_chunk.length?i.first_chunk[h+1]-1:1/0),T.chunk_run_index=h,y+=i.samples_per_chunk[h]),T.description_index=i.sample_description_index[T.chunk_run_index]-1,T.description=d.entries[T.description_index],T.offset=r.chunk_offsets[T.chunk_index-1]+_,_+=T.size,t>v&&(b++,v<0&&(v=0),v+=o.sample_counts[b]),t>0?(e.samples[t-1].duration=o.sample_deltas[b],e.samples_duration+=e.samples[t-1].duration,T.dts=e.samples[t-1].dts+e.samples[t-1].duration):T.dts=0,s?(t>=w&&(S++,w<0&&(w=0),w+=s.sample_counts[S]),T.cts=e.samples[t].dts+s.sample_offsets[S]):T.cts=T.dts,a?(t==a.sample_numbers[E]-1?(T.is_sync=!0,E++):(T.is_sync=!1,T.degradation_priority=0),l&&l.entries[A].sample_delta+U==t+1&&(T.subsamples=l.entries[A].subsamples,U+=l.entries[A].sample_delta,A++)):T.is_sync=!0,g.process_sdtp(e.mdia.minf.stbl.sdtp,T,T.number),T.degradation_priority=f?f.priority[t]:0,l&&l.entries[A].sample_delta+U==t&&(T.subsamples=l.entries[A].subsamples,U+=l.entries[A].sample_delta),(u.length>0||c.length>0)&&g.setSampleGroupProperties(e,T,t,e.sample_groups_info)}t>0&&(e.samples[t-1].duration=Math.max(e.mdia.mdhd.duration-e.samples[t-1].dts,0),e.samples_duration+=e.samples[t-1].duration)}},g.prototype.updateSampleLists=function(){var e,t,r,i,n,o,s,a,d,l,u,c,h,p,m;if(void 0!==this.moov)for(;this.lastMoofIndex<this.moofs.length;)if(d=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==d.type)for(l=d,e=0;e<l.trafs.length;e++){for(u=l.trafs[e],c=this.getTrackById(u.tfhd.track_id),h=this.getTrexById(u.tfhd.track_id),i=u.tfhd.flags&f.TFHD_FLAG_SAMPLE_DESC?u.tfhd.default_sample_description_index:h?h.default_sample_description_index:1,n=u.tfhd.flags&f.TFHD_FLAG_SAMPLE_DUR?u.tfhd.default_sample_duration:h?h.default_sample_duration:0,o=u.tfhd.flags&f.TFHD_FLAG_SAMPLE_SIZE?u.tfhd.default_sample_size:h?h.default_sample_size:0,s=u.tfhd.flags&f.TFHD_FLAG_SAMPLE_FLAGS?u.tfhd.default_sample_flags:h?h.default_sample_flags:0,u.sample_number=0,u.sbgps.length>0&&g.initSampleGroups(c,u,u.sbgps,c.mdia.minf.stbl.sgpds,u.sgpds),t=0;t<u.truns.length;t++){var _=u.truns[t];for(r=0;r<_.sample_count;r++)if((p={}).moof_number=this.lastMoofIndex,p.number_in_traf=u.sample_number,u.sample_number++,c.samples){p.number=c.samples.length,u.first_sample_index=c.samples.length,c.samples.push(p),p.track_id=c.tkhd.track_id,p.timescale=c.mdia.mdhd.timescale,p.description_index=i-1,p.description=c.mdia.minf.stbl.stsd.entries[p.description_index],p.size=o,_.flags&f.TRUN_FLAGS_SIZE&&(p.size=_.sample_size[r]),c.samples_size+=p.size,p.duration=n,_.flags&f.TRUN_FLAGS_DURATION&&(p.duration=_.sample_duration[r]),c.samples_duration+=p.duration,c.first_traf_merged||r>0?p.dts=c.samples[c.samples.length-2].dts+c.samples[c.samples.length-2].duration:(u.tfdt?p.dts=u.tfdt.baseMediaDecodeTime:p.dts=0,c.first_traf_merged=!0),p.cts=p.dts,_.flags&f.TRUN_FLAGS_CTS_OFFSET&&(p.cts=p.dts+_.sample_composition_time_offset[r]),m=s,_.flags&f.TRUN_FLAGS_FLAGS?m=_.sample_flags[r]:0===r&&_.flags&f.TRUN_FLAGS_FIRST_FLAG&&(m=_.first_sample_flags),p.is_sync=!(m>>16&1),p.is_leading=m>>26&3,p.depends_on=m>>24&3,p.is_depended_on=m>>22&3,p.has_redundancy=m>>20&3,p.degradation_priority=65535&m;var y=!!(u.tfhd.flags&f.TFHD_FLAG_BASE_DATA_OFFSET),v=!!(u.tfhd.flags&f.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),b=!!(_.flags&f.TRUN_FLAGS_DATA_OFFSET),w=0;w=y?u.tfhd.base_data_offset:v||0===t?l.start:a,p.offset=0===t&&0===r?b?w+_.data_offset:w:a,a=p.offset+p.size,(u.sbgps.length>0||u.sgpds.length>0||c.mdia.minf.stbl.sbgps.length>0||c.mdia.minf.stbl.sgpds.length>0)&&g.setSampleGroupProperties(c,p,p.number_in_traf,u.sample_groups_info)}}if(u.subs){c.has_fragment_subsamples=!0;var S=u.first_sample_index;for(t=0;t<u.subs.entries.length;t++)S+=u.subs.entries[t].sample_delta,(p=c.samples[S-1]).subsamples=u.subs.entries[t].subsamples}}},g.prototype.getSample=function(e,t){var r,i=e.samples[t];if(!this.moov)return null;if(i.data){if(i.alreadyRead==i.size)return i}else i.data=new Uint8Array(i.size),i.alreadyRead=0,this.samplesDataSize+=i.size,s.debug("ISOFile","Allocating sample #"+t+" on track #"+e.tkhd.track_id+" of size "+i.size+" (total: "+this.samplesDataSize+")");for(;;){var n=this.stream.findPosition(!0,i.offset+i.alreadyRead,!1);if(!(n>-1))return null;var o=(r=this.stream.buffers[n]).byteLength-(i.offset+i.alreadyRead-r.fileStart);if(i.size-i.alreadyRead<=o)return s.debug("ISOFile","Getting sample #"+t+" data (alreadyRead: "+i.alreadyRead+" offset: "+(i.offset+i.alreadyRead-r.fileStart)+" read size: "+(i.size-i.alreadyRead)+" full size: "+i.size+")"),d.memcpy(i.data.buffer,i.alreadyRead,r,i.offset+i.alreadyRead-r.fileStart,i.size-i.alreadyRead),r.usedBytes+=i.size-i.alreadyRead,this.stream.logBufferLevel(),i.alreadyRead=i.size,i;if(0===o)return null;s.debug("ISOFile","Getting sample #"+t+" partial data (alreadyRead: "+i.alreadyRead+" offset: "+(i.offset+i.alreadyRead-r.fileStart)+" read size: "+o+" full size: "+i.size+")"),d.memcpy(i.data.buffer,i.alreadyRead,r,i.offset+i.alreadyRead-r.fileStart,o),i.alreadyRead+=o,r.usedBytes+=o,this.stream.logBufferLevel()}},g.prototype.releaseSample=function(e,t){var r=e.samples[t];return r.data?(this.samplesDataSize-=r.size,r.data=null,r.alreadyRead=0,r.size):0},g.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},g.prototype.getCodecs=function(){var e,t="";for(e=0;e<this.moov.traks.length;e++){e>0&&(t+=","),t+=this.moov.traks[e].mdia.minf.stbl.stsd.entries[0].getCodec()}return t},g.prototype.getTrexById=function(e){var t;if(!this.moov||!this.moov.mvex)return null;for(t=0;t<this.moov.mvex.trexs.length;t++){var r=this.moov.mvex.trexs[t];if(r.track_id==e)return r}return null},g.prototype.getTrackById=function(e){if(void 0===this.moov)return null;for(var t=0;t<this.moov.traks.length;t++){var r=this.moov.traks[t];if(r.tkhd.track_id==e)return r}return null},g.prototype.items=[],g.prototype.entity_groups=[],g.prototype.itemsDataSize=0,g.prototype.flattenItemInfo=function(){var e,t,r,i=this.items,n=this.entity_groups,o=this.meta;if(null!=o&&void 0!==o.hdlr&&void 0!==o.iinf){for(e=0;e<o.iinf.item_infos.length;e++)(r={}).id=o.iinf.item_infos[e].item_ID,i[r.id]=r,r.ref_to=[],r.name=o.iinf.item_infos[e].item_name,o.iinf.item_infos[e].protection_index>0&&(r.protection=o.ipro.protections[o.iinf.item_infos[e].protection_index-1]),o.iinf.item_infos[e].item_type?r.type=o.iinf.item_infos[e].item_type:r.type="mime",r.content_type=o.iinf.item_infos[e].content_type,r.content_encoding=o.iinf.item_infos[e].content_encoding;if(o.grpl)for(e=0;e<o.grpl.boxes.length;e++)entity_group={},entity_group.id=o.grpl.boxes[e].group_id,entity_group.entity_ids=o.grpl.boxes[e].entity_ids,entity_group.type=o.grpl.boxes[e].type,n[entity_group.id]=entity_group;if(o.iloc)for(e=0;e<o.iloc.items.length;e++){var a=o.iloc.items[e];switch(r=i[a.item_ID],0!==a.data_reference_index&&(s.warn("Item storage with reference to other files: not supported"),r.source=o.dinf.boxes[a.data_reference_index-1]),a.construction_method){case 0:break;case 1:case 2:s.warn("Item storage with construction_method : not supported")}for(r.extents=[],r.size=0,t=0;t<a.extents.length;t++)r.extents[t]={},r.extents[t].offset=a.extents[t].extent_offset+a.base_offset,r.extents[t].length=a.extents[t].extent_length,r.extents[t].alreadyRead=0,r.size+=r.extents[t].length}if(o.pitm&&(i[o.pitm.item_id].primary=!0),o.iref)for(e=0;e<o.iref.references.length;e++){var d=o.iref.references[e];for(t=0;t<d.references.length;t++)i[d.from_item_ID].ref_to.push({type:d.type,id:d.references[t]})}if(o.iprp)for(var l=0;l<o.iprp.ipmas.length;l++){var u=o.iprp.ipmas[l];for(e=0;e<u.associations.length;e++){var c=u.associations[e];if((r=i[c.id])||(r=n[c.id]),r)for(void 0===r.properties&&(r.properties={},r.properties.boxes=[]),t=0;t<c.props.length;t++){var f=c.props[t];if(f.property_index>0&&f.property_index-1<o.iprp.ipco.boxes.length){var h=o.iprp.ipco.boxes[f.property_index-1];r.properties[h.type]=h,r.properties.boxes.push(h)}}}}}},g.prototype.getItem=function(e){var t,r;if(!this.meta)return null;if(!(r=this.items[e]).data&&r.size)r.data=new Uint8Array(r.size),r.alreadyRead=0,this.itemsDataSize+=r.size,s.debug("ISOFile","Allocating item #"+e+" of size "+r.size+" (total: "+this.itemsDataSize+")");else if(r.alreadyRead===r.size)return r;for(var i=0;i<r.extents.length;i++){var n=r.extents[i];if(n.alreadyRead!==n.length){var o=this.stream.findPosition(!0,n.offset+n.alreadyRead,!1);if(!(o>-1))return null;var a=(t=this.stream.buffers[o]).byteLength-(n.offset+n.alreadyRead-t.fileStart);if(!(n.length-n.alreadyRead<=a))return s.debug("ISOFile","Getting item #"+e+" extent #"+i+" partial data (alreadyRead: "+n.alreadyRead+" offset: "+(n.offset+n.alreadyRead-t.fileStart)+" read size: "+a+" full extent size: "+n.length+" full item size: "+r.size+")"),d.memcpy(r.data.buffer,r.alreadyRead,t,n.offset+n.alreadyRead-t.fileStart,a),n.alreadyRead+=a,r.alreadyRead+=a,t.usedBytes+=a,this.stream.logBufferLevel(),null;s.debug("ISOFile","Getting item #"+e+" extent #"+i+" data (alreadyRead: "+n.alreadyRead+" offset: "+(n.offset+n.alreadyRead-t.fileStart)+" read size: "+(n.length-n.alreadyRead)+" full extent size: "+n.length+" full item size: "+r.size+")"),d.memcpy(r.data.buffer,r.alreadyRead,t,n.offset+n.alreadyRead-t.fileStart,n.length-n.alreadyRead),t.usedBytes+=n.length-n.alreadyRead,this.stream.logBufferLevel(),r.alreadyRead+=n.length-n.alreadyRead,n.alreadyRead=n.length}}return r.alreadyRead===r.size?r:null},g.prototype.releaseItem=function(e){var t=this.items[e];if(t.data){this.itemsDataSize-=t.size,t.data=null,t.alreadyRead=0;for(var r=0;r<t.extents.length;r++){t.extents[r].alreadyRead=0}return t.size}return 0},g.prototype.processItems=function(e){for(var t in this.items){var r=this.items[t];this.getItem(r.id),e&&!r.sent&&(e(r),r.sent=!0,r.data=null)}},g.prototype.hasItem=function(e){for(var t in this.items){var r=this.items[t];if(r.name===e)return r.id}return-1},g.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},g.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},g.prototype.itemToFragmentedTrackFile=function(e){var t=e||{},r=null;if(null==(r=t.itemId?this.getItem(t.itemId):this.getPrimaryItem()))return null;var i=new g;i.discardMdatData=!1;var n={type:r.type,description_boxes:r.properties.boxes};r.properties.ispe&&(n.width=r.properties.ispe.image_width,n.height=r.properties.ispe.image_height);var o=i.addTrack(n);return o?(i.addSample(o,r.data),i):null},g.prototype.write=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e)},g.prototype.createFragment=function(e,t,r){var i=this.getTrackById(e),n=this.getSample(i,t);if(null==n)return this.setNextSeekPositionFromSample(i.samples[t]),null;var o=r||new d;o.endianness=d.BIG_ENDIAN;var a=this.createSingleSampleMoof(n);a.write(o),a.trafs[0].truns[0].data_offset=a.size+8,s.debug("MP4Box","Adjusting data_offset with new value "+a.trafs[0].truns[0].data_offset),o.adjustUint32(a.trafs[0].truns[0].data_offset_position,a.trafs[0].truns[0].data_offset);var l=new f.mdatBox;return l.data=n.data,l.write(o),o},g.writeInitializationSegment=function(e,t,r,i){var n;s.debug("ISOFile","Generating initialization segment");var o=new d;o.endianness=d.BIG_ENDIAN,e.write(o);var a=t.add("mvex");for(r&&a.add("mehd").set("fragment_duration",r),n=0;n<t.traks.length;n++)a.add("trex").set("track_id",t.traks[n].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",i).set("default_sample_size",0).set("default_sample_flags",65536);return t.write(o),o.buffer},g.prototype.save=function(e){var t=new d;t.endianness=d.BIG_ENDIAN,this.write(t),t.save(e)},g.prototype.getBuffer=function(){var e=new d;return e.endianness=d.BIG_ENDIAN,this.write(e),e.buffer},g.prototype.initializeSegmentation=function(){var e,t,r,i;for(null===this.onSegment&&s.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.resetTables()),t=[],e=0;e<this.fragmentedTracks.length;e++){var n=new f.moovBox;n.mvhd=this.moov.mvhd,n.boxes.push(n.mvhd),r=this.getTrackById(this.fragmentedTracks[e].id),n.boxes.push(r),n.traks.push(r),(i={}).id=r.tkhd.track_id,i.user=this.fragmentedTracks[e].user,i.buffer=g.writeInitializationSegment(this.ftyp,n,this.moov.mvex&&this.moov.mvex.mehd?this.moov.mvex.mehd.fragment_duration:void 0,this.moov.traks[e].samples.length>0?this.moov.traks[e].samples[0].duration:0),t.push(i)}return t},f.Box.prototype.printHeader=function(e){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),e.log(e.indent+"size:"+this.size),e.log(e.indent+"type:"+this.type)},f.FullBox.prototype.printHeader=function(e){this.size+=4,f.Box.prototype.printHeader.call(this,e),e.log(e.indent+"version:"+this.version),e.log(e.indent+"flags:"+this.flags)},f.Box.prototype.print=function(e){this.printHeader(e)},f.ContainerBox.prototype.print=function(e){this.printHeader(e);for(var t=0;t<this.boxes.length;t++)if(this.boxes[t]){var r=e.indent;e.indent+=" ",this.boxes[t].print(e),e.indent=r}},g.prototype.print=function(e){e.indent="";for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&this.boxes[t].print(e)},f.mvhdBox.prototype.print=function(e){f.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"timescale: "+this.timescale),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"rate: "+this.rate),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"next_track_id: "+this.next_track_id)},f.tkhdBox.prototype.print=function(e){f.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"track_id: "+this.track_id),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"layer: "+this.layer),e.log(e.indent+"alternate_group: "+this.alternate_group),e.log(e.indent+"width: "+this.width),e.log(e.indent+"height: "+this.height)};var y={createFile:function(e,t){var r=void 0===e||e,i=new g(t);return i.discardMdatData=!r,i}};t.createFile=y.createFile}));function Or(e){return e.reduce(((e,t)=>256*e+t))}function Gr(e){const t=[101,103,119,99],r=e.length-28,i=e.slice(r,r+t.length);return t.every(((e,t)=>e===i[t]))}zr.Log,zr.MP4BoxStream,zr.DataStream,zr.MultiBufferStream,zr.MPEG4DescriptorParser,zr.BoxParser,zr.XMLSubtitlein4Parser,zr.Textin4Parser,zr.ISOFile,zr.createFile;class $r{constructor(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=new Uint8Array([30,158,90,33,244,57,83,165,2,70,35,87,215,231,226,108]),this.t=this.n.slice().reverse()}destroy(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=null,this.t=null}transport(e){if(!this.s&&this.l>50)return e;if(this.l++,this.d)return e;const t=new Uint8Array(e);if(this.A){if(!(this.c<this.u))return this.a&&this.s?(this.a.set(t,this.r),this.s.parse(null,this.r,t.byteLength),this.a.slice(this.r,this.r+t.byteLength)):(console.error("video_error_2"),this.d=!0,e);Gr(t)&&this.c++}else{const r=function(e,t){const r=function(e,t){for(let r=0;r<e.byteLength-t.length;r++)for(let i=0;i<t.length&&e[r+i]===t[i];i++)if(i===t.length-1)return r;return null}(e,t);if(r){const t=Or(e.slice(r+16,r+16+8));return[t,Or(e.slice(r+24,r+24+8)),function(e){return e.map((e=>~e))}(e.slice(r+32,r+32+t))]}return null}(t,this.t);if(!r)return e;const i=function(e){try{if("object"!=typeof WebAssembly||"function"!=typeof WebAssembly.instantiate)throw null;{const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(!(e instanceof WebAssembly.Module&&new WebAssembly.Instance(e)instanceof WebAssembly.Instance))throw null}}catch(e){return new Error("video_error_4")}let t;try{t={env:{__handle_stack_overflow:()=>e(new Error("video_error_1")),memory:new WebAssembly.Memory({initial:256,maximum:256})}}}catch(e){return new Error("video_error_5")}return t}(e);if(i instanceof Error)return console.error(i.message),this.d=!0,e;this.A=!0,this.u=r[1],Gr(t)&&this.c++,WebAssembly.instantiate(r[2],i).then((e=>{if("function"!=typeof(t=e.instance.exports).parse||"object"!=typeof t.memory)return this.d=!0,void console.error("video_error_3");var t;this.s=e.instance.exports,this.a=new Uint8Array(this.s.memory.buffer)})).catch((e=>{this.d=!0,console.error("video_error_6")}))}return e}}const Hr=0,Vr=32,Wr=16,jr=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],qr=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function Yr(e){const t=[];for(let r=0,i=e.length;r<i;r+=2)t.push(parseInt(e.substr(r,2),16));return t}function Kr(e,t){const r=31&t;return e<<r|e>>>32-r}function Xr(e){return(255&jr[e>>>24&255])<<24|(255&jr[e>>>16&255])<<16|(255&jr[e>>>8&255])<<8|255&jr[255&e]}function Zr(e){return e^Kr(e,2)^Kr(e,10)^Kr(e,18)^Kr(e,24)}function Jr(e){return e^Kr(e,13)^Kr(e,23)}function Qr(e,t,r){const i=new Array(4),n=new Array(4);for(let t=0;t<4;t++)n[0]=255&e[4*t],n[1]=255&e[4*t+1],n[2]=255&e[4*t+2],n[3]=255&e[4*t+3],i[t]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];for(let e,t=0;t<32;t+=4)e=i[1]^i[2]^i[3]^r[t+0],i[0]^=Zr(Xr(e)),e=i[2]^i[3]^i[0]^r[t+1],i[1]^=Zr(Xr(e)),e=i[3]^i[0]^i[1]^r[t+2],i[2]^=Zr(Xr(e)),e=i[0]^i[1]^i[2]^r[t+3],i[3]^=Zr(Xr(e));for(let e=0;e<16;e+=4)t[e]=i[3-e/4]>>>24&255,t[e+1]=i[3-e/4]>>>16&255,t[e+2]=i[3-e/4]>>>8&255,t[e+3]=255&i[3-e/4]}function ei(e,t,r){let{padding:i="pkcs#7",mode:n,iv:o=[],output:s="string"}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("cbc"===n&&("string"==typeof o&&(o=Yr(o)),16!==o.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=Yr(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?r!==Hr?function(e){const t=[];for(let r=0,i=e.length;r<i;r++){const i=e.codePointAt(r);if(i<=127)t.push(i);else if(i<=2047)t.push(192|i>>>6),t.push(128|63&i);else if(i<=55295||i>=57344&&i<=65535)t.push(224|i>>>12),t.push(128|i>>>6&63),t.push(128|63&i);else{if(!(i>=65536&&i<=1114111))throw t.push(i),new Error("input is not supported");r++,t.push(240|i>>>18&28),t.push(128|i>>>12&63),t.push(128|i>>>6&63),t.push(128|63&i)}}return t}(e):Yr(e):[...e],("pkcs#5"===i||"pkcs#7"===i)&&r!==Hr){const t=Wr-e.length%Wr;for(let r=0;r<t;r++)e.push(t)}const a=new Array(Vr);!function(e,t,r){const i=new Array(4),n=new Array(4);for(let t=0;t<4;t++)n[0]=255&e[0+4*t],n[1]=255&e[1+4*t],n[2]=255&e[2+4*t],n[3]=255&e[3+4*t],i[t]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];i[0]^=2746333894,i[1]^=1453994832,i[2]^=1736282519,i[3]^=2993693404;for(let e,r=0;r<32;r+=4)e=i[1]^i[2]^i[3]^qr[r+0],t[r+0]=i[0]^=Jr(Xr(e)),e=i[2]^i[3]^i[0]^qr[r+1],t[r+1]=i[1]^=Jr(Xr(e)),e=i[3]^i[0]^i[1]^qr[r+2],t[r+2]=i[2]^=Jr(Xr(e)),e=i[0]^i[1]^i[2]^qr[r+3],t[r+3]=i[3]^=Jr(Xr(e));if(r===Hr)for(let e,r=0;r<16;r++)e=t[r],t[r]=t[31-r],t[31-r]=e}(t,a,r);const d=[];let l=o,u=e.length,c=0;for(;u>=Wr;){const t=e.slice(c,c+16),i=new Array(16);if("cbc"===n)for(let e=0;e<Wr;e++)r!==Hr&&(t[e]^=l[e]);Qr(t,i,a);for(let e=0;e<Wr;e++)"cbc"===n&&r===Hr&&(i[e]^=l[e]),d[c+e]=i[e];"cbc"===n&&(l=r!==Hr?i:t),u-=Wr,c+=Wr}if(("pkcs#5"===i||"pkcs#7"===i)&&r===Hr){const e=d.length,t=d[e-1];for(let r=1;r<=t;r++)if(d[e-r]!==t)throw new Error("padding is invalid");d.splice(e-t,t)}return"array"!==s?r!==Hr?d.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join(""):function(e){const t=[];for(let r=0,i=e.length;r<i;r++)e[r]>=240&&e[r]<=247?(t.push(String.fromCodePoint(((7&e[r])<<18)+((63&e[r+1])<<12)+((63&e[r+2])<<6)+(63&e[r+3]))),r+=3):e[r]>=224&&e[r]<=239?(t.push(String.fromCodePoint(((15&e[r])<<12)+((63&e[r+1])<<6)+(63&e[r+2]))),r+=2):e[r]>=192&&e[r]<=223?(t.push(String.fromCodePoint(((31&e[r])<<6)+(63&e[r+1]))),r++):t.push(String.fromCodePoint(e[r]));return t.join("")}(d):d}class ti{on(e,t,r){const i=this.e||(this.e={});return(i[e]||(i[e]=[])).push({fn:t,ctx:r}),this}once(e,t,r){const i=this;function n(){i.off(e,n);for(var o=arguments.length,s=new Array(o),a=0;a<o;a++)s[a]=arguments[a];t.apply(r,s)}return n._=t,this.on(e,n,r)}emit(e){const t=((this.e||(this.e={}))[e]||[]).slice();for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];for(let e=0;e<t.length;e+=1)t[e].fn.apply(t[e].ctx,i);return this}off(e,t){const r=this.e||(this.e={});if(!e)return Object.keys(r).forEach((e=>{delete r[e]})),void delete this.e;const i=r[e],n=[];if(i&&t)for(let e=0,r=i.length;e<r;e+=1)i[e].fn!==t&&i[e].fn._!==t&&n.push(i[e]);return n.length?r[e]=n:delete r[e],this}}const ri={init:0,findFirstStartCode:1,findSecondStartCode:2};class ii extends ti{constructor(e){super(),this.player=e,this.isDestroyed=!1,this.reset()}destroy(){this.isDestroyed=!1,this.off(),this.reset()}reset(){this.stats=ri.init,this.tempBuffer=new Uint8Array(0),this.parsedOffset=0,this.versionLayer=0}dispatch(e,t){let r=new Uint8Array(this.tempBuffer.length+e.length);for(r.set(this.tempBuffer,0),r.set(e,this.tempBuffer.length),this.tempBuffer=r;!this.isDestroyed;){if(this.state==ri.Init){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(!(!1&this.tempBuffer[this.parsedOffset+1])){this.versionLayer=this.tempBuffer[this.parsedOffset+1],this.state=ri.findFirstStartCode,this.fisrtStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==ri.findFirstStartCode){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(this.tempBuffer[this.parsedOffset+1]==this.versionLayer){this.state=ri.findSecondStartCode,this.secondStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==ri.findSecondStartCode){let e=this.tempBuffer.slice(this.fisrtStartCodeOffset,this.secondStartCodeOffset);this.emit("data",e,t),this.tempBuffer=this.tempBuffer.slice(this.secondStartCodeOffset),this.fisrtStartCodeOffset=0,this.parsedOffset=2,this.state=ri.findFirstStartCode}}}}function ni(e,t,r){for(let i=2;i<e.length;++i){const n=i-2,o=t[n%t.length],s=r[n%r.length];e[i]=e[i]^o^s}return e}class oi{constructor(e){this.destroys=[],this.proxy=this.proxy.bind(this),this.master=e}proxy(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!e)return;if(Array.isArray(t))return t.map((t=>this.proxy(e,t,r,i)));e.addEventListener(t,r,i);const n=()=>{bt(e.removeEventListener)&&e.removeEventListener(t,r,i)};return this.destroys.push(n),n}destroy(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach((e=>e())),this.destroys=[]}}class si{static init(){si.types={avc1:[],avcC:[],hvc1:[],hvcC:[],av01:[],av1C:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[],"ec-3":[],dec3:[]};for(let e in si.types)si.types.hasOwnProperty(e)&&(si.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let e=si.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,r=null,i=Array.prototype.slice.call(arguments,1),n=i.length;for(let e=0;e<n;e++)t+=i[e].byteLength;r=new Uint8Array(t),r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r.set(e,4);let o=8;for(let e=0;e<n;e++)r.set(i[e],o),o+=i[e].byteLength;return r}static generateInitSegment(e){let t=si.box(si.types.ftyp,si.constants.FTYP),r=si.moov(e),i=new Uint8Array(t.byteLength+r.byteLength);return i.set(t,0),i.set(r,t.byteLength),i}static moov(e){let t=si.mvhd(e.timescale,e.duration),r=si.trak(e),i=si.mvex(e);return si.box(si.types.moov,t,r,i)}static mvhd(e,t){return si.box(si.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return si.box(si.types.trak,si.tkhd(e),si.mdia(e))}static tkhd(e){let t=e.id,r=e.duration,i=e.presentWidth,n=e.presentHeight;return si.box(si.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>>8&255,255&i,0,0,n>>>8&255,255&n,0,0]))}static mdia(e){return si.box(si.types.mdia,si.mdhd(e),si.hdlr(e),si.minf(e))}static mdhd(e){let t=e.timescale,r=e.duration;return si.box(si.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,r>>>24&255,r>>>16&255,r>>>8&255,255&r,85,196,0,0]))}static hdlr(e){let t=null;return t="audio"===e.type?si.constants.HDLR_AUDIO:si.constants.HDLR_VIDEO,si.box(si.types.hdlr,t)}static minf(e){let t=null;return t="audio"===e.type?si.box(si.types.smhd,si.constants.SMHD):si.box(si.types.vmhd,si.constants.VMHD),si.box(si.types.minf,t,si.dinf(),si.stbl(e))}static dinf(){return si.box(si.types.dinf,si.box(si.types.dref,si.constants.DREF))}static stbl(e){return si.box(si.types.stbl,si.stsd(e),si.box(si.types.stts,si.constants.STTS),si.box(si.types.stsc,si.constants.STSC),si.box(si.types.stsz,si.constants.STSZ),si.box(si.types.stco,si.constants.STCO))}static stsd(e){return"audio"===e.type?"mp3"===e.audioType?si.box(si.types.stsd,si.constants.STSD_PREFIX,si.mp3(e)):si.box(si.types.stsd,si.constants.STSD_PREFIX,si.mp4a(e)):"avc"===e.videoType?si.box(si.types.stsd,si.constants.STSD_PREFIX,si.avc1(e)):si.box(si.types.stsd,si.constants.STSD_PREFIX,si.hvc1(e))}static mp3(e){let t=e.channelCount,r=e.audioSampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,r>>>8&255,255&r,0,0]);return si.box(si.types[".mp3"],i)}static mp4a(e){let t=e.channelCount,r=e.audioSampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,r>>>8&255,255&r,0,0]);return si.box(si.types.mp4a,i,si.esds(e))}static esds(e){let t=e.config||[],r=t.length,i=new Uint8Array([0,0,0,0,3,23+r,0,1,0,4,15+r,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([r]).concat(t).concat([6,1,2]));return si.box(si.types.esds,i)}static avc1(e){let t=e.avcc;const r=e.codecWidth,i=e.codecHeight;let n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>>8&255,255&r,i>>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return si.box(si.types.avc1,n,si.box(si.types.avcC,t))}static hvc1(e){let t=e.avcc;const r=e.codecWidth,i=e.codecHeight;let n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>>8&255,255&r,i>>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return si.box(si.types.hvc1,n,si.box(si.types.hvcC,t))}static mvex(e){return si.box(si.types.mvex,si.trex(e))}static trex(e){let t=e.id,r=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return si.box(si.types.trex,r)}static moof(e,t){return si.box(si.types.moof,si.mfhd(e.sequenceNumber),si.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return si.box(si.types.mfhd,t)}static traf(e,t){let r=e.id,i=si.box(si.types.tfhd,new Uint8Array([0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r])),n=si.box(si.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t])),o=si.sdtp(e),s=si.trun(e,o.byteLength+16+16+8+16+8+8);return si.box(si.types.traf,i,n,s,o)}static sdtp(e){let t=new Uint8Array(5),r=e.flags;return t[4]=r.isLeading<<6|r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy,si.box(si.types.sdtp,t)}static trun(e,t){let r=new Uint8Array(28);t+=36,r.set([0,0,15,1,0,0,0,1,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0);let i=e.duration,n=e.size,o=e.flags,s=e.cts;return r.set([i>>>24&255,i>>>16&255,i>>>8&255,255&i,n>>>24&255,n>>>16&255,n>>>8&255,255&n,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.isNonSync,0,0,s>>>24&255,s>>>16&255,s>>>8&255,255&s],12),si.box(si.types.trun,r)}static mdat(e){return si.box(si.types.mdat,e)}}si.init();const ai=[44100,48e3,32e3,0],di=[22050,24e3,16e3,0],li=[11025,12e3,8e3,0],ui=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],ci=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],fi=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1];const hi=3,pi=4,mi=6,_i=15,gi=17,yi=129,vi=135,bi=21,wi=134,Si=27,Ei=36;class Ai{constructor(){this.slices=[],this.total_length=0,this.expected_length=0,this.random_access_indicator=0}}class Ui{constructor(){this.pid=null,this.data=null,this.stream_type=null,this.random_access_indicator=null}}class Ti{constructor(){this.pid=null,this.stream_id=null,this.len=null,this.data=null,this.pts=null,this.nearest_pts=null,this.dts=null}}const xi=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];class Bi{constructor(){this.mimeType=null,this.duration=null,this.hasAudio=null,this.hasVideo=null,this.audioCodec=null,this.videoCodec=null,this.audioDataRate=null,this.videoDataRate=null,this.audioSampleRate=null,this.audioChannelCount=null,this.width=null,this.height=null,this.fps=null,this.profile=null,this.level=null,this.refFrames=null,this.chromaFormat=null,this.sarNum=null,this.sarDen=null,this.metadata=null,this.segments=null,this.segmentCount=null,this.hasKeyframesIndex=null,this.keyframesIndex=null}isComplete(){let e=!1===this.hasAudio||!0===this.hasAudio&&null!=this.audioCodec&&null!=this.audioSampleRate&&null!=this.audioChannelCount,t=!1===this.hasVideo||!0===this.hasVideo&&null!=this.videoCodec&&null!=this.width&&null!=this.height&&null!=this.fps&&null!=this.profile&&null!=this.level&&null!=this.refFrames&&null!=this.chromaFormat&&null!=this.sarNum&&null!=this.sarDen;return null!=this.mimeType&&e&&t}isSeekable(){return!0===this.hasKeyframesIndex}getNearestKeyframe(e){if(null==this.keyframesIndex)return null;let t=this.keyframesIndex,r=this._search(t.times,e);return{index:r,milliseconds:t.times[r],fileposition:t.filepositions[r]}}_search(e,t){let r=0,i=e.length-1,n=0,o=0,s=i;for(t<e[0]&&(r=0,o=s+1);o<=s;){if(n=o+Math.floor((s-o)/2),n===i||t>=e[n]&&t<e[n+1]){r=n;break}e[n]<t?o=n+1:s=n-1}return r}}class ki{constructor(e){let t=null,r=e.audio_object_type,i=e.audio_object_type,n=e.sampling_freq_index,o=e.channel_config,s=0,a=navigator.userAgent.toLowerCase();-1!==a.indexOf("firefox")?n>=6?(i=5,t=new Array(4),s=n-3):(i=2,t=new Array(2),s=n):-1!==a.indexOf("android")?(i=2,t=new Array(2),s=n):(i=5,s=n,t=new Array(4),n>=6?s=n-3:1===o&&(i=2,t=new Array(2),s=n)),t[0]=i<<3,t[0]|=(15&n)>>>1,t[1]=(15&n)<<7,t[1]|=(15&o)<<3,5===i&&(t[1]|=(15&s)>>>1,t[2]=(1&s)<<7,t[2]|=8,t[3]=0),this.config=t,this.sampling_rate=xi[n],this.sampling_index=n,this.channel_count=o,this.object_type=i,this.original_object_type=r,this.codec_mimetype="mp4a.40."+i,this.original_codec_mimetype="mp4a.40."+r}}function Ci(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=[],n=[],o={},s=new AbortController,a=null,d=null,l=null,u=null,v=null,b=null,S=!1,$e=!1,We=!!Ut(r),rt=!1,it=null,at=null,dt=null,gt=[],xt=null,Bt=null,kt=0,Ct=0,It=null,Vt=null,Wt=0,jt=0,qt=!1,Yt=!1,Jt=!1,sr=null,ar=null,dr=null,lr=!1,ur=()=>{const e=Et();return{debug:e.debug,debugLevel:e.debugLevel,debugUuid:e.debugUuid,useOffscreen:e.useOffscreen,useWCS:e.useWCS,useMSE:e.useMSE,videoBuffer:e.videoBuffer,videoBufferDelay:e.videoBufferDelay,openWebglAlignment:e.openWebglAlignment,playType:e.playType,hasAudio:e.hasAudio,hasVideo:e.hasVideo,playbackRate:1,playbackForwardMaxRateDecodeIFrame:e.playbackForwardMaxRateDecodeIFrame,playbackIsCacheBeforeDecodeForFpsRender:e.playbackConfig.isCacheBeforeDecodeForFpsRender,sampleRate:0,networkDelay:e.networkDelay,visibility:!0,useSIMD:e.useSIMD,isRecording:!1,recordType:e.recordType,isNakedFlow:e.isNakedFlow,checkFirstIFrame:e.checkFirstIFrame,audioBufferSize:1024,isM7sCrypto:e.isM7sCrypto,m7sCryptoAudio:e.m7sCryptoAudio,cryptoKey:e.cryptoKey,cryptoIV:e.cryptoIV,isSm4Crypto:e.isSm4Crypto,sm4CryptoKey:e.sm4CryptoKey,isXorCrypto:e.isXorCrypto,isHls265:!1,isFlv:e.isFlv,isFmp4:e.isFmp4,isMpeg4:e.isMpeg4,isTs:e.isTs,isFmp4Private:e.isFmp4Private,isEmitSEI:e.isEmitSEI,isRecordTypeFlv:!1,isWasmMp4:!1,isChrome:!1,isDropSameTimestampGop:e.isDropSameTimestampGop,mseDecodeAudio:e.mseDecodeAudio,nakedFlowH265DemuxUseNew:e.nakedFlowH265DemuxUseNew,mseDecoderUseWorker:e.mseDecoderUseWorker,mseAutoCleanupSourceBuffer:e.mseAutoCleanupSourceBuffer,mseAutoCleanupMaxBackwardDuration:e.mseAutoCleanupMaxBackwardDuration,mseAutoCleanupMinBackwardDuration:e.mseAutoCleanupMinBackwardDuration,mseCorrectTimeDuration:e.mseCorrectTimeDuration,mseCorrectAudioTimeDuration:e.mseCorrectAudioTimeDuration}};"VideoEncoder"in self&&(o={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){if(o.isEmitInfo||(gr.debug.log("worker","Webcodecs Video Decoder initSize"),postMessage({cmd:A,w:e.codedWidth,h:e.codedHeight}),o.isEmitInfo=!0,o.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),o.offscreenCanvasCtx=o.offscreenCanvas.getContext("2d")),bt(e.createImageBitmap))e.createImageBitmap().then((t=>{o.offscreenCanvasCtx.drawImage(t,0,0,e.codedWidth,e.codedHeight);let r=o.offscreenCanvas.transferToImageBitmap();postMessage({cmd:U,buffer:r,delay:gr.delay,ts:0},[r]),wt(e)}));else{o.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let t=o.offscreenCanvas.transferToImageBitmap();postMessage({cmd:U,buffer:t,delay:gr.delay,ts:0},[t]),wt(e)}},error:function(e){gr.debug.error("worker","VideoDecoder error",e)}}),decode:function(e,t,r){const i=e[0]>>4==1;if(o.hasInit){const r=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:i?Fe:Ie});o.decoder.decode(r)}else if(i&&0===e[1]){const t=15&e[0];postMessage({cmd:D,code:t});const r=new Uint8Array(e);postMessage({cmd:P,buffer:r,codecId:t},[r.buffer]);let i=null,n=null;const s=e.slice(5);t===Te?(n=Lt(s),i={codec:n.codec,description:s}):t===xe&&(n=Zt(s),i={codec:n.codec,description:s}),n&&n.codecWidth&&n.codecHeight&&(i.codedHeight=n.codecHeight,i.codedWidth=n.codecWidth);try{o.decoder.configure(i),o.hasInit=!0}catch(e){gr.debug.log("worker","VideoDecoder configure error",e.code,e)}}},reset(){o.hasInit=!1,o.isEmitInfo=!1,o.offscreenCanvas=null,o.offscreenCanvasCtx=null}});let cr=function(){if(lr=!0,gr.fetchStatus!==Ye||Tt(gr._opt.isChrome)){if(s)try{s.abort(),s=null}catch(e){gr.debug.log("worker","abort catch",e)}}else s=null,gr.debug.log("worker",`abort() and not abortController.abort() _status is ${gr.fetchStatus} and _isChrome is ${gr._opt.isChrome}`)},fr={init(){fr.lastBuf=null,fr.vps=null,fr.sps=null,fr.pps=null,fr.streamType=null,fr.localDts=0,fr.isSendSeqHeader=!1},destroy(){fr.lastBuf=null,fr.vps=null,fr.sps=null,fr.pps=null,fr.streamType=null,fr.localDts=0,fr.isSendSeqHeader=!1},dispatch(e){const t=new Uint8Array(e);fr.extractNALu$2(t)},getNaluDts(){let e=fr.localDts;return fr.localDts=fr.localDts+40,e},getNaluAudioDts(){const e=gr._opt.sampleRate,t=gr._opt.audioBufferSize;return fr.localDts+parseInt(t/e*1e3)},extractNALu(e){let t,r,i=0,n=e.byteLength,o=0,s=[];for(;i<n;)switch(t=e[i++],o){case 0:0===t&&(o=1);break;case 1:o=0===t?2:0;break;case 2:case 3:0===t?o=3:1===t&&i<n?(r&&s.push(e.subarray(r,i-o-1)),r=i,o=0):o=0}return r&&s.push(e.subarray(r,n)),s},extractNALu$2(e){let t=null;if(!e||e.byteLength<1)return;fr.lastBuf?(t=new Uint8Array(e.byteLength+fr.lastBuf.length),t.set(fr.lastBuf),t.set(new Uint8Array(e),fr.lastBuf.length)):t=new Uint8Array(e);let r=0,i=-1,n=-2;const o=new Array;for(let e=0;e<t.length;e+=2){const r=t[e],s=t[e+1];0==i&&0==r&&0==s?o.push(e-1):1==s&&0==r&&0==i&&0==n&&o.push(e-2),n=r,i=s}if(o.length>1)for(let e=0;e<o.length-1;++e){const i=t.subarray(o[e],o[e+1]+1);fr.handleNALu(i),r=o[e+1]}else r=o[0];if(0!=r&&r<t.length)fr.lastBuf=t.subarray(r);else{fr.lastBuf||(fr.lastBuf=t);const r=new Uint8Array(fr.lastBuf.length+e.byteLength);r.set(fr.lastBuf),r.set(new Uint8Array(e),fr.lastBuf.length),fr.lastBuf=r}},handleNALu(e){e.byteLength<=4?gr.debug.warn("worker",`handleNALu nalu byteLength is ${e.byteLength} <= 4`):(e=e.slice(4),fr.handleVideoNalu(e))},handleVideoNalu(e){if(fr.streamType||(fr.streamType=function(e){let t=null,r=31&e[0];return r!==De.sps&&r!==De.pps||(t=ke.h264),t||(r=(126&e[0])>>1,r!==Pe.vps&&r!==Pe.sps&&r!==Pe.pps||(t=ke.h265)),t}(e),sr=fr.streamType===ke.h265),fr.streamType===ke.h264){const t=fr.handleAddNaluStartCode(e),r=fr.extractNALu(t);if(0===r.length)return void gr.debug.warn("worker","handleVideoNalu","h264 naluList.length === 0");const i=[];if(r.forEach((e=>{const t=Nt(e);t===De.pps||t===De.sps?fr.handleVideoH264Nalu(e):Ot(t)&&i.push(e)})),1===i.length)fr.handleVideoH264Nalu(i[0]);else{const e=function(e){if(0===e.length)return!1;const t=Nt(e[0]);for(let r=1;r<e.length;r++)if(t!==Nt(e[r]))return!1;return!0}(i);if(e){const e=Nt(i[0]),t=Gt(e);fr.handleVideoH264NaluList(i,t,e)}else i.forEach((e=>{fr.handleVideoH264Nalu(e)}))}}else if(fr.streamType===ke.h265)if(gr._opt.nakedFlowH265DemuxUseNew){const t=fr.handleAddNaluStartCode(e),r=fr.extractNALu(t);if(0===r.length)return void gr.debug.warn("worker","handleVideoNalu","h265 naluList.length === 0");const i=[];if(r.forEach((e=>{const t=tr(e);t===Pe.pps||t===Pe.sps||t===Pe.vps?fr.handleVideoH265Nalu(e):rr(t)&&i.push(e)})),1===i.length)fr.handleVideoH265Nalu(i[0]);else{const e=function(e){if(0===e.length)return!1;const t=tr(e[0]);for(let r=1;r<e.length;r++)if(t!==tr(e[r]))return!1;return!0}(i);if(e){const e=tr(i[0]),t=ir(e);fr.handleVideoH265NaluList(i,t,e)}else i.forEach((e=>{fr.handleVideoH265Nalu(e)}))}}else{tr(e)===Pe.pps?fr.extractH265PPS(e):fr.handleVideoH265Nalu(e)}},extractH264PPS(e){const t=fr.handleAddNaluStartCode(e);fr.extractNALu(t).forEach((e=>{zt(Nt(e))?fr.extractH264SEI(e):fr.handleVideoH264Nalu(e)}))},extractH265PPS(e){const t=fr.handleAddNaluStartCode(e);fr.extractNALu(t).forEach((e=>{const t=tr(e);t===Pe.sei?fr.extractH265SEI(e):fr.handleVideoH265Nalu(e)}))},extractH264SEI(e){const t=fr.handleAddNaluStartCode(e);fr.extractNALu(t).forEach((e=>{fr.handleVideoH264Nalu(e)}))},extractH265SEI(e){const t=fr.handleAddNaluStartCode(e);fr.extractNALu(t).forEach((e=>{fr.handleVideoH265Nalu(e)}))},handleAddNaluStartCode(e){const t=[0,0,0,1],r=new Uint8Array(e.length+t.length);return r.set(t),r.set(e,t.length),r},handleVideoH264Nalu(e){const t=Nt(e);switch(t){case De.sps:fr.sps=e;break;case De.pps:fr.pps=e}if(fr.isSendSeqHeader){if(fr.sps&&fr.pps){const e=Mt({sps:fr.sps,pps:fr.pps}),t=fr.getNaluDts();gr.decode(e,{type:te,ts:t,isIFrame:!0,cts:0}),fr.sps=null,fr.pps=null}if(Ot(t)){const r=Gt(t),i=fr.getNaluDts(),n=function(e,t){let r=[];r[0]=t?23:39,r[1]=1,r[2]=0,r[3]=0,r[4]=0,r[5]=e.byteLength>>24&255,r[6]=e.byteLength>>16&255,r[7]=e.byteLength>>8&255,r[8]=255&e.byteLength;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}(e,r);fr.doDecode(n,{type:te,ts:i,isIFrame:r,cts:0})}else gr.debug.warn("work",`handleVideoH264Nalu Avc Seq Head is ${t}`)}else if(fr.sps&&fr.pps){fr.isSendSeqHeader=!0;const e=Mt({sps:fr.sps,pps:fr.pps});gr.decode(e,{type:te,ts:0,isIFrame:!0,cts:0}),fr.sps=null,fr.pps=null}},handleVideoH264NaluList(e,t,r){if(fr.isSendSeqHeader){const i=fr.getNaluDts(),n=Rt(e.reduce(((e,t)=>{const r=pt(e),i=pt(t),n=new Uint8Array(r.byteLength+i.byteLength);return n.set(r,0),n.set(i,r.byteLength),n})),t);fr.doDecode(n,{type:te,ts:i,isIFrame:t,cts:0}),gr.debug.log("worker",`handleVideoH264NaluList list size is ${e.length} package length is ${n.byteLength} isIFrame is ${t},nalu type is ${r}, dts is ${i}`)}else gr.debug.warn("worker","handleVideoH264NaluList isSendSeqHeader is false")},handleVideoH265Nalu(e){const t=tr(e);switch(t){case Pe.vps:fr.vps=e;break;case Pe.sps:fr.sps=e;break;case Pe.pps:fr.pps=e}if(fr.isSendSeqHeader){if(fr.vps&&fr.sps&&fr.pps){const e=Qt({vps:fr.vps,sps:fr.sps,pps:fr.pps}),t=fr.getNaluDts();gr.decode(e,{type:te,ts:t,isIFrame:!0,cts:0}),fr.vps=null,fr.sps=null,fr.pps=null}if(rr(t)){const r=ir(t),i=fr.getNaluDts(),n=function(e,t){let r=[];r[0]=t?28:44,r[1]=1,r[2]=0,r[3]=0,r[4]=0,r[5]=e.byteLength>>24&255,r[6]=e.byteLength>>16&255,r[7]=e.byteLength>>8&255,r[8]=255&e.byteLength;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}(e,r);fr.doDecode(n,{type:te,ts:i,isIFrame:r,cts:0})}else gr.debug.warn("work",`handleVideoH265Nalu HevcSeqHead is ${t}`)}else if(fr.vps&&fr.sps&&fr.pps){fr.isSendSeqHeader=!0;const e=Qt({vps:fr.vps,sps:fr.sps,pps:fr.pps});gr.decode(e,{type:te,ts:0,isIFrame:!0,cts:0}),fr.vps=null,fr.sps=null,fr.pps=null}},handleVideoH265NaluList(e,t,r){if(fr.isSendSeqHeader){const i=fr.getNaluDts(),n=er(e.reduce(((e,t)=>{const r=pt(e),i=pt(t),n=new Uint8Array(r.byteLength+i.byteLength);return n.set(r,0),n.set(i,r.byteLength),n})),t);fr.doDecode(n,{type:te,ts:i,isIFrame:t,cts:0}),gr.debug.log("worker",`handleVideoH265NaluList list size is ${e.length} package length is ${n.byteLength} isIFrame is ${t},nalu type is ${r}, dts is ${i}`)}else gr.debug.warn("worker","handleVideoH265NaluList isSendSeqHeader is false")},doDecode(e,t){gr.calcNetworkDelay(t.ts),t.isIFrame&&gr.calcIframeIntervalTimestamp(t.ts),gr.decode(e,t)}},hr={LOG_NAME:"worker fmp4Demuxer",mp4Box:zr.createFile(),offset:0,videoTrackId:null,audioTrackId:null,isHevc:!1,listenMp4Box(){hr.mp4Box.onReady=hr.onReady,hr.mp4Box.onError=hr.onError,hr.mp4Box.onSamples=hr.onSamples},initTransportDescarmber(){hr.transportDescarmber=new $r},_getSeqHeader(e){const t=hr.mp4Box.getTrackById(e.id);for(const e of t.mdia.minf.stbl.stsd.entries)if(e.avcC||e.hvcC){const t=new zr.DataStream(void 0,0,zr.DataStream.BIG_ENDIAN);let r=[];e.avcC?(e.avcC.write(t),r=[23,0,0,0,0]):(hr.isHevc=!0,sr=!0,e.hvcC.write(t),r=[28,0,0,0,0]);const i=new Uint8Array(t.buffer,8),n=new Uint8Array(r.length+i.length);return n.set(r,0),n.set(i,r.length),n}return null},onReady(e){gr.debug.log(hr.LOG_NAME,"onReady()");const t=e.videoTracks[0],r=e.audioTracks[0];if(t){hr.videoTrackId=t.id;const e=hr._getSeqHeader(t);e&&(gr.debug.log(hr.LOG_NAME,"seqHeader"),gr.decodeVideo(e,0,!0,0)),hr.mp4Box.setExtractionOptions(t.id)}if(r&&gr._opt.hasAudio){hr.audioTrackId=r.id;const e=r.audio||{},t=lt.indexOf(e.sample_rate),i=r.codec.replace("mp4a.40.","");hr.mp4Box.setExtractionOptions(r.id);const n=nt({profile:parseInt(i,10),sampleRate:t,channel:e.channel_count});gr.debug.log(hr.LOG_NAME,"aacADTSHeader"),gr.decodeAudio(n,0)}hr.mp4Box.start()},onError(e){gr.debug.error(hr.LOG_NAME,"mp4Box onError",e)},onSamples(e,t,r){if(e===hr.videoTrackId)for(const t of r){const r=t.data,i=t.is_sync,n=1e3*t.cts/t.timescale;t.duration,t.timescale,i&&gr.calcIframeIntervalTimestamp(n);let o=null;o=hr.isHevc?er(r,i):Rt(r,i),gr.decode(o,{type:te,ts:n,isIFrame:i,cts:0}),hr.mp4Box.releaseUsedSamples(e,t.number)}else if(e===hr.audioTrackId){if(gr._opt.hasAudio)for(const t of r){const r=t.data,i=1e3*t.cts/t.timescale;t.duration,t.timescale;const n=new Uint8Array(r.byteLength+2);n.set([175,1],0),n.set(r,2),gr.decode(n,{type:ee,ts:i,isIFrame:!1,cts:0}),hr.mp4Box.releaseUsedSamples(e,t.number)}}else gr.debug.warn(hr.LOG_NAME,"onSamples() trackId error",e)},dispatch(e){let t=e;"string"!=typeof e?"object"==typeof e?(hr.transportDescarmber&&(t=hr.transportDescarmber.transport(t)),t.buffer.fileStart=hr.offset,hr.offset+=t.byteLength,hr.mp4Box.appendBuffer(t.buffer)):gr.debug.warn(hr.LOG_NAME,"dispatch()","data is not object",e):gr.debug.warn(hr.LOG_NAME,"dispatch()","data is string",e)},destroy(){hr.mp4Box&&(hr.mp4Box.flush(),hr.mp4Box=null),hr.transportDescarmber&&(hr.transportDescarmber.destroy(),hr.transportDescarmber=null),hr.offset=0,hr.videoTrackId=null,hr.audioTrackId=null,hr.isHevc=!1}},pr={LOG_NAME:"worker mpeg4Demuxer",lastBuffer:new Uint8Array(0),parsedOffset:0,firstStartCodeOffset:0,secondStartCodeOffset:0,state:"init",hasInitVideoCodec:!1,localDts:0,dispatch(e){const t=new Uint8Array(e);pr.extractNALu(t)},destroy(){pr.lastBuffer=new Uint8Array(0),pr.parsedOffset=0,pr.firstStartCodeOffset=0,pr.secondStartCodeOffset=0,pr.state="init",pr.hasInitVideoCodec=!1,pr.localDts=0},extractNALu(e){if(!e||e.byteLength<1)return void gr.debug.warn(pr.LOG_NAME,"extractNALu() buffer error",e);const t=new Uint8Array(pr.lastBuffer.length+e.length);for(t.set(pr.lastBuffer,0),t.set(new Uint8Array(e),pr.lastBuffer.length),pr.lastBuffer=t;;){if("init"===pr.state){let e=!1;for(;pr.lastBuffer.length-pr.parsedOffset>=4;)if(0===pr.lastBuffer[pr.parsedOffset])if(0===pr.lastBuffer[pr.parsedOffset+1])if(1===pr.lastBuffer[pr.parsedOffset+2]){if(182===pr.lastBuffer[pr.parsedOffset+3]){pr.state="findFirstStartCode",pr.firstStartCodeOffset=pr.parsedOffset,pr.parsedOffset+=4,e=!0;break}pr.parsedOffset++}else pr.parsedOffset++;else pr.parsedOffset++;else pr.parsedOffset++;if(e)continue;break}if("findFirstStartCode"===pr.state){let e=!1;for(;pr.lastBuffer.length-pr.parsedOffset>=4;)if(0===pr.lastBuffer[pr.parsedOffset])if(0===pr.lastBuffer[pr.parsedOffset+1])if(1===pr.lastBuffer[pr.parsedOffset+2]){if(182===pr.lastBuffer[pr.parsedOffset+3]){pr.state="findSecondStartCode",pr.secondStartCodeOffset=pr.parsedOffset,pr.parsedOffset+=4,e=!0;break}pr.parsedOffset++}else pr.parsedOffset++;else pr.parsedOffset++;else pr.parsedOffset++;if(e)continue;break}if("findSecondStartCode"===pr.state){if(!(pr.lastBuffer.length-pr.parsedOffset>0))break;{let e,t,r=192&pr.lastBuffer[pr.parsedOffset];e=0==r?pr.secondStartCodeOffset-14:pr.secondStartCodeOffset;let i=0==(192&pr.lastBuffer[pr.firstStartCodeOffset+4]);if(i){if(pr.firstStartCodeOffset-14<0)return void gr.debug.warn(pr.LOG_NAME,"firstStartCodeOffset -14 is",pr.firstStartCodeOffset-14);pr.hasInitVideoCodec||(pr.hasInitVideoCodec=!0,gr.debug.log(pr.LOG_NAME,"setCodec"),vr.setCodec(Be,"")),t=pr.lastBuffer.subarray(pr.firstStartCodeOffset-14,e)}else t=pr.lastBuffer.subarray(pr.firstStartCodeOffset,e);let n=pr.getNaluDts();pr.hasInitVideoCodec?(postMessage({cmd:M,type:ye,value:t.byteLength}),postMessage({cmd:M,type:ve,value:n}),vr.decode(t,i?1:0,n)):gr.debug.warn(pr.LOG_NAME,"has not init video codec"),pr.lastBuffer=pr.lastBuffer.subarray(e),pr.firstStartCodeOffset=0==r?14:0,pr.parsedOffset=pr.firstStartCodeOffset+4,pr.state="findFirstStartCode"}}}},getNaluDts(){let e=pr.localDts;return pr.localDts=pr.localDts+40,e}},mr={TAG_NAME:"worker TsLoaderV2",first_parse_:!0,tsPacketSize:0,syncOffset:0,pmt_:null,config_:null,media_info_:new Bi,timescale_:90,duration_:0,pat_:{version_number:0,network_pid:0,program_map_pid:{}},current_program_:null,current_pmt_pid_:-1,program_pmt_map_:{},pes_slice_queues_:{},section_slice_queues_:{},video_metadata_:{vps:null,sps:null,pps:null,details:null},audio_metadata_:{codec:null,audio_object_type:null,sampling_freq_index:null,sampling_frequency:null,channel_config:null},last_pcr_:null,audio_last_sample_pts_:void 0,aac_last_incomplete_data_:null,has_video_:!1,has_audio_:!1,video_init_segment_dispatched_:!1,audio_init_segment_dispatched_:!1,video_metadata_changed_:!1,audio_metadata_changed_:!1,loas_previous_frame:null,video_track_:{type:"video",id:1,sequenceNumber:0,samples:[],length:0},audio_track_:{type:"audio",id:2,sequenceNumber:0,samples:[],length:0},_remainingPacketData:null,init(){},destroy(){mr.media_info_=null,mr.pes_slice_queues_=null,mr.section_slice_queues_=null,mr.video_metadata_=null,mr.audio_metadata_=null,mr.aac_last_incomplete_data_=null,mr.video_track_=null,mr.audio_track_=null,mr._remainingPacketData=null},probe(e){let t=new Uint8Array(e),r=-1,i=188;if(t.byteLength<=3*i)return{needMoreData:!0};for(;-1===r;){let e=Math.min(1e3,t.byteLength-3*i);for(let n=0;n<e;){if(71===t[n]&&71===t[n+i]&&71===t[n+2*i]){r=n;break}n++}if(-1===r)if(188===i)i=192;else{if(192!==i)break;i=204}}return-1===r?{match:!1}:(192===i&&r>=4&&(r-=4),{match:!0,consumed:0,ts_packet_size:i,sync_offset:r})},_initPmt:()=>({program_number:0,version_number:0,pcr_pid:0,pid_stream_type:{},common_pids:{h264:void 0,h265:void 0,adts_aac:void 0,loas_aac:void 0,opus:void 0,ac3:void 0,eac3:void 0,mp3:void 0},pes_private_data_pids:{},timed_id3_pids:{},synchronous_klv_pids:{},asynchronous_klv_pids:{},scte_35_pids:{},smpte2038_pids:{}}),dispatch(e){mr._remainingPacketData&&(e=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if((t=t.filter(Boolean)).length<2)return t[0];const i=new Uint8Array(t.reduce(((e,t)=>e+t.byteLength),0));let n=0;return t.forEach((e=>{i.set(e,n),n+=e.byteLength})),i}(mr._remainingPacketData,e),mr._remainingPacketData=null);let t=e.buffer;const r=mr.parseChunks(t);r?mr._remainingPacketData=e.subarray(r):e.length<this.tsPacketSize&&(mr._remainingPacketData=e)},parseChunks(e){let t=0;if(mr.first_parse_){mr.first_parse_=!1;const r=mr.probe(e);r.match&&(mr.tsPacketSize=r.ts_packet_size,mr.syncOffset=r.sync_offset),t=mr.syncOffset,gr.debug.log(mr.TAG_NAME,`isFirstDispatch and tsPacketSize = ${mr.tsPacketSize}, syncOffset = ${mr.syncOffset}`)}for(;t+mr.tsPacketSize<=e.byteLength;){192===mr.tsPacketSize&&(t+=4);const r=new Uint8Array(e,t,188);let i=r[0];if(71!==i){gr.debug.warn(mr.TAG_NAME,`sync_byte = ${i}, not 0x47`);break}let n=(64&r[1])>>>6;r[1];let o=(31&r[1])<<8|r[2],s=(48&r[3])>>>4,a=15&r[3],d=!(!mr.pmt_||mr.pmt_.pcr_pid!==o),l={},u=4;if(2==s||3==s){let e=r[4];if(e>0&&(d||3==s)){if(l.discontinuity_indicator=(128&r[5])>>>7,l.random_access_indicator=(64&r[5])>>>6,l.elementary_stream_priority_indicator=(32&r[5])>>>5,(16&r[5])>>>4){let e=300*(r[6]<<25|r[7]<<17|r[8]<<9|r[9]<<1|r[10]>>>7)+((1&r[10])<<8|r[11]);mr.last_pcr_=e}}if(2==s||5+e===188){t+=188,204===mr.tsPacketSize&&(t+=16);continue}u=5+e}if(1==s||3==s)if(0===o||o===mr.current_pmt_pid_||null!=mr.pmt_&&mr.pmt_.pid_stream_type[o]===wi){let r=188-u;mr.handleSectionSlice(e,t+u,r,{pid:o,payload_unit_start_indicator:n,continuity_conunter:a,random_access_indicator:l.random_access_indicator})}else if(null!=mr.pmt_&&null!=mr.pmt_.pid_stream_type[o]){let r=188-u,i=mr.pmt_.pid_stream_type[o];o!==mr.pmt_.common_pids.h264&&o!==mr.pmt_.common_pids.h265&&o!==mr.pmt_.common_pids.adts_aac&&o!==mr.pmt_.common_pids.loas_aac&&o!==mr.pmt_.common_pids.ac3&&o!==mr.pmt_.common_pids.eac3&&o!==mr.pmt_.common_pids.opus&&o!==mr.pmt_.common_pids.mp3&&!0!==mr.pmt_.pes_private_data_pids[o]&&!0!==mr.pmt_.timed_id3_pids[o]&&!0!==mr.pmt_.synchronous_klv_pids[o]&&!0!==mr.pmt_.asynchronous_klv_pids[o]||mr.handlePESSlice(e,t+u,r,{pid:o,stream_type:i,payload_unit_start_indicator:n,continuity_conunter:a,random_access_indicator:l.random_access_indicator})}t+=188,204===mr.tsPacketSize&&(t+=16)}return mr.dispatchAudioVideoMediaSegment(),t},handleSectionSlice(e,t,r,i){let n=new Uint8Array(e,t,r),o=mr.section_slice_queues_[i.pid];if(i.payload_unit_start_indicator){let s=n[0];if(null!=o&&0!==o.total_length){let n=new Uint8Array(e,t+1,Math.min(r,s));o.slices.push(n),o.total_length+=n.byteLength,o.total_length===o.expected_length?mr.emitSectionSlices(o,i):mr.clearSlices(o,i)}for(let a=1+s;a<n.byteLength;){if(255===n[a+0])break;let s=(15&n[a+1])<<8|n[a+2];mr.section_slice_queues_[i.pid]=new Ai,o=mr.section_slice_queues_[i.pid],o.expected_length=s+3,o.random_access_indicator=i.random_access_indicator;let d=new Uint8Array(e,t+a,Math.min(r-a,o.expected_length-o.total_length));o.slices.push(d),o.total_length+=d.byteLength,o.total_length===o.expected_length?mr.emitSectionSlices(o,i):o.total_length>=o.expected_length&&mr.clearSlices(o,i),a+=d.byteLength}}else if(null!=o&&0!==o.total_length){let n=new Uint8Array(e,t,Math.min(r,o.expected_length-o.total_length));o.slices.push(n),o.total_length+=n.byteLength,o.total_length===o.expected_length?mr.emitSectionSlices(o,i):o.total_length>=o.expected_length&&mr.clearSlices(o,i)}},handlePESSlice(e,t,r,i){let n=new Uint8Array(e,t,r),o=n[0]<<16|n[1]<<8|n[2];n[3];let s=n[4]<<8|n[5];if(i.payload_unit_start_indicator){if(1!==o)return void gr.debug.warn(mr.TAG_NAME,`handlePESSlice: packet_start_code_prefix should be 1 but with value ${o}`);let e=mr.pes_slice_queues_[i.pid];e&&(0===e.expected_length||e.expected_length===e.total_length?mr.emitPESSlices(e,i):mr.clearSlices(e,i)),mr.pes_slice_queues_[i.pid]=new Ai,mr.pes_slice_queues_[i.pid].random_access_indicator=i.random_access_indicator}if(null==mr.pes_slice_queues_[i.pid])return;let a=mr.pes_slice_queues_[i.pid];a.slices.push(n),i.payload_unit_start_indicator&&(a.expected_length=0===s?0:s+6),a.total_length+=n.byteLength,a.expected_length>0&&a.expected_length===a.total_length?mr.emitPESSlices(a,i):a.expected_length>0&&a.expected_length<a.total_length&&mr.clearSlices(a,i)},emitSectionSlices(e,t){let r=new Uint8Array(e.total_length);for(let t=0,i=0;t<e.slices.length;t++){let n=e.slices[t];r.set(n,i),i+=n.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;let i={};i.pid=t.pid,i.data=r,i.file_position=e.file_position,i.random_access_indicator=e.random_access_indicator,mr.parseSection(i)},emitPESSlices(e,t){let r=new Uint8Array(e.total_length);for(let t=0,i=0;t<e.slices.length;t++){let n=e.slices[t];r.set(n,i),i+=n.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;let i=new Ui;i.pid=t.pid,i.data=r,i.stream_type=t.stream_type,i.random_access_indicator=e.random_access_indicator,mr.parsePES(i)},clearSlices(e){e.slices=[],e.expected_length=-1,e.total_length=0},parseSection(e){let t=e.data,r=e.pid;0===r?mr.parsePAT(t):r===mr.current_pmt_pid_?mr.parsePMT(t):null!=mr.pmt_&&mr.pmt_.scte_35_pids[r]},parsePES(e){let t=e.data,r=t[0]<<16|t[1]<<8|t[2],i=t[3],n=t[4]<<8|t[5];if(1===r){if(188!==i&&190!==i&&191!==i&&240!==i&&241!==i&&255!==i&&242!==i&&248!==i){t[6];let r,o,s=(192&t[7])>>>6,a=t[8];2!==s&&3!==s||(r=536870912*(14&t[9])+4194304*(255&t[10])+16384*(254&t[11])+128*(255&t[12])+(254&t[13])/2,o=3===s?536870912*(14&t[14])+4194304*(255&t[15])+16384*(254&t[16])+128*(255&t[17])+(254&t[18])/2:r);let d,l=9+a;if(0!==n){if(n<3+a)return void gr.debug.warn(mr.TAG_NAME,"Malformed PES: PES_packet_length < 3 + PES_header_data_length");d=n-3-a}else d=t.byteLength-l;let u=t.subarray(l,l+d);switch(e.stream_type){case hi:case pi:mr.parseMP3Payload(u,r);break;case mi:mr.pmt_.common_pids.opus===e.pid||mr.pmt_.common_pids.ac3===e.pid||mr.pmt_.common_pids.eac3===e.pid||(mr.pmt_.asynchronous_klv_pids[e.pid]?mr.parseAsynchronousKLVMetadataPayload(u,e.pid,i):mr.pmt_.smpte2038_pids[e.pid]?mr.parseSMPTE2038MetadataPayload(u,r,o,e.pid,i):mr.parsePESPrivateDataPayload(u,r,o,e.pid,i));break;case _i:mr.parseADTSAACPayload(u,r);break;case gi:mr.parseLOASAACPayload(u,r);break;case yi:case vi:break;case bi:mr.pmt_.timed_id3_pids[e.pid]?mr.parseTimedID3MetadataPayload(u,r,o,e.pid,i):mr.pmt_.synchronous_klv_pids[e.pid]&&mr.parseSynchronousKLVMetadataPayload(u,r,o,e.pid,i);break;case Si:mr.parseH264Payload(u,r,o,e.random_access_indicator);break;case Ei:mr.parseH265Payload(u,r,o,e.random_access_indicator)}}else if((188===i||191===i||240===i||241===i||255===i||242===i||248===i)&&e.stream_type===mi){let r,o=6;r=0!==n?n:t.byteLength-o;let s=t.subarray(o,o+r);mr.parsePESPrivateDataPayload(s,void 0,void 0,e.pid,i)}}else gr.debug.error(mr.TAG_NAME,`parsePES: packet_start_code_prefix should be 1 but with value ${r}`)},parsePAT(e){let t=e[0];if(0!==t)return void Log.e(mr.TAG,`parsePAT: table_id ${t} is not corresponded to PAT!`);let r=(15&e[1])<<8|e[2];e[3],e[4];let i=(62&e[5])>>>1,n=1&e[5],o=e[6];e[7];let s=null;if(1===n&&0===o)s={version_number:0,network_pid:0,program_pmt_pid:{}},s.version_number=i;else if(s=mr.pat_,null==s)return;let a=r-5-4,d=-1,l=-1;for(let t=8;t<8+a;t+=4){let r=e[t]<<8|e[t+1],i=(31&e[t+2])<<8|e[t+3];0===r?s.network_pid=i:(s.program_pmt_pid[r]=i,-1===d&&(d=r),-1===l&&(l=i))}1===n&&0===o&&(null==mr.pat_&&gr.debug.log(mr.TAG_NAME,`Parsed first PAT: ${JSON.stringify(s)}`),mr.pat_=s,mr.current_program_=d,mr.current_pmt_pid_=l)},parsePMT(e){let t=e[0];if(2!==t)return void gr.debug.error(mr.TAG_NAME,`parsePMT: table_id ${t} is not corresponded to PMT!`);let r,i=(15&e[1])<<8|e[2],n=e[3]<<8|e[4],o=(62&e[5])>>>1,s=1&e[5],a=e[6];if(e[7],1===s&&0===a)r=mr._initPmt(),r.program_number=n,r.version_number=o,mr.program_pmt_map_[n]=r;else if(r=mr.program_pmt_map_[n],null==r)return;r.pcr_pid=(31&e[8])<<8|e[9];let d=(15&e[10])<<8|e[11],l=12+d,u=i-9-d-4;for(let t=l;t<l+u;){let i=e[t],n=(31&e[t+1])<<8|e[t+2],o=(15&e[t+3])<<8|e[t+4];r.pid_stream_type[n]=i;let s=r.common_pids.h264||r.common_pids.h265,a=r.common_pids.adts_aac||r.common_pids.loas_aac||r.common_pids.ac3||r.common_pids.eac3||r.common_pids.opus||r.common_pids.mp3;if(i!==Si||s)if(i!==Ei||s)if(i!==_i||a)if(i!==gi||a)if(i!==yi||a)if(i!==vi||a)if(i!==hi&&i!==pi||a)if(i===mi){if(r.pes_private_data_pids[n]=!0,o>0){for(let i=t+5;i<t+5+o;){let t=e[i+0],o=e[i+1];if(5===t){let t=String.fromCharCode(...Array.from(e.subarray(i+2,i+2+o)));"VANC"===t?r.smpte2038_pids[n]=!0:"Opus"===t?r.common_pids.opus=n:"KLVA"===t&&(r.asynchronous_klv_pids[n]=!0)}else if(127===t&&n===r.common_pids.opus){let t=null;if(128===e[i+2]&&(t=e[i+3]),null==t){Log.e(mr.TAG,"Not Supported Opus channel count.");continue}const r={codec:"opus",channel_count:0==(15&t)?2:15&t,channel_config_code:t,sample_rate:48e3},n={codec:"opus",meta:r};0==mr.audio_init_segment_dispatched_?(mr.audio_metadata_=r,mr.dispatchAudioInitSegment(n)):mr.detectAudioMetadataChange(n)&&(mr.dispatchAudioMediaSegment(),mr.dispatchAudioInitSegment(n))}i+=2+o}e.subarray(t+5,t+5+o)}}else if(i===bi){if(o>0)for(let i=t+5;i<t+5+o;){let t=e[i+0],o=e[i+1];if(38===t){let t=e[i+2]<<8|e[i+3]<<0,o=null;65535===t&&(o=String.fromCharCode(...Array.from(e.subarray(i+4,i+4+4))));let s=null;if(255===e[i+4+(65535===t?4:0)]){let r=4+(65535===t?4:0)+1;s=String.fromCharCode(...Array.from(e.subarray(i+r,i+r+4)))}"ID3 "===o&&"ID3 "===s?r.timed_id3_pids[n]=!0:"KLVA"===s&&(r.synchronous_klv_pids[n]=!0)}i+=2+o}}else i===wi&&(r.scte_35_pids[n]=!0);else r.common_pids.mp3=n;else r.common_pids.eac3=n;else r.common_pids.ac3=n;else r.common_pids.loas_aac=n;else r.common_pids.adts_aac=n;else r.common_pids.h265=n;else r.common_pids.h264=n;t+=5+o}n===mr.current_program_&&(null==mr.pmt_&&gr.debug.log(mr.TAG_NAME,`Parsed first PMT: ${JSON.stringify(r)}`),mr.pmt_=r,(r.common_pids.h264||r.common_pids.h265)&&(mr.has_video_=!0),(r.common_pids.adts_aac||r.common_pids.loas_aac||r.common_pids.ac3||r.common_pids.opus||r.common_pids.mp3)&&(mr.has_audio_=!0))},parseSCTE35(e){},parseH264Payload(e,t,r,i){let n=new $t(e),o=null,s=null,a=[],d=0,l=!1;for(;null!=(o=n.readNextNaluPayload());){let e=new Ht(o);if(e.type===De.kSliceSPS){let t=Ft.parseSPS$2(o.data);mr.video_init_segment_dispatched_?!0===mr.detectVideoMetadataChange(e,t)&&(gr.debug.log(mr.TAG_NAME,"H264: Critical h264 metadata has been changed, attempt to re-generate InitSegment"),mr.video_metadata_changed_=!0,mr.video_metadata_={vps:void 0,sps:e,pps:void 0,details:t}):(mr.video_metadata_.sps=e,mr.video_metadata_.details=t)}else e.type===De.kSlicePPS?mr.video_init_segment_dispatched_&&!mr.video_metadata_changed_||(mr.video_metadata_.pps=e,mr.video_metadata_.sps&&mr.video_metadata_.pps&&(mr.video_metadata_changed_&&mr.dispatchVideoMediaSegment(),mr.dispatchVideoInitSegment())):(e.type===De.kSliceIDR||e.type===De.kSliceNonIDR&&1===i)&&(l=!0);mr.video_init_segment_dispatched_&&(a.push(e),d+=e.data.byteLength)}let u=Math.floor(t/mr.timescale_),c=Math.floor(r/mr.timescale_);if(a.length){let e=mr.video_track_;for(let e=0;e<a.length;e++){let t=a[e];if(null==s)s=t.data;else{let e=new Uint8Array(s.byteLength+t.data.byteLength);e.set(s,0),e.set(t.data,s.byteLength),s=e}}let t={length:d,isIFrame:l,dts:c,pts:u,cts:u-c,payload:s,type:te,isHevc:!1};e.samples.push(t),e.length=s.byteLength}},parseH265Payload(e,t,r,i){let n=new nr(e),o=null,s=null,a=[],d=0,l=!1;for(;null!=(o=n.readNextNaluPayload());){let e=new or(o);if(e.type===Pe.kSliceVPS){if(!mr.video_init_segment_dispatched_){let t=Kt.parseVPS(o.data);mr.video_metadata_.vps=e,mr.video_metadata_.details={...mr.video_metadata_.details,...t}}}else if(e.type===Pe.kSliceSPS){let t=Kt.parseSPS(o.data);mr.video_init_segment_dispatched_?!0===mr.detectVideoMetadataChange(e,t)&&(gr.debug.log(mr.TAG_NAME,"H265: Critical h265 metadata has been changed, attempt to re-generate InitSegment"),mr.video_metadata_changed_=!0,mr.video_metadata_={vps:void 0,sps:e,pps:void 0,details:t}):(mr.video_metadata_.sps=e,mr.video_metadata_.details={...mr.video_metadata_.details,...t})}else if(e.type===Pe.kSlicePPS){if(!mr.video_init_segment_dispatched_||mr.video_metadata_changed_){let t=Kt.parsePPS(o.data);mr.video_metadata_.pps=e,mr.video_metadata_.details={...mr.video_metadata_.details,...t},mr.video_metadata_.vps&&mr.video_metadata_.sps&&mr.video_metadata_.pps&&(mr.video_metadata_changed_&&mr.dispatchVideoMediaSegment(),mr.dispatchVideoInitSegment())}}else e.type!==Pe.kSliceIDR_W_RADL&&e.type!==Pe.kSliceIDR_N_LP&&e.type!==Pe.kSliceCRA_NUT||(l=!0);mr.video_init_segment_dispatched_&&(a.push(e),d+=e.data.byteLength)}let u=Math.floor(t/mr.timescale_),c=Math.floor(r/mr.timescale_);if(a.length){let e=mr.video_track_;for(let e=0;e<a.length;e++){let t=a[e];if(null==s)s=t.data;else{let e=new Uint8Array(s.byteLength+t.data.byteLength);e.set(s,0),e.set(t.data,s.byteLength),s=e}}let t={type:te,length:d,isIFrame:l,dts:c,pts:u,cts:u-c,payload:s,isHevc:!0};e.samples.push(t),e.length=s.byteLength}},detectVideoMetadataChange(e,t){if(t.codec_mimetype!==mr.video_metadata_.details.codec_mimetype)return gr.debug.log(mr.TAG_NAME,`Video: Codec mimeType changed from ${mr.video_metadata_.details.codec_mimetype} to ${t.codec_mimetype}`),!0;if(t.codec_size.width!==mr.video_metadata_.details.codec_size.width||t.codec_size.height!==mr.video_metadata_.details.codec_size.height){let e=mr.video_metadata_.details.codec_size,r=t.codec_size;return gr.debug.log(mr.TAG_NAME,`Video: Coded Resolution changed from ${e.width}x${e.height} to ${r.width}x${r.height}`),!0}return t.present_size.width!==mr.video_metadata_.details.present_size.width&&(gr.debug.log(mr.TAG_NAME,`Video: Present resolution width changed from ${mr.video_metadata_.details.present_size.width} to ${t.present_size.width}`),!0)},isInitSegmentDispatched:()=>mr.has_video_&&mr.has_audio_?mr.video_init_segment_dispatched_&&mr.audio_init_segment_dispatched_:mr.has_video_&&!mr.has_audio_?mr.video_init_segment_dispatched_:!(mr.has_video_||!mr.has_audio_)&&mr.audio_init_segment_dispatched_,dispatchVideoInitSegment(){let e=mr.video_metadata_.details,t={type:"video"};t.id=mr.video_track_.id,t.timescale=1e3,t.duration=mr.duration_,t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height,t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.sarRatio=e.sar_ratio,t.frameRate=e.frame_rate;let r=t.frameRate.fps_den,i=t.frameRate.fps_num;if(t.refSampleDuration=r/i*1e3,t.codec=e.codec_mimetype,mr.video_metadata_.vps){let e=mr.video_metadata_.vps.data.subarray(4),r=mr.video_metadata_.sps.data.subarray(4),i=mr.video_metadata_.pps.data.subarray(4);t.hvcc=Qt({vps:e,sps:r,pps:i}),0==mr.video_init_segment_dispatched_&&gr.debug.log(mr.TAG_NAME,`Generated first HEVCDecoderConfigurationRecord for mimeType: ${t.codec}`),t.hvcc&&gr.decodeVideo(t.hvcc,0,!0,0)}else{let e=mr.video_metadata_.sps.data.subarray(4),r=mr.video_metadata_.pps.data.subarray(4);t.avcc=function(e){let{sps:t,pps:r}=e,i=8+t.byteLength+1+2+r.byteLength,n=!1;const o=Ft.parseSPS$2(t);66!==t[3]&&77!==t[3]&&88!==t[3]&&(n=!0,i+=4);let s=new Uint8Array(i);s[0]=1,s[1]=t[1],s[2]=t[2],s[3]=t[3],s[4]=255,s[5]=225;let a=t.byteLength;s[6]=a>>>8,s[7]=255&a;let d=8;s.set(t,8),d+=a,s[d]=1;let l=r.byteLength;s[d+1]=l>>>8,s[d+2]=255&l,s.set(r,d+3),d+=3+l,n&&(s[d]=252|o.chroma_format_idc,s[d+1]=248|o.bit_depth_luma-8,s[d+2]=248|o.bit_depth_chroma-8,s[d+3]=0,d+=4);const u=[23,0,0,0,0],c=new Uint8Array(u.length+s.byteLength);return c.set(u,0),c.set(s,u.length),c}({sps:e,pps:r}),0==mr.video_init_segment_dispatched_&&gr.debug.log(mr.TAG_NAME,`Generated first AVCDecoderConfigurationRecord for mimeType: ${t.codec}`),t.avcc&&gr.decodeVideo(t.avcc,0,!0,0)}mr.video_init_segment_dispatched_=!0,mr.video_metadata_changed_=!1;let n=mr.media_info_;n.hasVideo=!0,n.width=t.codecWidth,n.height=t.codecHeight,n.fps=t.frameRate.fps,n.profile=t.profile,n.level=t.level,n.refFrames=e.ref_frames,n.chromaFormat=e.chroma_format_string,n.sarNum=t.sarRatio.width,n.sarDen=t.sarRatio.height,n.videoCodec=t.codec,n.hasAudio&&n.audioCodec?n.mimeType=`video/mp2t; codecs="${n.videoCodec},${n.audioCodec}"`:n.mimeType=`video/mp2t; codecs="${n.videoCodec}"`},dispatchVideoMediaSegment(){mr.isInitSegmentDispatched()&&mr.video_track_.length&&mr._preDoDecode()},dispatchAudioMediaSegment(){mr.isInitSegmentDispatched()&&mr.audio_track_.length&&mr._preDoDecode()},dispatchAudioVideoMediaSegment(){mr.isInitSegmentDispatched()&&(mr.audio_track_.length||mr.video_track_.length)&&mr._preDoDecode()},parseADTSAACPayload(e,t){if(mr.has_video_&&!mr.video_init_segment_dispatched_)return;if(mr.aac_last_incomplete_data_){let t=new Uint8Array(e.byteLength+mr.aac_last_incomplete_data_.byteLength);t.set(mr.aac_last_incomplete_data_,0),t.set(e,mr.aac_last_incomplete_data_.byteLength),e=t}let r,i;if(null!=t&&(i=t/mr.timescale_),"aac"===mr.audio_metadata_.codec){if(null==t&&null!=mr.audio_last_sample_pts_)r=1024/mr.audio_metadata_.sampling_frequency*1e3,i=mr.audio_last_sample_pts_+r;else if(null==t)return void gr.debug.warn(mr.TAG_NAME,"AAC: Unknown pts");if(mr.aac_last_incomplete_data_&&mr.audio_last_sample_pts_){r=1024/mr.audio_metadata_.sampling_frequency*1e3;let e=mr.audio_last_sample_pts_+r;Math.abs(e-i)>1&&(gr.debug.warn(mr.TAG_NAME,`AAC: Detected pts overlapped, expected: ${e}ms, PES pts: ${i}ms`),i=e)}}let n,o=new ct(e),s=null,a=i;for(;null!=(s=o.readNextAACFrame());){r=1024/s.sampling_frequency*1e3;const e={codec:"aac",data:s};0==mr.audio_init_segment_dispatched_?(mr.audio_metadata_={codec:"aac",audio_object_type:s.audio_object_type,sampling_freq_index:s.sampling_freq_index,sampling_frequency:s.sampling_frequency,channel_config:s.channel_config},mr.dispatchAudioInitSegment(e)):mr.detectAudioMetadataChange(e)&&(mr.dispatchAudioMediaSegment(),mr.dispatchAudioInitSegment(e)),n=a;let t=Math.floor(a);const i=new Uint8Array(s.data.length+2);i.set([175,1],0),i.set(s.data,2);let o={payload:i,length:i.byteLength,pts:t,dts:t,type:ee};mr.audio_track_.samples.push(o),mr.audio_track_.length+=i.byteLength,a+=r}o.hasIncompleteData()&&(mr.aac_last_incomplete_data_=o.getIncompleteData()),n&&(mr.audio_last_sample_pts_=n)},parseLOASAACPayload(e,t){if(mr.has_video_&&!mr.video_init_segment_dispatched_)return;if(mr.aac_last_incomplete_data_){let t=new Uint8Array(e.byteLength+mr.aac_last_incomplete_data_.byteLength);t.set(mr.aac_last_incomplete_data_,0),t.set(e,mr.aac_last_incomplete_data_.byteLength),e=t}let r,i;if(null!=t&&(i=t/mr.timescale_),"aac"===mr.audio_metadata_.codec){if(null==t&&null!=mr.audio_last_sample_pts_)r=1024/mr.audio_metadata_.sampling_frequency*1e3,i=mr.audio_last_sample_pts_+r;else if(null==t)return void gr.debug.warn(mr.TAG_NAME,"AAC: Unknown pts");if(mr.aac_last_incomplete_data_&&mr.audio_last_sample_pts_){r=1024/mr.audio_metadata_.sampling_frequency*1e3;let e=mr.audio_last_sample_pts_+r;Math.abs(e-i)>1&&(gr.debug.warn(mr.TAG,`AAC: Detected pts overlapped, expected: ${e}ms, PES pts: ${i}ms`),i=e)}}let n,o=new ft(e),s=null,a=i;for(;null!=(s=o.readNextAACFrame(mr.loas_previous_frame??void 0));){mr.loas_previous_frame=s,r=1024/s.sampling_frequency*1e3;const e={codec:"aac",data:s};0==mr.audio_init_segment_dispatched_?(mr.audio_metadata_={codec:"aac",audio_object_type:s.audio_object_type,sampling_freq_index:s.sampling_freq_index,sampling_frequency:s.sampling_frequency,channel_config:s.channel_config},mr.dispatchAudioInitSegment(e)):mr.detectAudioMetadataChange(e)&&(mr.dispatchAudioMediaSegment(),mr.dispatchAudioInitSegment(e)),n=a;let t=Math.floor(a);const i=new Uint8Array(s.data.length+2);i.set([175,1],0),i.set(s.data,2);let o={payload:i,length:i.byteLength,pts:t,dts:t,type:ee};mr.audio_track_.samples.push(o),mr.audio_track_.length+=i.byteLength,a+=r}o.hasIncompleteData()&&(mr.aac_last_incomplete_data_=o.getIncompleteData()),n&&(mr.audio_last_sample_pts_=n)},parseAC3Payload(e,t){},parseEAC3Payload(e,t){},parseOpusPayload(e,t){},parseMP3Payload(e,t){if(mr.has_video_&&!mr.video_init_segment_dispatched_)return;let r=[44100,48e3,32e3,0],i=[22050,24e3,16e3,0],n=[11025,12e3,8e3,0],o=e[1]>>>3&3,s=(6&e[1])>>1;e[2];let a=(12&e[2])>>>2,d=3!==(e[3]>>>6&3)?2:1,l=0,u=34;switch(o){case 0:l=n[a];break;case 2:l=i[a];break;case 3:l=r[a]}switch(s){case 1:u=34;break;case 2:u=33;break;case 3:u=32}const c={};c.object_type=u,c.sample_rate=l,c.channel_count=d,c.data=e;const f={codec:"mp3",data:c};0==mr.audio_init_segment_dispatched_?(mr.audio_metadata_={codec:"mp3",object_type:u,sample_rate:l,channel_count:d},mr.dispatchAudioInitSegment(f)):mr.detectAudioMetadataChange(f)&&(mr.dispatchAudioMediaSegment(),mr.dispatchAudioInitSegment(f));let h={payload:e,length:e.byteLength,pts:t/mr.timescale_,dts:t/mr.timescale_,type:ee};mr.audio_track_.samples.push(h),mr.audio_track_.length+=e.byteLength},detectAudioMetadataChange(e){if(e.codec!==mr.audio_metadata_.codec)return gr.debug.log(mr.TAG_NAME,`Audio: Audio Codecs changed from ${mr.audio_metadata_.codec} to ${e.codec}`),!0;if("aac"===e.codec&&"aac"===mr.audio_metadata_.codec){const t=e.data;if(t.audio_object_type!==mr.audio_metadata_.audio_object_type)return gr.debug.log(mr.TAG_NAME,`AAC: AudioObjectType changed from ${mr.audio_metadata_.audio_object_type} to ${t.audio_object_type}`),!0;if(t.sampling_freq_index!==mr.audio_metadata_.sampling_freq_index)return gr.debug.log(mr.TAG_NAME,`AAC: SamplingFrequencyIndex changed from ${mr.audio_metadata_.sampling_freq_index} to ${t.sampling_freq_index}`),!0;if(t.channel_config!==mr.audio_metadata_.channel_config)return gr.debug.log(mr.TAG_NAME,`AAC: Channel configuration changed from ${mr.audio_metadata_.channel_config} to ${t.channel_config}`),!0}else if("ac-3"===e.codec&&"ac-3"===mr.audio_metadata_.codec){const t=e.data;if(t.sampling_frequency!==mr.audio_metadata_.sampling_frequency)return gr.debug.log(mr.TAG_NAME,`AC3: Sampling Frequency changed from ${mr.audio_metadata_.sampling_frequency} to ${t.sampling_frequency}`),!0;if(t.bit_stream_identification!==mr.audio_metadata_.bit_stream_identification)return gr.debug.log(mr.TAG_NAME,`AC3: Bit Stream Identification changed from ${mr.audio_metadata_.bit_stream_identification} to ${t.bit_stream_identification}`),!0;if(t.bit_stream_mode!==mr.audio_metadata_.bit_stream_mode)return gr.debug.log(mr.TAG_NAME,`AC3: BitStream Mode changed from ${mr.audio_metadata_.bit_stream_mode} to ${t.bit_stream_mode}`),!0;if(t.channel_mode!==mr.audio_metadata_.channel_mode)return gr.debug.log(mr.TAG_NAME,`AC3: Channel Mode changed from ${mr.audio_metadata_.channel_mode} to ${t.channel_mode}`),!0;if(t.low_frequency_effects_channel_on!==mr.audio_metadata_.low_frequency_effects_channel_on)return gr.debug.log(mr.TAG_NAME,`AC3: Low Frequency Effects Channel On changed from ${mr.audio_metadata_.low_frequency_effects_channel_on} to ${t.low_frequency_effects_channel_on}`),!0}else if("opus"===e.codec&&"opus"===mr.audio_metadata_.codec){const t=e.meta;if(t.sample_rate!==mr.audio_metadata_.sample_rate)return gr.debug.log(mr.TAG_NAME,`Opus: SamplingFrequencyIndex changed from ${mr.audio_metadata_.sample_rate} to ${t.sample_rate}`),!0;if(t.channel_count!==mr.audio_metadata_.channel_count)return gr.debug.log(mr.TAG_NAME,`Opus: Channel count changed from ${mr.audio_metadata_.channel_count} to ${t.channel_count}`),!0}else if("mp3"===e.codec&&"mp3"===mr.audio_metadata_.codec){const t=e.data;if(t.object_type!==mr.audio_metadata_.object_type)return gr.debug.log(mr.TAG_NAME,`MP3: AudioObjectType changed from ${mr.audio_metadata_.object_type} to ${t.object_type}`),!0;if(t.sample_rate!==mr.audio_metadata_.sample_rate)return gr.debug.log(mr.TAG_NAME,`MP3: SamplingFrequencyIndex changed from ${mr.audio_metadata_.sample_rate} to ${t.sample_rate}`),!0;if(t.channel_count!==mr.audio_metadata_.channel_count)return gr.debug.log(mr.TAG_NAME,`MP3: Channel count changed from ${mr.audio_metadata_.channel_count} to ${t.channel_count}`),!0}return!1},dispatchAudioInitSegment(e){let t={type:"audio"};if(t.id=mr.audio_track_.id,t.timescale=1e3,t.duration=mr.duration_,"aac"===mr.audio_metadata_.codec){let r="aac"===e.codec?e.data:null,i=new ki(r);t.audioSampleRate=i.sampling_rate,t.audioSampleRateIndex=i.sampling_index,t.channelCount=i.channel_count,t.codec=i.codec_mimetype,t.originalCodec=i.original_codec_mimetype,t.config=i.config,t.refSampleDuration=1024/t.audioSampleRate*t.timescale;const n=nt({profile:gr._opt.mseDecodeAudio?i.object_type:i.original_object_type,sampleRate:t.audioSampleRateIndex,channel:t.channelCount});gr.decodeAudio(n,0)}else"ac-3"===mr.audio_metadata_.codec||"ec-3"===mr.audio_metadata_.codec||"opus"===mr.audio_metadata_.codec||"mp3"===mr.audio_metadata_.codec&&(t.audioSampleRate=mr.audio_metadata_.sample_rate,t.channelCount=mr.audio_metadata_.channel_count,t.codec="mp3",t.originalCodec="mp3",t.config=void 0);0==mr.audio_init_segment_dispatched_&&gr.debug.log(mr.TAG_NAME,`Generated first AudioSpecificConfig for mimeType: ${t.codec}`),mr.audio_init_segment_dispatched_=!0,mr.video_metadata_changed_=!1;let r=mr.media_info_;r.hasAudio=!0,r.audioCodec=t.originalCodec,r.audioSampleRate=t.audioSampleRate,r.audioChannelCount=t.channelCount,r.hasVideo&&r.videoCodec?r.mimeType=`video/mp2t; codecs="${r.videoCodec},${r.audioCodec}"`:r.mimeType=`video/mp2t; codecs="${r.audioCodec}"`},dispatchPESPrivateDataDescriptor(e,t,r){},parsePESPrivateDataPayload(e,t,r,i,n){let o=new Ti;if(o.pid=i,o.stream_id=n,o.len=e.byteLength,o.data=e,null!=t){let e=Math.floor(t/mr.timescale_);o.pts=e}else o.nearest_pts=mr.getNearestTimestampMilliseconds();if(null!=r){let e=Math.floor(r/mr.timescale_);o.dts=e}},parseTimedID3MetadataPayload(e,t,r,i,n){gr.debug.log(mr.TAG_NAME,`Timed ID3 Metadata: pid=${i}, pts=${t}, dts=${r}, stream_id=${n}`)},parseSynchronousKLVMetadataPayload(e,t,r,i,n){gr.debug.log(mr.TAG_NAME,`Synchronous KLV Metadata: pid=${i}, pts=${t}, dts=${r}, stream_id=${n}`)},parseAsynchronousKLVMetadataPayload(e,t,r){gr.debug.log(mr.TAG_NAME,`Asynchronous KLV Metadata: pid=${t}, stream_id=${r}`)},parseSMPTE2038MetadataPayload(e,t,r,i,n){gr.debug.log(mr.TAG_NAME,`SMPTE 2038 Metadata: pid=${i}, pts=${t}, dts=${r}, stream_id=${n}`)},getNearestTimestampMilliseconds(){if(null!=mr.audio_last_sample_pts_)return Math.floor(mr.audio_last_sample_pts_);if(null!=mr.last_pcr_){return Math.floor(mr.last_pcr_/300/mr.timescale_)}},_preDoDecode(){const e=mr.video_track_,t=mr.audio_track_;let r=e.samples;t.samples.length>0&&(r=e.samples.concat(t.samples),r=r.sort(((e,t)=>e.dts-t.dts))),r.forEach((e=>{const t=new Uint8Array(e.payload);delete e.payload,e.type===te?mr._doDecodeVideo({...e,payload:t}):e.type===ee&&mr._doDecodeAudio({...e,payload:t})})),e.samples=[],e.length=0,t.samples=[],t.length=0},_doDecodeVideo(e){const t=new Uint8Array(e.payload);let r=null;r=e.isHevc?er(t,e.isIFrame):Rt(t,e.isIFrame),e.isIFrame&&gr.calcIframeIntervalTimestamp(e.dts);let i=gr.cryptoPayload(r,e.isIFrame);gr.decode(i,{type:te,ts:e.dts,isIFrame:e.isIFrame,cts:e.cts})},_doDecodeAudio(e){const t=new Uint8Array(e.payload);let r=t;Ut(gr._opt.m7sCryptoAudio)&&(r=gr.cryptoPayloadAudio(t)),gr.decode(r,{type:ee,ts:e.dts,isIFrame:!1,cts:0})}},_r=null;self.Worker&&self.MediaSource&&"canConstructInDedicatedWorker"in self.MediaSource&&!0===self.MediaSource.canConstructInDedicatedWorker&&(_r={TAG_NAME:"worker MediaSource",_resetInIt(){_r.isAvc=null,_r.isAAC=null,_r.videoInfo={},_r.videoMeta={},_r.audioMeta={},_r.sourceBuffer=null,_r.audioSourceBuffer=null,_r.hasInit=!1,_r.hasAudioInit=!1,_r.isAudioInitInfo=!1,_r.videoMimeType="",_r.audioMimeType="",_r.cacheTrack={},_r.cacheAudioTrack={},_r.timeInit=!1,_r.sequenceNumber=0,_r.audioSequenceNumber=0,_r.firstRenderTime=null,_r.firstAudioTime=null,_r.mediaSourceAppendBufferFull=!1,_r.mediaSourceAppendBufferError=!1,_r.mediaSourceAddSourceBufferError=!1,_r.mediaSourceBufferError=!1,_r.mediaSourceError=!1,_r.prevTimestamp=null,_r.decodeDiffTimestamp=null,_r.prevDts=null,_r.prevAudioDts=null,_r.prevPayloadBufferSize=0,_r.isWidthOrHeightChanged=!1,_r.prevTs=null,_r.prevAudioTs=null,_r.eventListenList=[],_r.pendingRemoveRanges=[],_r.pendingSegments=[],_r.pendingAudioRemoveRanges=[],_r.pendingAudioSegments=[],_r.supportVideoFrameCallbackHandle=null,_r.audioSourceBufferCheckTimeout=null,_r.audioSourceNoDataCheckTimeout=null,_r.hasPendingEos=!1,_r.$video={currentTime:0,readyState:0}},init(){_r.events=new oi,_r._resetInIt(),_r.mediaSource=new self.MediaSource,_r.isDecodeFirstIIframe=!!Tt(gr._opt.checkFirstIFrame),_r._bindMediaSourceEvents()},destroy(){_r.stop(),_r._clearAudioSourceBufferCheckTimeout(),_r.eventListenList&&_r.eventListenList.length&&(_r.eventListenList.forEach((e=>e())),_r.eventListenList=[]),_r._resetInIt()},getState:()=>_r.mediaSource&&_r.mediaSource.readyState,isStateOpen:()=>_r.getState()===Re,isStateClosed:()=>_r.getState()===Ne,isStateEnded:()=>_r.getState()===Me,_bindMediaSourceEvents(){const{proxy:e}=_r.events,t=e(_r.mediaSource,Oe,(()=>{gr.debug.log(_r.TAG_NAME,"sourceOpen"),_r._onMediaSourceSourceOpen()})),r=e(_r.mediaSource,ze,(()=>{gr.debug.log(_r.TAG_NAME,"sourceClose")})),i=e(_r.mediaSource,Ge,(()=>{gr.debug.log(_r.TAG_NAME,"sourceended")}));_r.eventListenList.push(t,r,i)},_onMediaSourceSourceOpen(){_r.sourceBuffer||(gr.debug.log(_r.TAG_NAME,"onMediaSourceSourceOpen() sourceBuffer is null and next init"),_r._initSourceBuffer()),_r.audioSourceBuffer||(gr.debug.log(_r.TAG_NAME,"onMediaSourceSourceOpen() audioSourceBuffer is null and next init"),_r._initAudioSourceBuffer()),_r._hasPendingSegments()&&_r._doAppendSegments()},decodeVideo(e,t,r,i){if(gr.isDestroyed)gr.debug.warn(_r.TAG_NAME,"decodeVideo() and decoder is destroyed");else if(Tt(_r.hasInit))if(r&&e[1]===Ve.sequenceHeader){const i=15&e[0];if(i===xe&&Tt(function(){let e=!1;return"MediaSource"in self&&(self.MediaSource.isTypeSupported(Le.hev)||self.MediaSource.isTypeSupported(Le.hev2)||self.MediaSource.isTypeSupported(Le.hev3)||self.MediaSource.isTypeSupported(Le.hev4)||self.MediaSource.isTypeSupported(Le.hev5))&&(e=!0),e}()))return void _r.emitError(Ae.mediaSourceH265NotSupport);_r.videoInfo.codec=i,postMessage({cmd:D,code:i});const n=new Uint8Array(e);postMessage({cmd:P,buffer:n,codecId:i},[n.buffer]),_r.hasInit=_r._decodeConfigurationRecord(e,t,r,i)}else gr.debug.warn(_r.TAG_NAME,`decodeVideo has not init , isIframe is ${r} , payload is ${e[1]}`);else if(!_r.isDecodeFirstIIframe&&r&&(_r.isDecodeFirstIIframe=!0),_r.isDecodeFirstIIframe){if(r&&0===e[1]){const t=15&e[0];let r={};if(t===Te){r=Lt(e.slice(5))}else t===xe&&(r=Xt(e));const i=_r.videoInfo;i&&i.codecWidth&&i.codecWidth&&r&&r.codecWidth&&r.codecHeight&&(r.codecWidth!==i.codecWidth||r.codecHeight!==i.codecWidth)&&(gr.debug.warn(_r.TAG_NAME,`\n                                decodeVideo: video width or height is changed,\n                                old width is ${i.codecWidth}, old height is ${i.codecWidth},\n                                new width is ${r.codecWidth}, new height is ${r.codecHeight},\n                                and emit change event`),_r.isWidthOrHeightChanged=!0,_r.emitError(Ae.mseWidthOrHeightChange))}if(_r.isWidthOrHeightChanged)return void gr.debug.warn(_r.TAG_NAME,"decodeVideo: video width or height is changed, and return");if(At(e))return void gr.debug.warn(_r.TAG_NAME,"decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<12)return void gr.debug.warn(_r.TAG_NAME,`decodeVideo and payload is too small , payload length is ${e.byteLength}`);let n=t;if(gr.isPlayer){if(null===_r.firstRenderTime&&(_r.firstRenderTime=t,postMessage({cmd:J,value:_r.firstRenderTime})),n=t-_r.firstRenderTime,n<0&&(gr.debug.warn(_r.TAG_NAME,`decodeVideo\n                                 local dts is < 0 , ts is ${t} and prevTs is ${_r.prevTs},\n                                 firstRenderTime is ${_r.firstRenderTime} and mseCorrectTimeDuration is ${gr._opt.mseCorrectTimeDuration}`),n=null===_r.prevDts?0:_r.prevDts+gr._opt.mseCorrectTimeDuration,_r._checkTsIsMaxDiff(t)))return gr.debug.warn(_r.TAG_NAME,`decodeVideo is max diff , ts is ${t} and prevTs is ${_r.prevTs}, diff is ${_r.prevTs-t}`),void _r.emitError(Ae.mediaSourceTsIsMaxDiff);if(null!==_r.prevDts&&n<=_r.prevDts){if(gr.debug.warn(_r.TAG_NAME,`\n                                decodeVideo dts is less than(or equal) prev dts ,\n                                dts is ${n} and prev dts is ${_r.prevDts} ，\n                                and now ts is ${t} and prev ts is ${_r.prevTs} ，\n                                and diff is ${t-_r.prevTs} and firstRenderTime is ${_r.firstRenderTime} and isIframe is ${r}，\n                                and mseCorrectTimeDuration is ${gr._opt.mseCorrectTimeDuration},\n                                and prevPayloadBufferSize is ${_r.prevPayloadBufferSize} and payload size is ${e.byteLength}`),n===_r.prevDts&&_r.prevPayloadBufferSize===e.byteLength)return void gr.debug.warn(_r.TAG_NAME,"decodeVideo dts is equal to prev dts and payload size is equal to prev payload size so drop this frame");if(n=_r.prevDts+gr._opt.mseCorrectTimeDuration,_r._checkTsIsMaxDiff(t))return gr.debug.warn(_r.TAG_NAME,`decodeVideo is max diff , ts is ${t} and prevTs is ${_r.prevTs}, diff is ${_r.prevTs-t} and emit replay`),void _r.emitError(Ae.mediaSourceTsIsMaxDiff)}}gr.isPlayer?_r._decodeVideo(e,n,r,i,t):gr.isPlayback,_r.prevDts=n,_r.prevPayloadBufferSize=e.byteLength,_r.prevTs=t}else gr.debug.log(_r.TAG_NAME,"decodeVideo first frame is not iFrame")},decodeAudio(e,t){if(gr.isDestroyed)gr.debug.warn(_r.TAG_NAME,"decodeAudio() and decoder is destroyed");else if(Tt(_r.hasAudioInit))_r.hasAudioInit=_r._decodeAudioConfigurationRecord(e,t);else{let r=t;if(ot(e))return void gr.debug.log(_r.TAG_NAME,"decodeAudio and has already initialized and payload is aac codec packet so drop this frame");if(_r._clearAudioNoDataCheckTimeout(),gr.isPlayer){if(null===_r.firstAudioTime&&(_r.firstAudioTime=t,null!==_r.firstRenderTime&&null!==_r.prevTs)){const e=Math.abs(_r.firstRenderTime-_r.prevTs);e>300&&(_r.firstAudioTime-=e,gr.debug.warn(_r.TAG_NAME,`video\n                                    firstAudioTime is ${_r.firstRenderTime} and current time is ${_r.prevTs}\n                                    play time is ${e} and firstAudioTime ${t} - ${e} = ${_r.firstAudioTime}`))}r=t-_r.firstAudioTime,r<0&&(gr.debug.warn(_r.TAG_NAME,`decodeAudio\n                             local dts is < 0 , ts is ${t} and prevTs is ${_r.prevAudioTs},\n                             firstAudioTime is ${_r.firstAudioTime}`),r=null===_r.prevAudioDts?0:_r.prevAudioDts+gr._opt.mseCorrectAudioTimeDuration),null!==_r.prevAudioTs&&r<=_r.prevAudioDts&&(gr.debug.warn(_r.TAG_NAME,`\n                            decodeAudio dts is less than(or equal) prev dts ,\n                            dts is ${r} and prev dts is ${_r.prevAudioDts} ，\n                            and now ts is ${t} and prev ts is ${_r.prevAudioTs} ，\n                            and diff is ${t-_r.prevAudioTs}`),r=_r.prevAudioDts+gr._opt.mseCorrectAudioTimeDuration)}gr.isPlayer?_r._decodeAudio(e,r,t):gr.isPlayback,_r.prevAudioTs=t,_r.prevAudioDts=r}},_checkTsIsMaxDiff:e=>_r.prevTs>0&&e<_r.prevTs&&_r.prevTs-e>w,_decodeConfigurationRecord(e,t,r,i){let n=e.slice(5),o={};if(i===Te?o=Lt(n):i===xe&&(o=Zt(n)),_r.videoInfo.width=o.codecWidth,_r.videoInfo.height=o.codecHeight,0===o.codecWidth&&0===o.codecHeight)return gr.debug.warn(_r.TAG_NAME,"_decodeConfigurationRecord error",JSON.stringify(o)),_r.emitError(Ae.mediaSourceDecoderConfigurationError),!1;const s={id:Xe,type:"video",timescale:1e3,duration:0,avcc:n,codecWidth:o.codecWidth,codecHeight:o.codecHeight,videoType:o.videoType},a=si.generateInitSegment(s);_r.isAvc=i===Te;let d=o.codec;return _r.videoMimeType=d?`video/mp4; codecs="${o.codec}"`:_r.isAvc?Le.avc:Le.hev,postMessage({cmd:A,w:o.codecWidth,h:o.codecHeight}),_r._initSourceBuffer(),_r.appendBuffer(a.buffer),_r.sequenceNumber=0,_r.cacheTrack={},_r.timeInit=!1,!0},_decodeAudioConfigurationRecord(e,t){const r=e[0]>>4,i=e[0]>>1&1,n=r===Ce.MP3,o=r===Ce.AAC;if(Tt(o||n))return gr.debug.warn(_r.TAG_NAME,`_decodeAudioConfigurationRecord audio codec is not support , codecId is ${r} ant auto wasm decode`),_r.emitError(Ae.mediaSourceAudioG711NotSupport),!1;const s={id:Ze,type:"audio",timescale:1e3};let a={};if(ot(e)){if(a=function(e){let t=new Uint8Array(e),r=null,i=0,n=0,o=0,s=null;if(i=n=t[0]>>>3,o=(7&t[0])<<1|t[1]>>>7,o<0||o>=ut.length)return void console.error("Flv: AAC invalid sampling frequency index!");let a=ut[o],d=(120&t[1])>>>3;if(d<0||d>=8)return void console.log("Flv: AAC invalid channel configuration");5===i&&(s=(7&t[1])<<1|t[2]>>>7,t[2]);let l=self.navigator.userAgent.toLowerCase();return-1!==l.indexOf("firefox")?o>=6?(i=5,r=new Array(4),s=o-3):(i=2,r=new Array(2),s=o):-1!==l.indexOf("android")?(i=2,r=new Array(2),s=o):(i=5,s=o,r=new Array(4),o>=6?s=o-3:1===d&&(i=2,r=new Array(2),s=o)),r[0]=i<<3,r[0]|=(15&o)>>>1,r[1]=(15&o)<<7,r[1]|=(15&d)<<3,5===i&&(r[1]|=(15&s)>>>1,r[2]=(1&s)<<7,r[2]|=8,r[3]=0),{audioType:"aac",config:r,sampleRate:a,channelCount:d,objectType:i,codec:"mp4a.40."+i,originalCodec:"mp4a.40."+n}}(e.slice(2)),!a)return!1;s.audioSampleRate=a.sampleRate,s.channelCount=a.channelCount,s.config=a.config,s.refSampleDuration=1024/s.audioSampleRate*s.timescale}else{if(!n)return!1;if(a=function(e){if(e.length<4)return void console.error("Invalid MP3 packet, header missing!");let t=new Uint8Array(e.buffer),r=null;if(255!==t[0])return void console.error("Invalid MP3 packet, first byte != 0xFF ");let i=t[1]>>>3&3,n=(6&t[1])>>1,o=(240&t[2])>>>4,s=(12&t[2])>>>2,a=3!=(t[3]>>>6&3)?2:1,d=0,l=0;switch(i){case 0:d=li[s];break;case 2:d=di[s];break;case 3:d=ai[s]}switch(n){case 1:o<fi.length&&(l=fi[o]);break;case 2:o<ci.length&&(l=ci[o]);break;case 3:o<ui.length&&(l=ui[o])}return r={bitRate:l,samplingRate:d,channelCount:a,codec:"mp3",originalCodec:"mp3",audioType:"mp3"},r}(e),!a)return!1;s.audioSampleRate=a.samplingRate,s.channelCount=a.channelCount,s.refSampleDuration=1152/s.audioSampleRate*s.timescale}s.codec=a.codec,s.duration=0;let d="mp4",l=a.codec,u=null;n&&Tt(function(){const e=window.navigator.userAgent.toLowerCase();return/firefox/i.test(e)}())?(d="mpeg",l="",u=new Uint8Array):u=si.generateInitSegment(s);let c=`${s.type}/${d}`;return l&&l.length>0&&(c+=`;codecs=${l}`),Tt(_r.isAudioInitInfo)&&(dr=r===Ce.AAC?i?16:8:0===i?8:16,postMessage({cmd:B,code:r}),postMessage({cmd:x,sampleRate:s.audioSampleRate,channels:s.channelCount,depth:dr}),_r.isAudioInitInfo=!0),_r.audioMimeType=c,_r.isAAC=o,_r._initAudioSourceBuffer(),_r.appendAudioBuffer(u.buffer),!0},_initSourceBuffer(){const{proxy:e}=_r.events;if(null===_r.sourceBuffer&&null!==_r.mediaSource&&_r.isStateOpen()&&_r.videoMimeType){try{_r.sourceBuffer=_r.mediaSource.addSourceBuffer(_r.videoMimeType),gr.debug.log(_r.TAG_NAME,"_initSourceBuffer() mseDecoder.mediaSource.addSourceBuffer()",_r.videoMimeType)}catch(e){return gr.debug.error(_r.TAG_NAME,"appendBuffer() mseDecoder.mediaSource.addSourceBuffer()",e.code,e),_r.emitError(Ae.mseAddSourceBufferError,e.code),void(_r.mediaSourceAddSourceBufferError=!0)}if(_r.sourceBuffer){const t=e(_r.sourceBuffer,"error",(e=>{_r.mediaSourceBufferError=!0,gr.debug.error(_r.TAG_NAME,"mseSourceBufferError mseDecoder.sourceBuffer",e),_r.emitError(Ae.mseSourceBufferError,e.code)})),r=e(_r.sourceBuffer,"updateend",(()=>{_r._hasPendingRemoveRanges()?_r._doRemoveRanges():_r._hasPendingSegments()?_r._doAppendSegments():_r.hasPendingEos&&(gr.debug.log(_r.TAG_NAME,"videoSourceBuffer updateend and hasPendingEos is true, next endOfStream()"),_r.endOfStream())}));_r.eventListenList.push(t,r)}}else gr.debug.log(_r.TAG_NAME,`_initSourceBuffer and mseDecoder.isStateOpen is ${_r.isStateOpen()} and mseDecoder.isAvc === null is ${null===_r.isAvc}`)},_initAudioSourceBuffer(){const{proxy:e}=_r.events;if(null===_r.audioSourceBuffer&&null!==_r.mediaSource&&_r.isStateOpen()&&_r.audioMimeType){try{_r.audioSourceBuffer=_r.mediaSource.addSourceBuffer(_r.audioMimeType),_r._clearAudioSourceBufferCheckTimeout(),gr.debug.log(_r.TAG_NAME,"_initAudioSourceBuffer() mseDecoder.mediaSource.addSourceBuffer()",_r.audioMimeType)}catch(e){return gr.debug.error(_r.TAG_NAME,"appendAudioBuffer() mseDecoder.mediaSource.addSourceBuffer()",e.code,e),_r.emitError(Ae.mseAddSourceBufferError,e.code),void(_r.mediaSourceAddSourceBufferError=!0)}if(_r.audioSourceBuffer){const t=e(_r.audioSourceBuffer,"error",(e=>{_r.mediaSourceBufferError=!0,gr.debug.error(_r.TAG_NAME,"mseSourceBufferError mseDecoder.audioSourceBuffer",e),_r.emitError(Ae.mseSourceBufferError,e.code)})),r=e(_r.audioSourceBuffer,"updateend",(()=>{_r._hasPendingRemoveRanges()?_r._doRemoveRanges():_r._hasPendingSegments()?_r._doAppendSegments():_r.hasPendingEos&&(gr.debug.log(_r.TAG_NAME,"audioSourceBuffer updateend and hasPendingEos is true, next endOfStream()"),_r.endOfStream())}));_r.eventListenList.push(t,r),null===_r.audioSourceNoDataCheckTimeout&&(_r.audioSourceNoDataCheckTimeout=setTimeout((()=>{_r._clearAudioNoDataCheckTimeout(),_r.emitError(Ae.mediaSourceAudioNoDataTimeout)}),1e3))}}else gr.debug.log(_r.TAG_NAME,`_initAudioSourceBuffer and mseDecoder.isStateOpen is ${_r.isStateOpen()} and mseDecoder.audioMimeType is ${_r.audioMimeType}`)},_decodeVideo(e,t,r,i,n){let o=e.slice(5),s=o.byteLength;if(0===s)return void gr.debug.warn(_r.TAG_NAME,"_decodeVideo payload bytes is 0 and return");let a=(new Date).getTime(),d=!1;_r.prevTimestamp||(_r.prevTimestamp=a,d=!0);const l=a-_r.prevTimestamp;if(_r.decodeDiffTimestamp=l,l>500&&!d&&gr.isPlayer&&gr.debug.warn(_r.TAG_NAME,`_decodeVideo now time is ${a} and prev time is ${_r.prevTimestamp}, diff time is ${l} ms`),_r.cacheTrack.id&&t>=_r.cacheTrack.dts){let e=8+_r.cacheTrack.size,r=new Uint8Array(e);r[0]=e>>>24&255,r[1]=e>>>16&255,r[2]=e>>>8&255,r[3]=255&e,r.set(si.types.mdat,4),r.set(_r.cacheTrack.data,8),_r.cacheTrack.duration=t-_r.cacheTrack.dts;let i=si.moof(_r.cacheTrack,_r.cacheTrack.dts);_r.cacheTrack={};let n=new Uint8Array(i.byteLength+r.byteLength);n.set(i,0),n.set(r,i.byteLength),_r.appendBuffer(n.buffer)}else gr.debug.log(_r.TAG_NAME,`timeInit set false , cacheTrack = {} now dts is ${t}, and ts is ${n} cacheTrack dts is ${_r.cacheTrack&&_r.cacheTrack.dts}`),_r.timeInit=!1,_r.cacheTrack={};_r.cacheTrack||(_r.cacheTrack={}),_r.cacheTrack.id=Xe,_r.cacheTrack.sequenceNumber=++_r.sequenceNumber,_r.cacheTrack.size=s,_r.cacheTrack.dts=t,_r.cacheTrack.cts=i,_r.cacheTrack.isKeyframe=r,_r.cacheTrack.data=o,_r.cacheTrack.flags={isLeading:0,dependsOn:r?2:1,isDependedOn:r?1:0,hasRedundancy:0,isNonSync:r?0:1},_r.prevTimestamp=(new Date).getTime()},_decodeAudio(e,t,r){let i=_r.isAAC?e.slice(2):e.slice(1),n=i.byteLength;if(_r.cacheAudioTrack.id&&t>=_r.cacheAudioTrack.dts){let e=8+_r.cacheAudioTrack.size,r=new Uint8Array(e);r[0]=e>>>24&255,r[1]=e>>>16&255,r[2]=e>>>8&255,r[3]=255&e,r.set(si.types.mdat,4),r.set(_r.cacheAudioTrack.data,8),_r.cacheAudioTrack.duration=t-_r.cacheAudioTrack.dts;let i=si.moof(_r.cacheAudioTrack,_r.cacheAudioTrack.dts);_r.cacheAudioTrack={};let n=new Uint8Array(i.byteLength+r.byteLength);n.set(i,0),n.set(r,i.byteLength),_r.appendAudioBuffer(n.buffer)}else _r.cacheAudioTrack={};_r.cacheAudioTrack||(_r.cacheAudioTrack={}),_r.cacheAudioTrack.id=Ze,_r.cacheAudioTrack.sequenceNumber=++_r.audioSequenceNumber,_r.cacheAudioTrack.size=n,_r.cacheAudioTrack.dts=t,_r.cacheAudioTrack.cts=0,_r.cacheAudioTrack.data=i,_r.cacheAudioTrack.flags={isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}},appendBuffer(e){gr.isDestroyed?gr.debug.warn(_r.TAG_NAME,"appendBuffer() player is destroyed"):_r.mediaSourceAddSourceBufferError?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceAddSourceBufferError is true"):_r.mediaSourceAppendBufferFull?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceAppendBufferFull is true"):_r.mediaSourceAppendBufferError?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceAppendBufferError is true"):_r.mediaSourceBufferError?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceBufferError is true"):(_r.pendingSegments.push(e),_r.sourceBuffer&&(gr._opt.mseAutoCleanupSourceBuffer&&_r._needCleanupSourceBuffer()&&_r._doCleanUpSourceBuffer(),Tt(_r.getSourceBufferUpdating())&&_r.isStateOpen()&&Tt(_r._hasPendingRemoveRanges()))?_r._doAppendSegments():_r.isStateClosed()?(_r.mediaSourceBufferError=!0,_r.emitError(Ae.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed")):_r.isStateEnded()?(_r.mediaSourceBufferError=!0,_r.emitError(Ae.mseSourceBufferError,"mediaSource is end")):_r._hasPendingRemoveRanges()&&gr.debug.log(_r.TAG_NAME,`video has pending remove ranges and video length is ${_r.pendingRemoveRanges.length}, audio length is ${_r.pendingAudioRemoveRanges.length}`))},appendAudioBuffer(e){gr.isDestroyed?gr.debug.warn(_r.TAG_NAME,"appendAudioBuffer() player is destroyed"):_r.mediaSourceAddSourceBufferError?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceAddSourceBufferError is true"):_r.mediaSourceAppendBufferFull?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceAppendBufferFull is true"):_r.mediaSourceAppendBufferError?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceAppendBufferError is true"):_r.mediaSourceBufferError?gr.debug.warn(_r.TAG_NAME,"mseDecoder.mediaSourceBufferError is true"):(_r.pendingAudioSegments.push(e),_r.audioSourceBuffer&&(gr._opt.mseAutoCleanupSourceBuffer&&_r._needCleanupSourceBuffer()&&_r._doCleanUpSourceBuffer(),Tt(_r.getAudioSourceBufferUpdating())&&_r.isStateOpen()&&Tt(_r._hasPendingRemoveRanges()))?_r._doAppendSegments():_r.isStateClosed()?(_r.mediaSourceBufferError=!0,_r.emitError(Ae.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed")):_r.isStateEnded()?(_r.mediaSourceBufferError=!0,_r.emitError(Ae.mseSourceBufferError,"mediaSource is end")):_r._hasPendingRemoveRanges()&&gr.debug.log(_r.TAG_NAME,`audio has pending remove ranges and video length is ${_r.pendingRemoveRanges.length}, audio length is ${_r.pendingAudioRemoveRanges.length}`))},getSourceBufferUpdating:()=>_r.sourceBuffer&&_r.sourceBuffer.updating,getAudioSourceBufferUpdating:()=>_r.audioSourceBuffer&&_r.audioSourceBuffer.updating,stop(){_r.abortSourceBuffer(),_r.removeSourceBuffer()},clearUpAllSourceBuffer(){if(_r.sourceBuffer){const e=_r.sourceBuffer.buffered;for(let t=0;t<e.length;t++){let r=e.start(t),i=e.end(t);_r.pendingRemoveRanges.push({start:r,end:i})}Tt(_r.getSourceBufferUpdating())&&_r._doRemoveRanges()}if(_r.audioSourceBuffer){const e=_r.audioSourceBuffer.buffered;for(let t=0;t<e.length;t++){let r=e.start(t),i=e.end(t);_r.pendingAudioRemoveRanges.push({start:r,end:i})}Tt(_r.getAudioSourceBufferUpdating())&&_r._doRemoveRanges()}},endOfStream(){if(_r.isStateOpen())if(_r.getSourceBufferUpdating()||_r.getAudioSourceBufferUpdating())gr.debug.log(_r.TAG_NAME,"endOfStream() has pending eos"),_r.hasPendingEos=!0;else{_r.hasPendingEos=!1;try{gr.debug.log(_r.TAG_NAME,"endOfStream()"),_r.mediaSource.endOfStream()}catch(e){gr.debug.warn(_r.TAG_NAME,"endOfStream() error",e)}}},abortSourceBuffer(){if(_r.isStateOpen){if(_r.sourceBuffer)try{gr.debug.log(_r.TAG_NAME,"abortSourceBuffer() abort sourceBuffer"),_r.sourceBuffer.abort()}catch(e){}if(_r.audioSourceBuffer)try{gr.debug.log(_r.TAG_NAME,"abortSourceBuffer() abort audioSourceBuffer"),_r.audioSourceBuffer.abort()}catch(e){}}_r.sourceBuffer=null,_r.audioSourceBuffer=null},removeSourceBuffer(){if(!_r.isStateClosed()&&_r.mediaSource){if(_r.sourceBuffer)try{gr.debug.log(_r.TAG_NAME,"removeSourceBuffer() sourceBuffer"),_r.mediaSource.removeSourceBuffer(_r.sourceBuffer)}catch(e){gr.debug.warn(_r.TAG_NAME,"removeSourceBuffer() sourceBuffer error",e)}if(_r.audioSourceBuffer)try{gr.debug.log(_r.TAG_NAME,"removeSourceBuffer() audioSourceBuffer"),_r.mediaSource.removeSourceBuffer(_r.audioSourceBuffer)}catch(e){gr.debug.warn(_r.TAG_NAME,"removeSourceBuffer() audioSourceBuffer error",e)}}},_hasPendingSegments:()=>_r.pendingSegments.length>0||_r.pendingAudioSegments.length>0,getPendingSegmentsLength:()=>_r.pendingSegments.length,_handleUpdatePlaybackRate(){},_doAppendSegments(){if(_r.isStateClosed()||_r.isStateEnded())gr.debug.log(_r.TAG_NAME,"_doAppendSegments() mediaSource is closed or ended and return");else if(null!==_r.sourceBuffer){if(_r.needInitAudio()&&null===_r.audioSourceBuffer)return gr.debug.log(_r.TAG_NAME,"_doAppendSegments() audioSourceBuffer is null and need init audio source buffer"),void(null===_r.audioSourceBufferCheckTimeout&&(_r.audioSourceBufferCheckTimeout=setTimeout((()=>{_r._clearAudioSourceBufferCheckTimeout(),_r.emitError(Ae.mediaSourceAudioInitTimeout)}),1e3)));if(Tt(_r.getSourceBufferUpdating())&&_r.pendingSegments.length>0){const e=_r.pendingSegments.shift();try{_r.sourceBuffer.appendBuffer(e)}catch(e){gr.debug.error(_r.TAG_NAME,"mseDecoder.sourceBuffer.appendBuffer()",e.code,e),22===e.code?(_r.stop(),_r.mediaSourceAppendBufferFull=!0,_r.emitError(Ae.mediaSourceFull)):11===e.code?(_r.stop(),_r.mediaSourceAppendBufferError=!0,_r.emitError(Ae.mediaSourceAppendBufferError)):(_r.stop(),_r.mediaSourceBufferError=!0,_r.emitError(Ae.mseSourceBufferError,e.code))}}if(Tt(_r.getAudioSourceBufferUpdating())&&_r.pendingAudioSegments.length>0){const e=_r.pendingAudioSegments.shift();try{_r.audioSourceBuffer.appendBuffer(e)}catch(e){gr.debug.error(_r.TAG_NAME,"mseDecoder.audioSourceBuffer.appendBuffer()",e.code,e),22===e.code?(_r.stop(),_r.mediaSourceAppendBufferFull=!0,_r.emitError(Ae.mediaSourceFull)):11===e.code?(_r.stop(),_r.mediaSourceAppendBufferError=!0,_r.emitError(Ae.mediaSourceAppendBufferError)):(_r.stop(),_r.mediaSourceBufferError=!0,_r.emitError(Ae.mseSourceBufferError,e.code))}}}else gr.debug.log(_r.TAG_NAME,"_doAppendSegments() sourceBuffer is null and wait init and return")},_doCleanUpSourceBuffer(){const e=_r.$video.currentTime;if(_r.sourceBuffer){const t=_r.sourceBuffer.buffered;let r=!1;for(let i=0;i<t.length;i++){let n=t.start(i),o=t.end(i);if(n<=e&&e<o+3){if(e-n>=gr._opt.mseAutoCleanupMaxBackwardDuration){r=!0;let t=e-gr._opt.mseAutoCleanupMinBackwardDuration;_r.pendingRemoveRanges.push({start:n,end:t})}}else o<e&&(r=!0,_r.pendingRemoveRanges.push({start:n,end:o}))}r&&Tt(_r.getSourceBufferUpdating())&&_r._doRemoveRanges()}if(_r.audioSourceBuffer){const t=_r.audioSourceBuffer.buffered;let r=!1;for(let i=0;i<t.length;i++){let n=t.start(i),o=t.end(i);if(n<=e&&e<o+3){if(e-n>=gr._opt.mseAutoCleanupMaxBackwardDuration){r=!0;let t=e-gr._opt.mseAutoCleanupMinBackwardDuration;_r.pendingAudioRemoveRanges.push({start:n,end:t})}}else o<e&&(r=!0,_r.pendingAudioRemoveRanges.push({start:n,end:o}))}r&&Tt(_r.getAudioSourceBufferUpdating())&&_r._doRemoveRanges()}},_hasPendingRemoveRanges:()=>_r.pendingRemoveRanges.length>0||_r.pendingAudioRemoveRanges.length>0,needInitAudio:()=>gr._opt.hasAudio&&gr._opt.mseDecodeAudio,_doRemoveRanges(){if(_r.sourceBuffer&&Tt(_r.getSourceBufferUpdating())){let e=_r.pendingRemoveRanges;for(;e.length&&Tt(_r.getSourceBufferUpdating());){let t=e.shift();try{_r.sourceBuffer.remove(t.start,t.end)}catch(e){gr.debug.warn(_r.TAG_NAME,"_doRemoveRanges() sourceBuffer error",e)}}}if(_r.audioSourceBuffer&&Tt(_r.getAudioSourceBufferUpdating())){let e=_r.pendingAudioRemoveRanges;for(;e.length&&Tt(_r.getAudioSourceBufferUpdating());){let t=e.shift();try{_r.audioSourceBuffer.remove(t.start,t.end)}catch(e){gr.debug.warn(_r.TAG_NAME,"_doRemoveRanges() audioSourceBuffer error",e)}}}},_getPlaybackRate(){},_needCleanupSourceBuffer(){if(Tt(gr._opt.mseAutoCleanupSourceBuffer))return!1;const e=_r.$video.currentTime;if(_r.sourceBuffer){let t=_r.sourceBuffer.buffered;if(t.length>=1&&e-t.start(0)>=gr._opt.mseAutoCleanupMaxBackwardDuration)return!0}if(_r.audioSourceBuffer){let t=_r.audioSourceBuffer.buffered;if(t.length>=1&&e-t.start(0)>=gr._opt.mseAutoCleanupMaxBackwardDuration)return!0}return!1},_clearAudioSourceBufferCheckTimeout(){_r.audioSourceBufferCheckTimeout&&(clearTimeout(_r.audioSourceBufferCheckTimeout),_r.audioSourceBufferCheckTimeout=null)},_clearAudioNoDataCheckTimeout(){_r.audioSourceNoDataCheckTimeout&&(clearTimeout(_r.audioSourceNoDataCheckTimeout),_r.audioSourceNoDataCheckTimeout=null)},getHandle:()=>_r.mediaSource.handle,emitError(e){postMessage({cmd:Q,value:e,msg:arguments.length>1&&void 0!==arguments[1]?arguments[1]:""})}});let gr={isPlayer:!0,isPlayback:!1,dropping:!1,isPushDropping:!1,isWorkerFetch:!1,isDestroyed:!1,isTimeWait:!1,fetchStatus:qe,_opt:ur(),mp3Demuxer:null,delay:-1,pushLatestDelay:-1,firstTimestamp:null,startTimestamp:null,preDelayTimestamp:null,stopId:null,streamFps:null,streamAudioFps:null,streamVideoFps:null,writableStream:null,networkDelay:0,webglObj:null,startStreamRateAndStatsInterval:function(){gr.stopStreamRateAndStatsInterval(),l=setInterval((()=>{d&&d(0);const e=JSON.stringify({demuxBufferDelay:gr.getVideoBufferLength(),audioDemuxBufferDelay:gr.getAudioBufferLength(),streamBufferByteLength:gr.getStreamBufferLength(),netBuf:gr.networkDelay||0,pushLatestDelay:gr.pushLatestDelay||0,latestDelay:gr.delay,isStreamTsMoreThanLocal:rt});postMessage({cmd:M,type:we,value:e})}),1e3)},stopStreamRateAndStatsInterval:function(){l&&(clearInterval(l),l=null)},useOffscreen:function(){return gr._opt.useOffscreen&&"undefined"!=typeof OffscreenCanvas},getDelay:function(e,t){if(!e||gr._opt.hasVideo&&!We)return-1;if(t===ee)return gr.delay;if(gr.preDelayTimestamp&&gr.preDelayTimestamp>e)return gr.preDelayTimestamp-e>1e3&&gr.debug.warn("worker",`getDelay() and preDelayTimestamp is ${gr.preDelayTimestamp} > timestamp is ${e} more than ${gr.preDelayTimestamp-e}ms and return ${gr.delay}`),gr.preDelayTimestamp=e,gr.delay;if(gr.firstTimestamp){if(e){const t=Date.now()-gr.startTimestamp,r=e-gr.firstTimestamp;t>=r?(rt=!1,gr.delay=t-r):(rt=!0,gr.delay=r-t)}}else gr.firstTimestamp=e,gr.startTimestamp=Date.now(),gr.delay=-1;return gr.preDelayTimestamp=e,gr.delay},getDelayNotUpdateDelay:function(e,t){if(!e||gr._opt.hasVideo&&!We)return-1;if(t===ee)return gr.pushLatestDelay;if(gr.preDelayTimestamp&&gr.preDelayTimestamp-e>1e3)return gr.debug.warn("worker",`getDelayNotUpdateDelay() and preDelayTimestamp is ${gr.preDelayTimestamp} > timestamp is ${e} more than ${gr.preDelayTimestamp-e}ms and return -1`),-1;if(gr.firstTimestamp){let t=-1;if(e){const r=Date.now()-gr.startTimestamp,i=e-gr.firstTimestamp;r>=i?(rt=!1,t=r-i):(rt=!0,t=i-r)}return t}return-1},resetDelay:function(){gr.firstTimestamp=null,gr.startTimestamp=null,gr.delay=-1,gr.dropping=!1},resetAllDelay:function(){gr.resetDelay(),gr.preDelayTimestamp=null},doDecode:function(e){gr._opt.isEmitSEI&&e.type===te&&gr.isWorkerFetch&&gr.findSei(e.payload,e.ts),gr.isPlayUseMSEAndDecoderInWorker()?e.type===ee?gr._opt.mseDecodeAudio?_r.decodeAudio(e.payload,e.ts):e.decoder.decode(e.payload,e.ts):e.type===te&&_r.decodeVideo(e.payload,e.ts,e.isIFrame,e.cts):gr._opt.useWCS&&gr.useOffscreen()&&e.type===te&&o.decode?o.decode(e.payload,e.ts,e.cts):e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},decodeNext(e){if(0===i.length)return;const t=e.ts,n=i[0],o=e.type===te&&At(e.payload);if(Tt(r))o&&(gr.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${n.type} ts is ${n.ts}\n                isVideoSqeHeader is ${o}`),i.shift(),gr.doDecode(n));else{const r=n.ts-t,s=n.type===ee&&e.type===te;(r<=20||s||o)&&(gr.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${n.type} ts is ${n.ts}\n                diff is ${r} and isVideoAndNextAudio is ${s} and isVideoSqeHeader is ${o}`),i.shift(),gr.doDecode(n))}},init:function(){gr.debug.log("worker","init and opt is",JSON.stringify(gr._opt));const e=gr._opt.playType===_,t=gr._opt.playType===g;if(fr.init(),gr.isPlayer=e,gr.isPlayback=t,gr.isPlayUseMSEAndDecoderInWorker()&&_r&&_r.init(),gr.isPlaybackCacheBeforeDecodeForFpsRender())gr.debug.log("worker","playback and playbackIsCacheBeforeDecodeForFpsRender is true");else{gr.debug.log("worker","setInterval()");const t=gr._opt.videoBuffer+gr._opt.videoBufferDelay,r=()=>{let r=null;if(i.length){if(gr.isPushDropping)return void gr.debug.warn("worker",`loop() isPushDropping is true and bufferList length is ${i.length}`);if(gr.dropping){for(r=i.shift(),gr.debug.warn("worker",`loop() dropBuffer is dropping and isIFrame ${r.isIFrame} and delay is ${gr.delay} and bufferlist is ${i.length}`);!r.isIFrame&&i.length;)r=i.shift();const e=gr.getDelayNotUpdateDelay(r.ts,r.type);r.isIFrame&&e<=gr.getNotDroppingDelayTs()&&(gr.debug.log("worker","loop() is dropping = false, is iFrame"),gr.dropping=!1,gr.doDecode(r),gr.decodeNext(r))}else if(gr.isPlayback||gr.isPlayUseMSE()||0===gr._opt.videoBuffer)for(;i.length;)r=i.shift(),gr.doDecode(r);else if(r=i[0],-1===gr.getDelay(r.ts,r.type))gr.debug.log("worker","loop() common dumex delay is -1 ,data.ts is",r.ts),i.shift(),gr.doDecode(r),gr.decodeNext(r);else if(gr.delay>t&&e)gr.hasIframeInBufferList()?(gr.debug.log("worker",`delay is ${gr.delay} > maxDelay ${t}, set dropping is true`),gr.resetAllDelay(),gr.dropping=!0,postMessage({cmd:N})):(i.shift(),gr.doDecode(r),gr.decodeNext(r));else for(;i.length;){if(r=i[0],!(gr.getDelay(r.ts,r.type)>gr._opt.videoBuffer)){gr.delay<0&&gr.debug.warn("worker",`loop() do not decode and delay is ${gr.delay}, bufferList is ${i.length}`);break}i.shift(),gr.doDecode(r)}}else-1!==gr.delay&&gr.debug.log("worker","loop() bufferList is empty and reset delay"),gr.resetAllDelay()};gr.stopId=setInterval((()=>{let e=(new Date).getTime();it||(it=e);const t=e-it;t>100&&gr.debug.warn("worker",`loop demux diff time is ${t}`),r(),it=(new Date).getTime()}),20)}if(Tt(gr._opt.checkFirstIFrame)&&(We=!0),gr.isPlayUseMSEAndDecoderInWorker()&&_r){const e=_r.getHandle();e&&postMessage({cmd:Z,mseHandle:e},[e])}},playbackCacheLoop:function(){gr.stopId&&(clearInterval(gr.stopId),gr.stopId=null);const e=()=>{let e=null;i.length&&(e=i.shift(),gr.doDecode(e))};e();const t=Math.ceil(1e3/(gr.streamFps*gr._opt.playbackRate));gr.debug.log("worker",`playbackCacheLoop fragDuration is ${t}, streamFps is ${gr.streamFps}, streamAudioFps is ${gr.streamAudioFps} ,streamVideoFps is ${gr.streamVideoFps} playbackRate is ${gr._opt.playbackRate}`),gr.stopId=setInterval(e,t)},close:function(){if(gr.debug.log("worker","close"),gr.isDestroyed=!0,cr(),!a||1!==a.readyState&&2!==a.readyState?a&&gr.debug.log("worker",`close() and socket.readyState is ${a.readyState}`):(lr=!0,a.close(1e3,"Client disconnecting")),a=null,gr.stopStreamRateAndStatsInterval(),gr.stopId&&(clearInterval(gr.stopId),gr.stopId=null),gr.mp3Demuxer&&(gr.mp3Demuxer.destroy(),gr.mp3Demuxer=null),gr.writableStream&&Tt(gr.writableStream.locked)&&gr.writableStream.close().catch((e=>{gr.debug.log("worker","close() and writableStream.close() error",e)})),gr.writableStream=null,yr)try{yr.clear&&yr.clear(),yr=null}catch(e){gr.debug.warn("worker","close() and audioDecoder.clear error",e)}if(vr)try{vr.clear&&vr.clear(),vr=null}catch(e){gr.debug.warn("worker","close() and videoDecoder.clear error",e)}d=null,it=null,rt=!1,o&&(o.reset&&o.reset(),o=null),_r&&(_r.destroy(),_r=null),gr.firstTimestamp=null,gr.startTimestamp=null,gr.networkDelay=0,gr.streamFps=null,gr.streamAudioFps=null,gr.streamVideoFps=null,gr.delay=-1,gr.pushLatestDelay=-1,gr.preDelayTimestamp=null,gr.dropping=!1,gr.isPushDropping=!1,gr.isPlayer=!0,gr.isPlayback=!1,gr.isWorkerFetch=!1,gr._opt=ur(),gr.webglObj&&(gr.webglObj.destroy(),gr.offscreenCanvas.removeEventListener("webglcontextlost",gr.onOffscreenCanvasWebglContextLost),gr.offscreenCanvas.removeEventListener("webglcontextrestored",gr.onOffscreenCanvasWebglContextRestored),gr.offscreenCanvas=null,gr.offscreenCanvasGL=null,gr.offscreenCanvasCtx=null),i=[],n=[],u=null,v=null,b=null,S=!1,$e=!1,We=!1,qt=!1,Yt=!1,Jt=!1,sr=null,ar=null,gt=[],kt=0,Ct=0,at=null,dt=null,It=null,Vt=null,dr=null,Wt=0,jt=0,xt=null,Bt=null,gr.fetchStatus=qe,fr.destroy(),hr.destroy(),pr.destroy(),mr.destroy(),postMessage({cmd:H})},pushBuffer:function(e,t){if(t.type===ee&&ot(e)){if(gr.debug.log("worker",`pushBuffer audio ts is ${t.ts}, isAacCodecPacket is true`),gr._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:q,buffer:t},[t.buffer])}gr.decodeAudio(e,t.ts)}else if(t.type===te&&t.isIFrame&&At(e)){if(gr.debug.log("worker",`pushBuffer video ts is ${t.ts}, isVideoSequenceHeader is true`),gr._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:Y,buffer:t},[t.buffer])}gr.decodeVideo(e,t.ts,t.isIFrame,t.cts)}else{if(gr._opt.isRecording)if(gr._opt.isRecordTypeFlv){const r=new Uint8Array(e);postMessage({cmd:K,type:t.type,buffer:r,ts:t.ts},[r.buffer])}else if(gr._opt.recordType===y)if(t.type===te){const r=new Uint8Array(e).slice(5);postMessage({cmd:F,buffer:r,isIFrame:t.isIFrame,ts:t.ts,cts:t.cts},[r.buffer])}else if(t.type===ee&&gr._opt.isWasmMp4){const r=new Uint8Array(e),i=st(r)?r.slice(2):r.slice(1);postMessage({cmd:k,buffer:i,ts:t.ts},[i.buffer])}if(gr.isPlayer&&Wt>0&&Vt>0&&t.type===te){const e=t.ts-Vt,r=Wt+Wt/2;e>r&&gr.debug.log("worker",`pushBuffer video\n                    ts is ${t.ts}, preTimestamp is ${Vt},\n                    diff is ${e} and preTimestampDuration is ${Wt} and maxDiff is ${r}\n                    maybe trigger black screen or flower screen\n                    `)}if(gr.isPlayer&&Vt>0&&t.type===te&&t.ts<Vt&&Vt-t.ts>w&&(gr.debug.warn("worker",`pushBuffer,\n                preTimestamp is ${Vt}, options.ts is ${t.ts},\n                diff is ${Vt-t.ts} more than 3600000,\n                and resetAllDelay`),gr.resetAllDelay(),Vt=null,Wt=0),gr.isPlayer&&Vt>0&&t.ts<=Vt&&t.type===te&&(gr.debug.warn("worker",`pushBuffer() and isIFrame is ${t.isIFrame} and,\n                options.ts is ${t.ts} less than (or equal) preTimestamp is ${Vt} and\n                payloadBufferSize is ${e.byteLength} and prevPayloadBufferSize is ${jt}`),gr._opt.isDropSameTimestampGop&&Tt(t.isIFrame)&&We)){const e=gr.hasIframeInBufferList(),t=Tt(gr.isPushDropping);return gr.debug.log("worker",`pushBuffer, isDropSameTimestampGop is true and\n                    hasIframe is ${e} and isNotPushDropping is ${t} and next dropBuffer`),void(e&&t?gr.dropBuffer$2():(gr.clearBuffer(!0),Ut(gr._opt.checkFirstIFrame)&&Ut(r)&&(gr.isPlayUseMSEAndDecoderInWorker()?_r.isDecodeFirstIIframe=!1:postMessage({cmd:X}))))}if(gr.isPlayer&&We){const e=gr._opt.videoBuffer+gr._opt.videoBufferDelay,r=gr.getDelayNotUpdateDelay(t.ts,t.type);gr.pushLatestDelay=r,r>e&&gr.delay<e&&gr.delay>0&&gr.hasIframeInBufferList()&&!1===gr.isPushDropping&&(gr.debug.log("worker",`pushBuffer(), pushLatestDelay is ${r} more than ${e} and decoder.delay is ${gr.delay} and has iIframe and next decoder.dropBuffer$2()`),gr.dropBuffer$2())}if(gr.isPlayer&&t.type===te&&(Vt>0&&(Wt=t.ts-Vt),jt=e.byteLength,Vt=t.ts),t.type===ee?i.push({ts:t.ts,payload:e,decoder:{decode:gr.decodeAudio},type:ee,isIFrame:!1}):t.type===te&&i.push({ts:t.ts,cts:t.cts,payload:e,decoder:{decode:gr.decodeVideo},type:te,isIFrame:t.isIFrame}),gr.isPlaybackCacheBeforeDecodeForFpsRender()&&(vt(gr.streamVideoFps)||vt(gr.streamAudioFps))){let e=gr.streamVideoFps,t=gr.streamAudioFps;if(vt(gr.streamVideoFps)&&(e=St(i,te),e&&(gr.streamVideoFps=e,postMessage({cmd:z,value:gr.streamVideoFps}),gr.streamFps=t?e+t:e,Tt(gr._opt.hasAudio)&&(gr.debug.log("worker","playbackCacheBeforeDecodeForFpsRender, _opt.hasAudio is false and set streamAudioFps is 0"),gr.streamAudioFps=0),gr.playbackCacheLoop())),vt(gr.streamAudioFps)&&(t=St(i,ee),t&&(gr.streamAudioFps=t,gr.streamFps=e?e+t:t,gr.playbackCacheLoop())),vt(gr.streamVideoFps)&&vt(gr.streamAudioFps)){const r=i.map((e=>({type:e.type,ts:e.ts})));gr.debug.log("worker",`playbackCacheBeforeDecodeForFpsRender, calc streamAudioFps is ${t}, streamVideoFps is ${e}, bufferListLength  is ${i.length}, and ts list is ${JSON.stringify(r)}`)}const r=gr.getAudioBufferLength()>0,n=r?60:40;i.length>=n&&(gr.debug.warn("worker",`playbackCacheBeforeDecodeForFpsRender, bufferListLength  is ${i.length} more than ${n}, and hasAudio is ${r} an set streamFps is 25`),gr.streamVideoFps=25,postMessage({cmd:z,value:gr.streamVideoFps}),r?(gr.streamAudioFps=25,gr.streamFps=gr.streamVideoFps+gr.streamAudioFps):gr.streamFps=gr.streamVideoFps,gr.playbackCacheLoop())}}},getVideoBufferLength(){let e=0;return i.forEach((t=>{t.type===te&&(e+=1)})),e},hasIframeInBufferList:()=>i.some((e=>e.type===te&&e.isIFrame)),isAllIframeInBufferList(){const e=gr.getVideoBufferLength();let t=0;return i.forEach((e=>{e.type===te&&e.isIFrame&&(t+=1)})),e===t},getNotDroppingDelayTs:()=>gr._opt.videoBuffer+gr._opt.videoBufferDelay/2,getAudioBufferLength(){let e=0;return i.forEach((t=>{t.type===ee&&(e+=1)})),e},getStreamBufferLength(){let e=0;return u&&u.buffer&&(e=u.buffer.byteLength),gr._opt.isNakedFlow?fr.lastBuf&&(e=fr.lastBuf.byteLength):gr._opt.isTs?mr._remainingPacketData&&(e=mr._remainingPacketData.byteLength):gr._opt.isFmp4&&hr.mp4Box&&(e=hr.mp4Box.getAllocatedSampleDataSize()),e},fetchStream:function(e,t){gr.debug.log("worker","fetchStream, url is "+e,"options:",JSON.stringify(t)),gr.isWorkerFetch=!0,t.isFlv?gr._opt.isFlv=!0:t.isFmp4?gr._opt.isFmp4=!0:t.isMpeg4?gr._opt.isMpeg4=!0:t.isNakedFlow?gr._opt.isNakedFlow=!0:t.isTs&&(gr._opt.isTs=!0),d=yt((e=>{postMessage({cmd:M,type:_e,value:e})})),gr.startStreamRateAndStatsInterval(),t.isFmp4&&(hr.listenMp4Box(),gr._opt.isFmp4Private&&hr.initTransportDescarmber()),t.protocol===h?(u=new Dt(gr.demuxFlv()),fetch(e).then((e=>{if(Ut(lr))return gr.debug.log("worker","request abort and run res.body.cancel()"),gr.fetchStatus=qe,void e.body.cancel();if(!function(e){return e.ok&&e.status>=200&&e.status<=299}(e))return gr.debug.warn("worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),cr(),void postMessage({cmd:M,type:Ae.fetchError,value:`fetch response status is ${e.status} and ok is ${e.ok}`});if(postMessage({cmd:M,type:be}),"undefined"!=typeof WritableStream)gr.writableStream=new WritableStream({write:e=>s&&s.signal&&s.signal.aborted?(gr.debug.log("worker","writableStream write() and abortController.signal.aborted is true so return"),void(gr.fetchStatus=Ke)):Ut(lr)?(gr.debug.log("worker","writableStream write() and requestAbort is true so return"),void(gr.fetchStatus=Ke)):(gr.fetchStatus=Ye,d(e.byteLength),void(t.isFlv?u.write(e):t.isFmp4?gr.demuxFmp4(e):t.isMpeg4?gr.demuxMpeg4(e):t.isTs&&gr.demuxTs(e))),close:()=>{gr.debug.log("worker","writableStream close()"),gr.fetchStatus=Ke,u=null,cr(),postMessage({cmd:M,type:me,value:p,msg:"fetch done"})},abort:e=>{if(s&&s.signal&&s.signal.aborted)return gr.debug.log("worker","writableStream abort() and abortController.signal.aborted is true so return"),void(gr.fetchStatus=Ke);u=null,e.name!==He?(gr.debug.log("worker",`writableStream abort() and e is ${e.toString()}`),cr(),postMessage({cmd:M,type:Ae.fetchError,value:e.toString()})):gr.debug.log("worker","writableStream abort() and e.name is AbortError so return")}}),e.body.pipeTo(gr.writableStream);else{const r=e.body.getReader(),i=()=>{r.read().then((e=>{let{done:r,value:n}=e;return r?(gr.debug.log("worker","fetchNext().then() and done is true"),gr.fetchStatus=Ke,u=null,cr(),void postMessage({cmd:M,type:me,value:p,msg:"fetch done"})):s&&s.signal&&s.signal.aborted?(gr.debug.log("worker","fetchNext().then() and abortController.signal.aborted is true so return"),void(gr.fetchStatus=Ke)):Ut(lr)?(gr.debug.log("worker","fetchNext().then() and requestAbort is true so return"),void(gr.fetchStatus=Ke)):(gr.fetchStatus=Ye,d(n.byteLength),t.isFlv?u.write(n):t.isFmp4?gr.demuxFmp4(n):t.isMpeg4&&gr.demuxMpeg4(n),void i())})).catch((e=>{if(s&&s.signal&&s.signal.aborted)return gr.debug.log("worker","fetchNext().catch() and abortController.signal.aborted is true so return"),void(gr.fetchStatus=Ke);u=null,e.name!==He?(gr.debug.log("worker",`fetchNext().catch() and e is ${e.toString()}`),cr(),postMessage({cmd:M,type:Ae.fetchError,value:e.toString()})):gr.debug.log("worker","fetchNext().catch() and e.name is AbortError so return")}))};i()}})).catch((e=>{s&&s.signal&&s.signal.aborted?gr.debug.log("worker","fetch().catch() and abortController.signal.aborted is true so return"):e.name!==He?(gr.debug.log("worker",`fetch().catch() and e is ${e.toString()}`),cr(),postMessage({cmd:M,type:Ae.fetchError,value:e.toString()}),u=null):gr.debug.log("worker","fetch().catch() and e.name is AbortError so return")}))):t.protocol===f&&(t.isFlv&&(u=new Dt(gr.demuxFlv())),a=new WebSocket(e),a.binaryType="arraybuffer",a.onopen=()=>{gr.debug.log("worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:M,type:be}),postMessage({cmd:M,type:Ee})},a.onclose=e=>{gr.debug.log("worker",`fetchStream, WebsocketStream socket close and code is ${e.code}`),1006===e.code&&gr.debug.error("worker",`fetchStream, WebsocketStream socket close abnormally and code is ${e.code}`),Ut(lr)?gr.debug.log("worker","fetchStream, WebsocketStream socket close and requestAbort is true so return"):(u=null,postMessage({cmd:M,type:me,value:m,msg:e.code}))},a.onerror=e=>{gr.debug.error("worker","fetchStream, WebsocketStream socket error",e),u=null,postMessage({cmd:M,type:Ae.websocketError,value:e.isTrusted?"websocket user aborted":"websocket error"})},a.onmessage=e=>{d(e.data.byteLength),t.isFlv?u.write(e.data):t.isFmp4?gr.demuxFmp4(e.data):t.isMpeg4?gr.demuxMpeg4(e.data):gr._opt.isNakedFlow?gr.demuxNakedFlow(e.data):gr.demuxM7s(e.data)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),r=new Uint32Array(e);for(;;){t[3]=0;const e=yield 15,i=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];const n=r[0];t[0]=e[10],t[1]=e[9],t[2]=e[8],t[3]=e[11];let o=r[0];const s=(yield n).slice();switch(i){case re:if(s.byteLength>0){let e=s;Ut(gr._opt.m7sCryptoAudio)&&(e=gr.cryptoPayloadAudio(s)),gr.decode(e,{type:ee,ts:o})}else gr.debug.warn("worker",`demuxFlv() type is audio and payload.byteLength is ${s.byteLength} and return`);break;case ie:if(s.byteLength>=6){const e=s[0];if(gr._isEnhancedH265Header(e))gr._decodeEnhancedH265Video(s,o);else{s[0];let e=s[0]>>4===je;if(e&&At(s)&&null===sr){const e=15&s[0];sr=e===xe,ar=mt(s,sr),gr.debug.log("worker",`demuxFlv() isVideoSequenceHeader is true and isHevc is ${sr} and nalUnitSize is ${ar}`)}e&&gr.calcIframeIntervalTimestamp(o),gr.isPlayer&&gr.calcNetworkDelay(o),r[0]=s[4],r[1]=s[3],r[2]=s[2],r[3]=0;let t=r[0],i=gr.cryptoPayload(s,e);gr.decode(i,{type:te,ts:o,isIFrame:e,cts:t})}}else gr.debug.warn("worker",`demuxFlv() type is video and payload.byteLength is ${s.byteLength} and return`);break;case ne:postMessage({cmd:j,buffer:s},[s.buffer]);break;default:gr.debug.log("worker",`demuxFlv() type is ${i}`)}}},decode:function(e,t){t.type===ee?gr._opt.hasAudio&&(postMessage({cmd:M,type:ge,value:e.byteLength}),gr.isPlayer?gr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):gr.isPlayback&&(gr.isPlaybackOnlyDecodeIFrame()||(gr.isPlaybackCacheBeforeDecodeForFpsRender(),gr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts})))):t.type===te&&gr._opt.hasVideo&&(postMessage({cmd:M,type:ye,value:e.byteLength}),postMessage({cmd:M,type:ve,value:t.ts}),gr.isPlayer?gr.pushBuffer(e,{type:t.type,ts:t.ts,isIFrame:t.isIFrame,cts:t.cts}):gr.isPlayback&&(gr.isPlaybackOnlyDecodeIFrame()?t.isIFrame&&gr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}):(gr.isPlaybackCacheBeforeDecodeForFpsRender(),gr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}))))},cryptoPayload:function(e,t){let r=e;return gr._opt.isM7sCrypto?gr._opt.cryptoIV&&gr._opt.cryptoIV.byteLength>0&&gr._opt.cryptoKey&&gr._opt.cryptoKey.byteLength>0?r=function(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t=new Uint8Array(t),r=new Uint8Array(r);const n=e.byteLength;let o=5;for(;o<n;){let a=(s=e.slice(o,o+4))[3]|s[2]<<8|s[1]<<16|s[0]<<24;if(a>n)break;let d=e[o+4],l=!1;if(i?(d=d>>>1&63,l=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(d)):(d&=31,l=1===d||5===d),l){const i=e.slice(o+4+2,o+4+a);let n=new Nr.ModeOfOperation.ctr(t,new Nr.Counter(r));const s=n.decrypt(i);n=null,e.set(s,o+4+2)}o=o+4+a}var s;return e}(e,gr._opt.cryptoKey,gr._opt.cryptoIV,sr):gr.debug.error("worker",`isM7sCrypto cryptoKey.length is ${gr._opt.cryptoKey&&gr._opt.cryptoKey.byteLength} or cryptoIV.length is ${gr._opt.cryptoIV&&gr._opt.cryptoIV.byteLength} null`):gr._opt.isSm4Crypto?gr._opt.sm4CryptoKey&&t?r=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const i=e.byteLength;let n=5;for(;n<i;){let s=(o=e.slice(n,n+4))[3]|o[2]<<8|o[1]<<16|o[0]<<24;if(s>i)break;let a=e[n+4],d=!1;if(r?(a=a>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(a)):(a&=31,d=1===a||5===a),d){const r=ei(e.slice(n+4+2,n+4+s),t,0,{padding:"none",output:"array"});e.set(r,n+4+2)}n=n+4+s}var o;return e}(e,gr._opt.sm4CryptoKey):gr._opt.sm4CryptoKey||gr.debug.error("worker","isSm4Crypto opt.sm4CryptoKey is null"):gr._opt.isXorCrypto&&(gr._opt.cryptoIV&&gr._opt.cryptoIV.byteLength>0&&gr._opt.cryptoKey&&gr._opt.cryptoKey.byteLength>0?r=function(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const n=e.byteLength;let o=5;for(;o<n;){let a=(s=e.slice(o,o+4))[3]|s[2]<<8|s[1]<<16|s[0]<<24;if(a>n)break;let d=e[o+4],l=!1;if(i?(d=d>>>1&63,l=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(d)):(d&=31,l=1===d||5===d),l){const i=ni(e.slice(o+4,o+4+a),t,r);e.set(i,o+4)}o=o+4+a}var s;return e}(e,gr._opt.cryptoKey,gr._opt.cryptoIV,sr):gr.debug.error("worker",`isXorCrypto cryptoKey.length is ${gr._opt.cryptoKey&&gr._opt.cryptoKey.byteLength} or cryptoIV.length is ${gr._opt.cryptoIV&&gr._opt.cryptoIV.byteLength} null`)),r},cryptoPayloadAudio:function(e){let t=e;if(gr._opt.isM7sCrypto)if(gr._opt.cryptoIV&&gr._opt.cryptoIV.byteLength>0&&gr._opt.cryptoKey&&gr._opt.cryptoKey.byteLength>0){e[0]>>4===Ce.AAC&&(t=function(e,t,r){if(e.byteLength<=30)return e;const i=e.slice(32);let n=new Nr.ModeOfOperation.ctr(t,new Nr.Counter(r));const o=n.decrypt(i);return n=null,e.set(o,32),e}(e,gr._opt.cryptoKey,gr._opt.cryptoIV))}else gr.debug.error("worker",`isM7sCrypto cryptoKey.length is ${gr._opt.cryptoKey&&gr._opt.cryptoKey.byteLength} or cryptoIV.length is ${gr._opt.cryptoIV&&gr._opt.cryptoIV.byteLength} null`);return t},setCodecAudio:function(e,t){const r=e[0]>>4,i=e[0]>>1&1;if(dr=r===Ce.AAC?i?16:8:0===i?8:16,yr&&yr.setCodec)if(ot(e)||r===Ce.ALAW||r===Ce.MULAW||r===Ce.MP3){gr.debug.log("worker",`setCodecAudio: init audio codec, codeId is ${r}`);const i=r===Ce.AAC?e.slice(2):new Uint8Array(0);yr.setCodec(r,gr._opt.sampleRate,i),r===Ce.AAC&&postMessage({cmd:C,buffer:i},[i.buffer]),$e=!0,r!==Ce.AAC&&(r===Ce.MP3?(gr.mp3Demuxer||(gr.mp3Demuxer=new ii(gr),gr.mp3Demuxer.on("data",((e,t)=>{yr.decode(e,t)}))),gr.mp3Demuxer.dispatch(e.slice(1),t)):yr.decode(e.slice(1),t))}else gr.debug.warn("worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",r);else gr.debug.error("worker","setCodecAudio: audioDecoder or audioDecoder.setCodec is null")},decodeAudio:function(e,t){if(gr.isDestroyed)gr.debug.log("worker","decodeAudio, decoder is destroyed and return");else if(gr.isPlayUseMSEAndDecoderInWorkerAndMseDecodeAudio())_r.decodeAudio(e,t);else if(Ut(r)&&Ut(gr._opt.mseDecodeAudio))postMessage({cmd:L,payload:e,ts:t,cts:t},[e.buffer]);else{const r=e[0]>>4;if($e){if(ot(e))return void gr.debug.log("worker","decodeAudio and has already initialized and payload is aac codec packet so drop this frame");r===Ce.MP3?gr.mp3Demuxer.dispatch(e.slice(1),t):yr.decode(r===Ce.AAC?e.slice(2):e.slice(1),t)}else gr.setCodecAudio(e,t)}},setCodecVideo:function(e){const t=15&e[0];if(vr&&vr.setCodec)if(At(e))if(t===Te||t===xe){gr.debug.log("worker",`setCodecVideo: init video codec , codecId is ${t}`);const r=e.slice(5);if(t===Te&&gr._opt.useSIMD){const e=Lt(r);if(e.codecWidth>4080||e.codecHeight>4080)return postMessage({cmd:$}),void gr.debug.warn("worker",`setCodecVideo: SIMD H264 decode video width is too large, width is ${e.codecWidth}, height is ${e.codecHeight}`)}const i=new Uint8Array(e);S=!0,vr.setCodec(t,r),postMessage({cmd:D,code:t}),postMessage({cmd:P,buffer:i,codecId:t},[i.buffer])}else gr.debug.warn("worker",`setCodecVideo: hasInitVideoCodec is false, codecId is ${t} is not H264 or H265`);else gr.debug.warn("worker",`decodeVideo: hasInitVideoCodec is false, codecId is ${t} and frameType is ${e[0]>>4} and packetType is ${e[1]}`);else gr.debug.error("worker","setCodecVideo: videoDecoder or videoDecoder.setCodec is null")},decodeVideo:function(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if(gr.isDestroyed)gr.debug.log("worker","decodeVideo, decoder is destroyed and return");else if(gr.isPlayUseMSEAndDecoderInWorker())_r.decodeVideo(e,t,i,n);else if(Ut(r))postMessage({cmd:I,payload:e,isIFrame:i,ts:t,cts:n,delay:gr.delay},[e.buffer]);else if(S)if(!We&&i&&(We=!0),We){if(i&&At(e)){const t=15&e[0];let r={};if(t===Te){r=Lt(e.slice(5))}else t===xe&&(r=Xt(e));r.codecWidth&&r.codecHeight&&v&&b&&(r.codecWidth!==v||r.codecHeight!==b)&&(gr.debug.warn("worker",`\n                            decodeVideo: video width or height is changed,\n                            old width is ${v}, old height is ${b},\n                            new width is ${r.codecWidth}, new height is ${r.codecHeight},\n                            and emit change event`),Yt=!0,postMessage({cmd:O}))}if(Yt)return void gr.debug.warn("worker","decodeVideo: video width or height is changed, and return");if(Jt)return void gr.debug.warn("worker","decodeVideo: simd decode error, and return");if(At(e))return void gr.debug.warn("worker","decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<12)return void gr.debug.warn("worker",`decodeVideo and payload is too small , payload length is ${e.byteLength}`);const r=e.slice(5);vr.decode(r,i?1:0,t)}else gr.debug.log("worker","decodeVideo first frame is not iFrame");else gr.setCodecVideo(e)},clearBuffer:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];gr.debug.log("worker",`clearBuffer,bufferList length is ${i.length}, need clear is ${e}`),e&&(i=[]),gr.isPlayer&&(gr.resetAllDelay(),Ut(gr._opt.checkFirstIFrame)&&(gr.dropping=!0,postMessage({cmd:N}))),Ut(gr._opt.checkFirstIFrame)&&Tt(r)&&(We=!1)},dropBuffer$2:function(){if(i.length>0){let e=i.findIndex((e=>Ut(e.isIFrame)&&e.type===te));if(gr.isAllIframeInBufferList())for(let t=0;t<i.length;t++){const r=i[t],n=gr.getDelayNotUpdateDelay(r.ts,r.type);if(n>=gr.getNotDroppingDelayTs()){gr.debug.log("worker",`dropBuffer$2() isAllIframeInBufferList() is true, and index is ${t} and tempDelay is ${n} and notDroppingDelayTs is ${gr.getNotDroppingDelayTs()}`),e=t;break}}if(e>=0){gr.isPushDropping=!0,postMessage({cmd:N});const t=i.length;i=i.slice(e);const r=i.shift();gr.resetAllDelay(),gr.getDelay(r.ts,r.type),gr.doDecode(r),gr.isPushDropping=!1,gr.debug.log("worker",`dropBuffer$2() iFrameIndex is ${e},and old bufferList length is ${t} ,new bufferList is ${i.length} and new delay is ${gr.delay} `)}else gr.isPushDropping=!1}0===i.length&&(gr.isPushDropping=!1)},demuxM7s:function(e){const t=new DataView(e),r=t.getUint32(1,!1),i=t.getUint8(0),n=new ArrayBuffer(4),o=new Uint32Array(n);switch(i){case ee:gr.decode(new Uint8Array(e,5),{type:ee,ts:r});break;case te:if(t.byteLength>=11){const i=new Uint8Array(e,5),n=i[0];if(gr._isEnhancedH265Header(n))gr._decodeEnhancedH265Video(i,r);else{const e=t.getUint8(5)>>4==1;if(e&&(gr.calcIframeIntervalTimestamp(r),At(i)&&null===sr)){const e=15&i[0];sr=e===xe}gr.isPlayer&&gr.calcNetworkDelay(r),o[0]=i[4],o[1]=i[3],o[2]=i[2],o[3]=0;let n=o[0],s=gr.cryptoPayload(i,e);gr.decode(s,{type:te,ts:r,isIFrame:e,cts:n})}}else gr.debug.warn("worker",`demuxM7s() type is video and arrayBuffer length is ${e.byteLength} and return`)}},demuxNakedFlow:function(e){fr.dispatch(e)},demuxFmp4:function(e){const t=new Uint8Array(e);hr.dispatch(t)},demuxMpeg4:function(e){pr.dispatch(e)},demuxTs:function(e){mr.dispatch(e)},_decodeEnhancedH265Video:function(e,t){const r=e[0],i=48&r,n=15&r,o=e.slice(1,5),s=new ArrayBuffer(4),a=new Uint32Array(s),d="a"==String.fromCharCode(o[0]);if(sr=Tt(d),n===Je){if(i===tt){const r=e.slice(5);if(d);else{const i=new Uint8Array(5+r.length);i.set([28,0,0,0,0],0),i.set(r,5),ar=mt(e,sr),gr.debug.log("worker",`demuxFlv() isVideoSequenceHeader(enhancedH265) is true and isHevc is ${sr} and nalUnitSize is ${ar}`),gr.decode(i,{type:te,ts:t,isIFrame:!0,cts:0})}}}else if(n===Qe){let r=e,n=0;const o=i===tt;if(o&&gr.calcIframeIntervalTimestamp(t),d);else{a[0]=e[4],a[1]=e[3],a[2]=e[2],a[3]=0,n=a[0];r=er(e.slice(8),o),r=gr.cryptoPayload(r,o),gr.decode(r,{type:te,ts:t,isIFrame:o,cts:n})}}else if(n===et){const r=i===tt;r&&gr.calcIframeIntervalTimestamp(t);let n=er(e.slice(5),r);n=gr.cryptoPayload(n,r),gr.decode(n,{type:te,ts:t,isIFrame:r,cts:0})}},_isEnhancedH265Header:function(e){return 128==(128&e)},findSei:function(e,t){let r=4;vt(ar)||(r=ar);const i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(e.length<4)return;const r=e.length,i=[];let n,o=0;for(;o+t<r;)if(n=ht(e,o),3===t&&(n>>>=8),o+=t,n){if(o+n>r)break;i.push(e.subarray(o,o+n)),o+=n}return i}(e.slice(5),r);i.forEach((e=>{const r=sr?e[0]>>>1&63:31&e[0];(sr&&(r===Pe.suffixSei||r===Pe.prefixSei)||Tt(sr)&&r===De.kSliceSEI)&&postMessage({cmd:W,buffer:e,ts:t},[e.buffer])}))},calcNetworkDelay:function(e){if(!(We&&e>0))return;null===at?(at=e,dt=_t()):e<at&&(gr.debug.warn("worker",`calcNetworkDelay, dts is ${e} less than bufferStartDts is ${at}`),at=e,dt=_t());const t=e-at,r=_t()-dt,i=r>t?r-t:0;gr.networkDelay=i,i>gr._opt.networkDelay&&gr._opt.playType===_&&(gr.debug.warn("worker",`calcNetworkDelay now dts:${e}, start dts is ${at} vs start is ${t},local diff is ${r} ,delay is ${i}`),postMessage({cmd:M,type:Se,value:i}))},calcIframeIntervalTimestamp:function(e){null===It?It=e:It<e&&(Bt=e-It,postMessage({cmd:R,value:Bt}),It=e)},canVisibilityDecodeNotDrop:function(){return gr._opt.visibility&&v*b<=2073600},isPlaybackCacheBeforeDecodeForFpsRender:function(){return gr.isPlayback&&gr._opt.playbackIsCacheBeforeDecodeForFpsRender},isPlaybackOnlyDecodeIFrame:function(){return gr._opt.playbackRate>=gr._opt.playbackForwardMaxRateDecodeIFrame},isPlayUseMSE:function(){return gr.isPlayer&&gr._opt.useMSE&&Ut(r)},isPlayUseMSEAndDecoderInWorker:function(){return gr.isPlayUseMSE()&&gr._opt.mseDecoderUseWorker},isPlayUseMSEAndDecoderInWorkerAndMseDecodeAudio:function(){return gr.isPlayUseMSEAndDecoderInWorker()&&gr._opt.mseDecodeAudio},playbackUpdatePlaybackRate:function(){gr.clearBuffer(!0)},onOffscreenCanvasWebglContextLost:function(e){gr.debug.error("worker","handleOffscreenCanvasWebglContextLost and next try to create webgl"),e.preventDefault(),qt=!0,gr.webglObj.destroy(),gr.webglObj=null,gr.offscreenCanvasGL=null,setTimeout((()=>{gr.offscreenCanvasGL=gr.offscreenCanvas.getContext("webgl"),gr.offscreenCanvasGL&&gr.offscreenCanvasGL.getContextAttributes().stencil?(gr.webglObj=c(gr.offscreenCanvasGL,gr._opt.openWebglAlignment),qt=!1):gr.debug.error("worker","handleOffscreenCanvasWebglContextLost, stencil is false")}),500)},onOffscreenCanvasWebglContextRestored:function(e){gr.debug.log("worker","handleOffscreenCanvasWebglContextRestored"),e.preventDefault()},videoInfo:function(e,t,r){postMessage({cmd:D,code:e}),postMessage({cmd:A,w:t,h:r}),v=t,b=r,gr.useOffscreen()&&(gr.offscreenCanvas=new OffscreenCanvas(t,r),gr.offscreenCanvasGL=gr.offscreenCanvas.getContext("webgl"),gr.webglObj=c(gr.offscreenCanvasGL,gr._opt.openWebglAlignment),gr.offscreenCanvas.addEventListener("webglcontextlost",gr.onOffscreenCanvasWebglContextLost,!1),gr.offscreenCanvas.addEventListener("webglcontextrestored",gr.onOffscreenCanvasWebglContextRestored,!1))},audioInfo:function(e,t,r){postMessage({cmd:B,code:e}),postMessage({cmd:x,sampleRate:t,channels:r,depth:dr}),Ct=r},yuvData:function(t,r){if(gr.isDestroyed)return void gr.debug.log("worker","yuvData, decoder is destroyed and return");const i=v*b*3/2;let n=e.HEAPU8.subarray(t,t+i),o=new Uint8Array(n);if(xt=null,gr.useOffscreen())try{if(qt)return;gr.webglObj.renderYUV(v,b,o);let e=gr.offscreenCanvas.transferToImageBitmap();postMessage({cmd:U,buffer:e,delay:gr.delay,ts:r},[e])}catch(e){gr.debug.error("worker","yuvData, transferToImageBitmap error is",e)}else postMessage({cmd:U,output:o,delay:gr.delay,ts:r},[o.buffer])},pcmData:function(e,r,i){if(gr.isDestroyed)return void gr.debug.log("worker","pcmData, decoder is destroyed and return");let o=r,s=[],a=0,d=gr._opt.audioBufferSize;for(let r=0;r<2;r++){let i=t.HEAPU32[(e>>2)+r]>>2;s[r]=t.HEAPF32.subarray(i,i+o)}if(kt){if(!(o>=(r=d-kt)))return kt+=o,n[0]=Float32Array.of(...n[0],...s[0]),void(2==Ct&&(n[1]=Float32Array.of(...n[1],...s[1])));gt[0]=Float32Array.of(...n[0],...s[0].subarray(0,r)),2==Ct&&(gt[1]=Float32Array.of(...n[1],...s[1].subarray(0,r))),postMessage({cmd:T,buffer:gt,ts:i},gt.map((e=>e.buffer))),a=r,o-=r}for(kt=o;kt>=d;kt-=d)gt[0]=s[0].slice(a,a+=d),2==Ct&&(gt[1]=s[1].slice(a-d,a)),postMessage({cmd:T,buffer:gt,ts:i},gt.map((e=>e.buffer)));kt&&(n[0]=s[0].slice(a),2==Ct&&(n[1]=s[1].slice(a)))},errorInfo:function(e){null===xt&&(xt=_t());const t=_t(),r=(i=Bt>0?2*Bt:5e3,n=1e3,o=5e3,Math.max(Math.min(i,Math.max(n,o)),Math.min(n,o)));var i,n,o;const s=t-xt;s>r&&(gr.debug.warn("worker",`errorInfo() emit simdDecodeError and\n                iframeIntervalTimestamp is ${Bt} and diff is ${s} and maxDiff is ${r}\n                and replay`),Jt=!0,postMessage({cmd:G}))},sendWebsocketMessage:function(e){a?a.readyState===Ue?a.send(e):gr.debug.error("worker","socket is not open"):gr.debug.error("worker","socket is null")},timeEnd:function(){},postStreamToMain(e,t){postMessage({cmd:V,type:t,buffer:e},[e.buffer])}};gr.debug=new Pt(gr);let yr=null;t.AudioDecoder&&(yr=new t.AudioDecoder(gr));let vr=null;e.VideoDecoder&&(vr=new e.VideoDecoder(gr)),postMessage({cmd:E}),self.onmessage=function(e){let t=e.data;if(!gr.isTimeWait)switch(t.cmd){case oe:try{gr._opt=Object.assign(gr._opt,JSON.parse(t.opt))}catch(e){}gr.init();break;case se:gr.pushBuffer(t.buffer,t.options);break;case ae:gr.decodeAudio(t.buffer,t.ts);break;case de:gr.decodeVideo(t.buffer,t.ts,t.isIFrame);break;case ce:gr.clearBuffer(t.needClear),gr.isTimeWait=!0,setTimeout((()=>{gr.isTimeWait=!1}),1e3);break;case fe:gr.fetchStream(t.url,JSON.parse(t.opt));break;case le:gr.close();break;case ue:gr.debug.log("worker","updateConfig",t.key,t.value),gr._opt[t.key]=t.value,"playbackRate"===t.key&&(gr.playbackUpdatePlaybackRate(),gr.isPlaybackCacheBeforeDecodeForFpsRender()&&gr.playbackCacheLoop());break;case he:gr.sendWebsocketMessage(t.message);break;case pe:_r.$video.currentTime=Number(t.message)}}}Date.now||(Date.now=function(){return(new Date).getTime()});const Di=[];Di.push(r({printErr:function(e){console.warn("EasyPro[❌❌❌][worker]:",e)}}),t({printErr:function(e){console.warn("EasyPro[❌❌❌][worker]:",e)}})),Promise.all(Di).then((e=>{const t=e[0];Ci(e[1],t)}))}));
