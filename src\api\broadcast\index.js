/**
 * Chrome SDK 接口统一入口
 * 基于Chrome SDK接口文档
 */
import chromeSDK from './chromeSDK.js';
import chromeSDKLogin from './login.js';
import groupUserManager from './groupUser.js';
import broadcastManager from './broadcast.js';
import deviceManager from './device.js';
import systemManager from './system.js';

/**
 * Chrome SDK 统一管理类
 */
class ChromeSDKManager {
  constructor() {
    this.sdk = chromeSDK;
    this.login = chromeSDKLogin;
    this.groupUser = groupUserManager;
    this.broadcast = broadcastManager;
    this.device = deviceManager;
    this.system = systemManager;
    this.dhWeb = null;
  }

  /**
   * 初始化Chrome SDK
   * @param {Object} dhWebInstance - DHAlarmWeb实例
   * @returns {ChromeSDKManager} 返回自身以支持链式调用
   */
  init(dhWebInstance) {
    this.dhWeb = dhWebInstance;
    this.sdk.init(dhWebInstance);
    return this;
  }

  /**
   * 获取Chrome SDK实例
   * @returns {Object} Chrome SDK实例
   */
  getSDK() {
    return this.sdk;
  }

  /**
   * 获取登录管理器
   * @returns {ChromeSDKLogin} 登录管理器
   */
  getLoginManager() {
    return this.login;
  }

  /**
   * 获取分组用户管理器
   * @returns {GroupUserManager} 分组用户管理器
   */
  getGroupUserManager() {
    return this.groupUser;
  }

  /**
   * 获取广播管理器
   * @returns {BroadcastManager} 广播管理器
   */
  getBroadcastManager() {
    return this.broadcast;
  }

  /**
   * 获取设备管理器
   * @returns {DeviceManager} 设备管理器
   */
  getDeviceManager() {
    return this.device;
  }

  /**
   * 获取系统管理器
   * @returns {SystemManager} 系统管理器
   */
  getSystemManager() {
    return this.system;
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return this.login.getLoginStatus();
  }

  /**
   * 获取登录句柄
   * @returns {number|null} 登录句柄
   */
  getLoginHandle() {
    return this.login.getLoginHandle();
  }

  /**
   * 设置全局回调函数
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    this.sdk.on(eventName, callback);
  }

  /**
   * 移除全局回调函数
   * @param {string} eventName - 事件名称
   */
  off(eventName) {
    this.sdk.off(eventName);
  }

  // ==================== 快捷方法 ====================

  /**
   * 快捷登录方法
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @param {string} serverAddr - 服务器地址
   * @returns {Promise} 登录结果
   */
  async quickLogin(username, password, serverAddr) {
    try {
      const result = await this.login.login(username, password, serverAddr);
      console.log('登录成功:', result);
      return result;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 快捷登出方法
   * @returns {Promise} 登出结果
   */
  async quickLogout() {
    try {
      await this.login.logout();
      console.log('登出成功');
    } catch (error) {
      console.error('登出失败:', error);
      throw error;
    }
  }

  /**
   * 快捷播放视频方法
   * @param {Object} video - 视频元素
   * @param {number} deviceId - 设备ID
   * @param {boolean} isTalk - 是否开启对讲
   * @returns {Promise} 播放结果
   */
  async quickPlayVideo(video, deviceId, isTalk = false) {
    try {
      await this.sdk.playRT(video, deviceId, isTalk);
      console.log('开始播放视频:', deviceId);
    } catch (error) {
      console.error('播放视频失败:', error);
      throw error;
    }
  }

  /**
   * 快捷停止视频方法
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 停止结果
   */
  async quickStopVideo(deviceId) {
    try {
      await this.sdk.stopRT(deviceId);
      console.log('停止播放视频:', deviceId);
    } catch (error) {
      console.error('停止视频失败:', error);
      throw error;
    }
  }

  /**
   * 快捷开始广播方法
   * @param {number[]} deviceIds - 设备ID数组
   * @param {number} broadcastType - 广播类型
   * @param {string} bcFilePath - 广播文件路径
   * @returns {Promise} 广播结果
   */
  async quickStartBroadcast(deviceIds, broadcastType, bcFilePath = '') {
    try {
      const result = await this.broadcast.startBroadcast(deviceIds, broadcastType, bcFilePath);
      console.log('开始广播:', result);
      return result;
    } catch (error) {
      console.error('开始广播失败:', error);
      throw error;
    }
  }

  /**
   * 快捷停止广播方法
   * @returns {Promise} 停止结果
   */
  async quickStopBroadcast() {
    try {
      await this.broadcast.stopBroadcast();
      console.log('停止广播成功');
    } catch (error) {
      console.error('停止广播失败:', error);
      throw error;
    }
  }

  /**
   * 快捷获取音频文件列表方法
   * @returns {Promise} 音频文件列表
   */
  async quickGetAudioFileList() {
    try {
      const result = await this.broadcast.getAudioFileList();
      console.log('获取音频文件列表成功:', result);
      return result;
    } catch (error) {
      console.error('获取音频文件列表失败:', error);
      throw error;
    }
  }

  /**
   * 快捷获取广播任务列表方法
   * @param {string} taskType - 任务类型
   * @returns {Promise} 广播任务列表
   */
  async quickGetBCTaskList(taskType = 'timed') {
    try {
      const result = await this.broadcast.getBCTaskList(taskType);
      console.log('获取广播任务列表成功:', result);
      return result;
    } catch (error) {
      console.error('获取广播任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 快捷获取设备额外信息方法
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 设备额外信息
   */
  async quickGetDeviceExtra(deviceId = 0) {
    try {
      const result = await this.device.getDeviceExtra(deviceId);
      console.log('获取设备额外信息成功:', result);
      return result;
    } catch (error) {
      console.error('获取设备额外信息失败:', error);
      throw error;
    }
  }

  // ==================== 常用常量定义 ====================

  /**
   * 广播类型常量
   */
  static BROADCAST_TYPE = {
    MIC: 1,    // 麦克风广播
    FILE: 2    // 文件广播
  };

  /**
   * 设备类型常量
   */
  static DEVICE_TYPE = {
    ALARM: 'Alarm',  // 报警设备
    IPC: 'Ipc'       // 联动设备
  };

  /**
   * 设备状态常量
   */
  static DEVICE_ACTION = {
    NORMAL: 'Normal',   // 设备在线或报警已被处理
    OFFLINE: 'Offline', // 设备离线
    START: 'Start',     // 设备正在呼叫
    DEALING: 'Dealing'  // 报警正在被处理
  };

  /**
   * 分组类型常量
   */
  static GROUP_TYPE = {
    NORMAL: 'normal',   // 普通分组
    LINKAGE: 'linkage', // 联动分组
    DEFENSE: 'defense'  // 联防分组
  };

  /**
   * 音量类型常量
   */
  static VOLUME_TYPE = {
    INPUT: 'input',   // 麦克风音量
    OUTPUT: 'output'  // 喇叭音量
  };

  /**
   * 广播状态常量
   */
  static BROADCAST_STATE = {
    PLAY: 'play',   // 播放
    PAUSE: 'pause'  // 暂停
  };
}

// 创建单例实例
const chromeSDKManager = new ChromeSDKManager();

// 导出单例实例和类
export default chromeSDKManager;
export { ChromeSDKManager };
