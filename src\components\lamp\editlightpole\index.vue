<template>
  <div class="container-bigbox">
    <!-- 选择设备页面 -->
    <div v-if="showSelect" style="width: 100%">
      <Select v-if="showSelect" @activItem="activItem" :selectData="selectData"></Select>
    </div>
    <!-- 选择设备后的设备详情页 -->
    <detail v-if="showDetail" :activItemData="activItemData"></detail>
    <!-- 提交设备数据页面 -->
    <detailfrom ref="detailfromRef" v-if="showDetailfrom" :activItemData="activItemData"></detailfrom>
    <!-- 配置灯杆表单页 -->
    <Deploy
      v-show="showDeploy"
      ref="deploy"
      :editdata="props.editdata"
      :activation="activation"
      :lampsLanterns="lampsLanterns"
      @coles="coles"
      @showSelectfun="showSelectfun"
    >
    </Deploy>
    <div class="btn-box">
      <el-button color="#165DFF" v-if="showDeploy" @click="DeploySbumit">提交</el-button>
      <el-button color="#165DFF" v-else @click="next">下一步</el-button>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, defineEmits, defineProps } from "vue";
import detail from "./detail.vue";
import Deploy from "./Deploy.vue";
import Select from "./select.vue";
import detailfrom from "./detailfrom.vue";
import { LampsLanternsType, update } from "@/api/lamp/index.js";

const emit = defineEmits(["handleClose"]);

const props = defineProps({
  editdata: {
    type: Object,
    default: () => ({}),
  },
});

const showSelect = ref(false);
const showDetail = ref(false);
const showDeploy = ref(true);
const showDetailfrom = ref(false);
const activation = ref(0);

const activItemData = ref({});
const activItem = (data) => {
  // activation.value = index;
  activItemData.value = data;
  console.log("data", data);
};

const next = () => {

 if(showSelect.value) {
    showDetail.value = true;
    showSelect.value = false;
    return
  } else if (showDetail.value) {
    showDetailfrom.value = true;
    showDetail.value = false;
    return
  }else if(showDetailfrom.value){
    detailfromSbumit()
    return
  }
};

const detailfromRef = ref(null);
const lampsLanterns = ref({});
const detailfromSbumit = async () => {
  const result = await detailfromRef.value.detailSubmitForm();
  console.log("result", result);
  if (result.success) {
    // 处理成功情况
    console.log('表单提交成功，字段信息：', result.data);
    lampsLanterns.value = result
    showDeploy.value = true;
    showDetailfrom.value = false;

  } else {
    // 处理失败情况
    console.log('表单提交失败，字段信息：', result.data);
  }
};


const deploy = ref(null);
const DeploySbumit = async () => {
  console.log("submit!", deploy.value);
  if (deploy.value) {
    const result = await deploy.value.submitForm();
    if (result.success) {
      // 处理成功情况
      console.log('表单提交成功，字段信息：', result.data);
      let row = {
        id: result.data.id,
        poleName: result.data.poleName,
        poleAddress: result.data.poleAddress,
        lng: result.data.lng,
        lat: result.data.lat,
        icon: result.data.icon,
        remark: result.data.remark,
        modelNo: result.data.modelNo,
        lampsLanterns: JSON.stringify(lampsLanterns.value?.data)
      }
      undetFun(row)
    } else {
      // 处理失败情况
      console.log('表单提交失败，字段信息：', result.data);

    }
  }
};

const undetFun = (row)=>{
  update(row).then(res=>{
    console.log(res,"res");
    coles()
  }).catch(err=>{
    console.log(err,"err");
  })
}

const coles = () => {
  showDeploy.value = false;
  showDetail.value = true;
  emit("handleClose");
};

const selectData = ref({});
const showSelectfun = (bol) => {
  console.log(bol,"点击了！！！");
  if(bol === 'den'){
    LampsLanternsType().then(res=>{
      console.log(res);
      selectData.value = res.data.data;
      showDeploy.value = false;
      showSelect.value = true;
    })
  }
};
</script>
<style lang="scss" scoped>
.container-bigbox {
  width: 100%;
}

.title {
  margin-bottom: 24px;
}

.ganti-box {
  width: 100%;
  height: 460px;
  display: flex;
  justify-content: space-between;
}

.btn-box {
  width: 100%;
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.ganti-item {
  width: 280px;
  height: 100%;
  background-color: #f2f2f2;
  overflow: hidden;
  cursor: pointer;
  .img-style {
    width: 100%;
    height: 100%;
  }
}

.activation {
  border: 1px solid #80ffff;
}
</style>
