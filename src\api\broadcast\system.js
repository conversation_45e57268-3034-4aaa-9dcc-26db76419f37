/**
 * Chrome SDK 系统管理相关接口
 * 基于Chrome SDK接口文档
 */
import chromeSDK from './chromeSDK.js';

/**
 * 系统管理类
 */
class SystemManager {
  constructor() {
    this.sdk = chromeSDK;
  }

  /**
   * 生成请求ID
   * @returns {number} 请求ID
   */
  generateRequestId() {
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  // ==================== 系统配置接口 ====================

  /**
   * 获取系统配置
   * @returns {Promise} 系统配置
   */
  getSystemCfg() {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetSystemCfg;
      this.sdk.callbacks.onGetSystemCfg = (result) => {
        this.sdk.callbacks.onGetSystemCfg = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getSystemCfg(requestId);
      } catch (error) {
        this.sdk.callbacks.onGetSystemCfg = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 设置系统配置
   * @param {Object} config - 系统配置
   * @returns {Promise} 操作结果
   */
  setSystemCfg(config) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSetSystemCfg;
      this.sdk.callbacks.onSetSystemCfg = (result) => {
        this.sdk.callbacks.onSetSystemCfg = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        const {
          serverName,
          serverPort,
          password,
          playrttimeout,
          alarmingtime,
          leveltime,
          recordEnable,
          recordRemainSpace,
          recordDisk
        } = config;

        this.sdk.dhWeb.setSystemCfg(
          requestId,
          serverName,
          serverPort,
          password,
          playrttimeout,
          alarmingtime,
          leveltime,
          recordEnable,
          recordRemainSpace,
          recordDisk
        );
      } catch (error) {
        this.sdk.callbacks.onSetSystemCfg = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 日志搜索接口 ====================

  /**
   * 搜索日志
   * @param {number} ulTimeBeg - 开始时间戳
   * @param {number} ulTimeEnd - 结束时间戳
   * @param {number} pageIndex - 页数index，0开始
   * @param {number} onePageItems - 一页显示的数量
   * @param {Object} option - 可选参数
   * @returns {Promise} 日志搜索结果
   */
  searchLog(ulTimeBeg, ulTimeEnd, pageIndex = 0, onePageItems = 20, option = {}) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSearchLog;
      this.sdk.callbacks.onSearchLog = (result) => {
        this.sdk.callbacks.onSearchLog = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.searchLog(requestId, ulTimeBeg, ulTimeEnd, pageIndex, onePageItems, option);
      } catch (error) {
        this.sdk.callbacks.onSearchLog = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 铃声规则管理接口 ====================

  /**
   * 获取设备来人提示音规则列表
   * @returns {Promise} 铃声规则列表
   */
  getTipAudioInfo() {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetTipAudioInfo;
      this.sdk.callbacks.onGetTipAudioInfo = (result) => {
        this.sdk.callbacks.onGetTipAudioInfo = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getTipAudioInfo(requestId);
      } catch (error) {
        this.sdk.callbacks.onGetTipAudioInfo = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 添加/修改规则列表
   * @param {number} ruleId - 规则ID，添加时传0
   * @param {number} ruleIndex - 规则index，添加时传0
   * @param {string} name - 规则名称
   * @param {number} trigSrc - 地感出发 0：否，1：是
   * @param {Array} timeSection - 时间段，数组类型，最多6个时间段
   * @returns {Promise} 操作结果
   */
  addOrModTipAudio(ruleId, ruleIndex, name, trigSrc, timeSection) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onAddOrModTipAudio;
      this.sdk.callbacks.onAddOrModTipAudio = (result) => {
        this.sdk.callbacks.onAddOrModTipAudio = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.addOrModTipAudio(requestId, ruleId, ruleIndex, name, trigSrc, timeSection);
      } catch (error) {
        this.sdk.callbacks.onAddOrModTipAudio = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 获取设备铃声规则
   * @param {number[]} cameraIds - 设备ID数组
   * @returns {Promise} 设备铃声规则
   */
  getDeviceTipAudioName(cameraIds) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetTipAudioName;
      this.sdk.callbacks.onGetTipAudioName = (result) => {
        this.sdk.callbacks.onGetTipAudioName = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getDeviceTipAudioName(requestId, cameraIds);
      } catch (error) {
        this.sdk.callbacks.onGetTipAudioName = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 文件上传和升级接口 ====================

  /**
   * 上传文件（升级文件、铃声文件）
   * @param {string} fileName - 文件名称，升级文件后缀为".image",铃声文件为".wav"
   * @param {ArrayBuffer} fileBuffer - 文件buffer
   * @returns {Promise} 操作结果
   */
  upLoadFirmware(fileName, fileBuffer) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      try {
        this.sdk.dhWeb.upLoadFirmware(requestId, fileName, fileBuffer);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 开始升级或导入
   * @param {string} fileName - 文件名称，需要与上传文件时fileName值相同
   * @param {number} deviceId - 设备ID
   * @param {number} flag - 升级标识，0:固件升级，1，按键提示音 2，来人提示音
   * @returns {Promise} 操作结果
   */
  startUpdate(fileName, deviceId, flag) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onStartUpdate;
      this.sdk.callbacks.onStartUpdate = (result) => {
        this.sdk.callbacks.onStartUpdate = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.startUpdate(requestId, fileName, deviceId, flag);
      } catch (error) {
        this.sdk.callbacks.onStartUpdate = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 停止升级
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 操作结果
   */
  stopUpdate(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onStopUpdate;
      this.sdk.callbacks.onStopUpdate = (result) => {
        this.sdk.callbacks.onStopUpdate = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.stopUpdate(requestId, deviceId);
      } catch (error) {
        this.sdk.callbacks.onStopUpdate = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 查询升级（导入）状态
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 升级状态
   */
  queryUpdateStatus(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onQueryUpdateStatus;
      this.sdk.callbacks.onQueryUpdateStatus = (result) => {
        this.sdk.callbacks.onQueryUpdateStatus = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.queryUpdateStatus(requestId, deviceId);
      } catch (error) {
        this.sdk.callbacks.onQueryUpdateStatus = originalCallback;
        reject(error);
      }
    });
  }
}

// 创建单例实例
const systemManager = new SystemManager();

export default systemManager;
