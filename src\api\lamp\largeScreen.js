import request from '@/axios';

// 获取集中控制器在线情况
export const getControllerOnline = () => {
    return request({
        url: '/hztech-light/largeScreenPost/centralizedStatusList',
        method: 'get'
    })
}

// 获取监控设备在线情况
export const getMonitorOnline = () => {
    return request({
        url: '/hztech-light/largeScreenPost/supervisoryControlStatusList',
        method: 'get'
    })
}

// 获取光环境设备在线情况
export const getLightOnline = () => {
    return request({
        url: '/hztech-light/largeScreenPost/environmentAcquisitionStatusList',
        method: 'get'
    })
}

// 获取气体传感器在线情况
export const getGasOnline = () => {
    return request({
        url: '/hztech-light/largeScreenPost/gasSensorStatusList',
        method: 'get'
    })
}


// 获取全部灯杆
export const getLampList = () => {
    return request({
        url: '/hztech-light/lightPole/list',
        method: 'get'
    })
}