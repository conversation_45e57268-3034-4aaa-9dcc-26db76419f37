<template>
  <div class="detail-container">
    <div class="title">杆体说明</div>
    <div class="content-wrapper">
      <div class="image-section">
        <div class="image-box">
          <img src="@/assets/lightpole.png" alt="灯杆示意图">
        </div>
      </div>
      <div class="text-section">
        <div class="section-item">
          <div class="section-title">
            <div class="title-bar"></div>
            <span>杆体功能</span>
          </div>
          <div class="section-content">
            杆体具有挂载照明设备、各类传感器、通信及安防设备并为之供电配电，实现信息传输与交互，同时提供防护与支撑以保障设备稳定运行的多功能特性。
          </div>
        </div>

        <div class="section-item">
          <div class="section-title">
            <div class="title-bar"></div>
            <span>杆体配件</span>
          </div>
          <div class="section-content">
            照明灯具、传感器、监控、广播、LED屏幕
          </div>
        </div>

        <div class="section-item">
          <div class="section-title">
            <div class="title-bar"></div>
            <span>配件说明</span>
          </div>
          <div class="section-content">
            照明灯具采用先进光学与智能调光，依光线智能变亮或变暗，散热佳、寿命长，保障道路照明与节能。传感器多样，含地磁传感器等，高灵敏、低功耗，无线传数据，助城市多方面决策。监控集高清摄像、夜视与智能识别，可远程操控，精准捕捉异常并告警，实现全方位监控。广播支持多格式，连应急中心，应急时传信息，平日发通知，促社区交流。LED 屏幕画质优，远程控内容，交通、天气、广告皆能显，利出行与商业，维智慧灯杆运营。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'

let props = defineProps({
    activation: {
        type: Number,
    },
});
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20px 20px 0;
  .title {
    margin-bottom: 14px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
  }
}

.content-wrapper {
  display: flex;
  gap: 24px;
}

.image-section {
  flex: 0 0 300px;
  
  .image-box {
    width: 100%;
    height: 453px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
}

.text-section {
  flex: 1;
  
  .section-item {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .title-bar {
      width: 4px;
      height: 16px;
      background-color: #1890ff;
      margin-right: 8px;
      border-radius: 2px;
    }
    
    span {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
  
  .section-content {
    font-size: 14px;
    line-height: 1.8;
    color: #666;
    text-align: justify;
  }
}
</style>