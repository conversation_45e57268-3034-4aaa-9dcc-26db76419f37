<template>
  <div class="intercom-panel">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>互联网对讲广播控制面板</span>
          <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
        </div>
      </template>

      <!-- 连接控制 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="24">
          <el-form :model="loginForm" :inline="true">
            <el-form-item label="服务器地址">
              <el-input v-model="loginForm.serverAddr" placeholder="192.168.1.100" />
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="loginForm.username" placeholder="mainalc" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="loginForm.password" type="password" placeholder="密码" />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="handleConnect"
                :loading="connecting"
                :disabled="isConnected"
              >
                {{ isConnected ? '已连接' : '连接' }}
              </el-button>
              <el-button 
                @click="handleDisconnect"
                :disabled="!isConnected"
              >
                断开
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 设备选择 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="12">
          <el-form-item label="选择设备">
            <el-select v-model="selectedDeviceId" placeholder="请选择设备" style="width: 100%">
              <el-option
                v-for="device in deviceList"
                :key="device.deviceId"
                :label="`${device.deviceName} (${device.deviceId})`"
                :value="device.deviceId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态">
            <el-tag :type="getDeviceStatusType(selectedDevice?.action)">
              {{ getDeviceStatusText(selectedDevice?.action) }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 视频播放区域 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="24">
          <div class="video-container">
            <video 
              ref="videoRef" 
              autoplay 
              muted 
              controls
              style="width: 100%; height: 300px; background: #000;"
            >
              您的浏览器不支持视频播放
            </video>
          </div>
        </el-col>
      </el-row>

      <!-- 控制按钮 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="24">
          <el-button-group>
            <el-button 
              type="primary" 
              @click="handlePlayVideo"
              :disabled="!canOperate"
              :loading="videoLoading"
            >
              播放视频
            </el-button>
            <el-button 
              @click="handleStopVideo"
              :disabled="!canOperate"
            >
              停止视频
            </el-button>
            <el-button 
              type="success" 
              @click="handleStartTalk"
              :disabled="!canOperate"
            >
              开始对讲
            </el-button>
            <el-button 
              @click="handleStopTalk"
              :disabled="!canOperate"
            >
              停止对讲
            </el-button>
          </el-button-group>
        </el-col>
      </el-row>

      <!-- 广播控制 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="24">
          <el-form :model="broadcastForm" :inline="true">
            <el-form-item label="广播类型">
              <el-radio-group v-model="broadcastForm.type">
                <el-radio :label="1">麦克风广播</el-radio>
                <el-radio :label="2">文件广播</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="音频文件" v-if="broadcastForm.type === 2">
              <el-input v-model="broadcastForm.filePath" placeholder="音频文件路径" />
            </el-form-item>

            <!-- 音量控制 -->
            <el-form-item label="麦克风音量">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-slider
                  v-model="broadcastForm.volume"
                  :min="0"
                  :max="100"
                  :step="1"
                  style="flex: 1;"
                  @change="handleMicVolumeChange"
                />
                <span style="min-width: 40px;">{{ broadcastForm.volume }}%</span>
              </div>
            </el-form-item>

            <el-form-item label="广播音量">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-slider
                  v-model="broadcastForm.broadcastVolume"
                  :min="0"
                  :max="100"
                  :step="1"
                  style="flex: 1;"
                  @change="handleBroadcastVolumeChange"
                />
                <span style="min-width: 40px;">{{ broadcastForm.broadcastVolume }}%</span>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="warning"
                @click="handleStartBroadcast"
                :disabled="!canOperate"
                :loading="broadcastLoading"
              >
                开始广播
              </el-button>
              <el-button
                @click="handleStopBroadcast"
                :disabled="!canOperate"
              >
                停止广播
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 音量控制 -->
      <VolumeControl
        :selected-device="selectedDevice"
        :is-connected="isConnected"
        @volume-changed="handleVolumeChanged"
      />

      <!-- 设备列表 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :data="deviceList" style="width: 100%" size="small">
            <el-table-column prop="deviceId" label="设备ID" width="100" />
            <el-table-column prop="deviceName" label="设备名称" />
            <el-table-column prop="deviceType" label="设备类型" width="100" />
            <el-table-column prop="action" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getDeviceStatusType(scope.row.action)" size="small">
                  {{ getDeviceStatusText(scope.row.action) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import intercomAPI from '@/api/intercom/broadcast.js';
import { BroadcastType } from '@/api/intercom/types.js';
import VolumeControl from './VolumeControl.vue';

export default {
  name: 'IntercomPanel',
  components: {
    VolumeControl
  },
  data() {
    return {
      // 连接状态
      isConnected: false,
      connecting: false,
      
      // 登录表单
      loginForm: {
        serverAddr: '************',
        // serverAddr: '***********',
        username: 'mainalc',
        password: '123456789'
      },
      
      // 设备相关
      deviceList: [],
      selectedDeviceId: null,
      
      // 操作状态
      videoLoading: false,
      broadcastLoading: false,
      
      // 广播表单
      broadcastForm: {
        type: 1, // 1: 麦克风, 2: 文件
        filePath: '',
        volume: 80, // 麦克风音量 (0-100)
        broadcastVolume: 80 // 广播音量 (0-100)
      }
    };
  },
  
  computed: {
    connectionStatus() {
      if (this.isConnected) {
        return { type: 'success', text: '已连接' };
      } else if (this.connecting) {
        return { type: 'warning', text: '连接中...' };
      } else {
        return { type: 'danger', text: '未连接' };
      }
    },

    selectedDevice() {
      return this.deviceList.find(d => d.deviceId === this.selectedDeviceId);
    },

    canOperate() {
      return this.isConnected && this.selectedDeviceId;
    }
  },

  watch: {
    // 监听设备选择变化，自动获取音量信息
    selectedDeviceId: {
      handler(newDeviceId, oldDeviceId) {
        if (newDeviceId && newDeviceId !== oldDeviceId && this.isConnected) {
          console.log(`🔄 设备切换到: ${newDeviceId}，获取音量信息...`);
          this.getCurrentVolumes();
        }
      },
      immediate: false
    },

    // 监听连接状态变化
    isConnected: {
      handler(connected) {
        if (connected && this.selectedDeviceId) {
          // 连接成功后获取当前设备音量
          setTimeout(() => {
            this.getCurrentVolumes();
          }, 1000); // 延迟1秒确保连接稳定
        }
      }
    }
  },
  
  async mounted() {
    await this.initializeSDK();
  },
  
  beforeUnmount() {
    this.cleanup();
  },
  
  methods: {
    // 初始化SDK
    async initializeSDK() {
      try {
        const result = await intercomAPI.initSDK();
        if (result) {
          this.setupEventListeners();
          this.$message.success('SDK初始化成功');
        } else {
          this.$message.error('SDK初始化失败');
        }
      } catch (error) {
        this.$message.error('SDK初始化失败: ' + error.message);
      }
    },
    
    // 设置事件监听
    setupEventListeners() {
      // 设备列表更新
      intercomAPI.on('onDeviceList', (result) => {
        if (result.error === 'success') {
          this.deviceList = result.params.list;
          this.$message.info(`设备列表已更新，共${this.deviceList.length}台设备`);
        }
      });
      
      // 设备状态通知
      intercomAPI.on('onNotify', (result) => {
        const { code, deviceId, action } = result.params;
        if (code === 'DeviceStatus') {
          const device = this.deviceList.find(d => d.deviceId === deviceId);
          if (device) {
            device.action = action;
            this.$message.info(`设备${deviceId}状态变更: ${this.getDeviceStatusText(action)}`);
          }
        }
      });
      
      // 错误处理
      intercomAPI.on('onError', (error) => {
        console.error('SDK错误:', error);
        this.$message.error('SDK错误: ' + (error.msg?.error || '未知错误'));
      });
      
      // 服务器断开
      intercomAPI.on('onServerClosed', () => {
        this.isConnected = false;
        this.$message.warning('服务器连接已断开');
      });
    },
    
    // 连接服务器
    async handleConnect() {
      if (!this.loginForm.serverAddr || !this.loginForm.username || !this.loginForm.password) {
        this.$message.error('请填写完整的连接信息');
        return;
      }
      
      this.connecting = true;
      try {
        const result = await intercomAPI.login(
          this.loginForm.username,
          this.loginForm.password,
          this.loginForm.serverAddr
        );
        
        this.isConnected = true;
        this.$message.success('连接成功');
        console.log('登录结果:', result);
        this.loginHandle = result.params.loginHandle;
      } catch (error) {
        this.$message.error('连接失败: ' + error.message);
      } finally {
        this.connecting = false;
      }
    },
    
    // 断开连接
    async handleDisconnect() {
      try {
        await intercomAPI.logout();
        this.isConnected = false;
        this.deviceList = [];
        this.selectedDeviceId = null;
        this.$message.success('已断开连接');
      } catch (error) {
        this.$message.error('断开连接失败: ' + error.message);
      }
    },
    
    // 播放视频
    async handlePlayVideo() {
      this.videoLoading = true;
      try {
        await intercomAPI.playVideo(this.$refs.videoRef, this.selectedDeviceId, false);
        this.$message.success('视频播放成功');
      } catch (error) {
        this.$message.error('视频播放失败: ' + error.message);
      } finally {
        this.videoLoading = false;
      }
    },
    
    // 停止视频
    async handleStopVideo() {
      try {
        await intercomAPI.stopVideo(this.selectedDeviceId);
        this.$message.success('视频已停止');
      } catch (error) {
        this.$message.error('停止视频失败: ' + error.message);
      }
    },
    
    // 开始对讲
    async handleStartTalk() {
      try {
        await intercomAPI.startTalk(this.selectedDeviceId);
        this.$message.success('对讲已开始');
      } catch (error) {
        this.$message.error('开始对讲失败: ' + error.message);
      }
    },
    
    // 停止对讲
    async handleStopTalk() {
      try {
        await intercomAPI.stopTalk(this.selectedDeviceId);
        this.$message.success('对讲已停止');
      } catch (error) {
        this.$message.error('停止对讲失败: ' + error.message);
      }
    },
    
    // 开始广播
    async handleStartBroadcast() {
      if (!this.selectedDeviceId) {
        this.$message.error('请先选择设备');
        return;
      }
      
      if (this.broadcastForm.type === 2 && !this.broadcastForm.filePath) {
        this.$message.error('文件广播请输入音频文件路径');
        return;
      }
      
      this.broadcastLoading = true;
      try {
        await intercomAPI.startBroadcast(
          [this.selectedDeviceId],
          this.broadcastForm.type,
          this.broadcastForm.type === 2 ? this.broadcastForm.filePath : null
        );
        this.$message.success('广播已开始');
      } catch (error) {
        this.$message.error('开始广播失败: ' + error.message);
      } finally {
        this.broadcastLoading = false;
      }
    },
    
    // 停止广播
    async handleStopBroadcast() {
      try {
        await intercomAPI.stopBroadcast(this.loginHandle);
        this.$message.success('广播已停止');
      } catch (error) {
        this.$message.error('停止广播失败: ' + error.message);
      }
    },
    
    // 获取设备状态类型
    getDeviceStatusType(action) {
      switch (action) {
        case 'Normal': return 'success';
        case 'Offline': return 'danger';
        case 'Start': return 'warning';
        case 'Dealing': return 'info';
        default: return '';
      }
    },
    
    // 获取设备状态文本
    getDeviceStatusText(action) {
      switch (action) {
        case 'Normal': return '在线';
        case 'Offline': return '离线';
        case 'Start': return '呼叫中';
        case 'Dealing': return '处理中';
        default: return '未知';
      }
    },

    // 处理麦克风音量变化
    async handleMicVolumeChange(value) {
      if (!this.canOperate) {
        return;
      }

      try {
        const requestId = intercomAPI.generateRequestId();
        await intercomAPI.setDeviceVolume(
          requestId,
          this.selectedDeviceId,
          'input', // 麦克风音量
          [value]
        );

        console.log(`✅ 麦克风音量已设置为: ${value}%`);
        this.$message.success(`麦克风音量已调整为: ${value}%`);
      } catch (error) {
        console.error('❌ 设置麦克风音量失败:', error);
        this.$message.error('设置麦克风音量失败: ' + error.message);
      }
    },

    // 处理广播音量变化
    async handleBroadcastVolumeChange(value) {
      if (!this.canOperate) {
        return;
      }

      try {
        const requestId = intercomAPI.generateRequestId();
        await intercomAPI.setBroadcastVolume(
          requestId,
          this.selectedDeviceId,
          [value]
        );

        console.log(`✅ 广播音量已设置为: ${value}%`);
        this.$message.success(`广播音量已调整为: ${value}%`);
      } catch (error) {
        console.error('❌ 设置广播音量失败:', error);
        this.$message.error('设置广播音量失败: ' + error.message);
      }
    },

    // 获取当前设备音量
    async getCurrentVolumes() {
      if (!this.canOperate) {
        return;
      }

      try {
        const requestId = intercomAPI.generateRequestId();

        // 获取麦克风音量
        const micVolumeResult = await intercomAPI.getDeviceVolume(
          requestId,
          this.selectedDeviceId,
          'input'
        );

        // 获取广播音量
        const broadcastVolumeResult = await intercomAPI.getBroadcastVolume(
          requestId + 1,
          this.selectedDeviceId
        );

        console.log('当前音量信息:', {
          microphone: micVolumeResult,
          broadcast: broadcastVolumeResult
        });

        // 更新UI显示的音量值（如果服务器返回了具体数值）
        // 注意：需要根据实际API返回的数据结构调整
        if (micVolumeResult.params && micVolumeResult.params.volume) {
          this.broadcastForm.volume = micVolumeResult.params.volume[0] || this.broadcastForm.volume;
        }

        if (broadcastVolumeResult.params && broadcastVolumeResult.params.volume) {
          this.broadcastForm.broadcastVolume = broadcastVolumeResult.params.volume[0] || this.broadcastForm.broadcastVolume;
        }

      } catch (error) {
        console.error('❌ 获取音量信息失败:', error);
      }
    },

    // 处理音量变化事件（来自VolumeControl组件）
    handleVolumeChanged(volumeInfo) {
      console.log('🔊 音量变化:', volumeInfo);

      // 更新本地音量值
      if (volumeInfo.type === 'microphone') {
        this.broadcastForm.volume = volumeInfo.value;
      } else if (volumeInfo.type === 'broadcast') {
        this.broadcastForm.broadcastVolume = volumeInfo.value;
      }

      // 可以在这里添加其他需要响应音量变化的逻辑
      this.$message.success(`${volumeInfo.type === 'microphone' ? '麦克风' : '广播'}音量已调整为: ${volumeInfo.value}%`);
    },

    // 清理资源
    cleanup() {
      if (this.isConnected) {
        intercomAPI.logout();
        intercomAPI.destroy();
      }
    }
  }
};
</script>

<style scoped>
.intercom-panel {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
