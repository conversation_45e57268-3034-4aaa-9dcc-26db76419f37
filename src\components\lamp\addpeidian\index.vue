<template>
  <div class="peidianxiang">
    <!-- 选择设备 -->
    <Selectbox 
      v-if="showSelectbox"
      :selectData="selectData"
      @activItem="deviceActivItem"
      @next="next">
    </Selectbox>
    <!-- 设备表单 -->
    <Frombox 
      ref="fromboxRef" 
      v-if="showFrombox" 
      @submit="submit"
      :activItemData="activItemData" ></Frombox>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits } from 'vue'
import Selectbox from "./select.vue"
import { ControllerType, add } from "@/api/lamp/switchBox.js"
import Frombox from "./fromBox.vue"

const activItemData = ref({});
const deviceActivItem = (item) => {
  console.log("item",item);
  activItemData.value = item;
}
const showSelectbox = ref(true);
const showFrombox = ref(false);
const selectData = ref({})
const getselectData = () => {
  ControllerType().then(res => {
    selectData.value = res.data.data;
  })
}

const next = () => {
  showSelectbox.value = false;
  showFrombox.value = true;
}

const emit = defineEmits(['submit'])
const submit = (data) => {
  console.log("datasubmit",data);
  if (data.success) {
    emit('submit',data.data)
  }else{
    return
  }
}


onMounted(()=>{
  getselectData()
})
</script>
<style lang="scss" scoped>
.peidianxiang{
    width: 100%;
    height: 100%;
}
</style>