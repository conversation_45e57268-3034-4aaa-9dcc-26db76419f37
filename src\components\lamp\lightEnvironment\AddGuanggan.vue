<template>
  <div class="peidianxiang">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px" label-position="top">
      <div class="switch-box">
        <span class="from-style">灯具执行状态</span>
        <el-switch
          v-model="formData.executionStatus"
          active-color="#165DFF"
          inactive-color="#165DFF"
          :active-value="1"
          :inactive-value="2"
          size="large"
          :disabled="readonly"
        ></el-switch>
      </div>
      <el-form-item label="亮度值" prop="businessLight">
        <el-input 
          class="input-style" 
          v-model="formData.businessLight" 
          placeholder="请输入0-100000"
          :disabled="readonly"
        />
      </el-form-item>
      <el-form-item label="集中控制器" prop="centralizedNo">
        <el-select
            v-model="formData.centralizedNo"
            @change="centralizedNoChange"
            multiple
            :disabled="readonly"
            placeholder="请选择"
            style="width: 100%">
            <el-option
                v-for="item in centralizedNoList"
                :key="item.deviceNo"
                :label="item.deviceName"
                :value="item.deviceNo"/>
        </el-select>
      </el-form-item>
      <el-form-item label="回路" prop="loopNums">
        <el-select
            v-model="formData.loopNums"
            @visible-change="cxloopNums"
            @change="loopNumsChange"
            multiple
            :disabled="readonly"
            placeholder="请选择"
            style="width: 100%">
            <el-option
                key="select-all"
                label="全选"
                :value="'all'"
            />
            <el-option
                v-for="item in loopNumsList"
                :key="item.id"
                :label="item.loopName"
                :value="item.id"/>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="suoming">
      系统会实时读取各通道的光照传感器数值。
      <br />
      当数值位于0到200 Lux之间时,环境光比较微弱，可认为是夜晚；
      <br />
      当数值位于200到1000 Lux之间时,环境光比较微弱，属于日出或日落的黎明/黄昏过渡期；
      <br />
      当数值位于数值大于1000 Lux时，说明光照已接近或达到传感器量程上限，可视为白天。
    </div>
    <div class="btn-box" v-if="!readonly">
      <el-button color="#165DFF" type="primary" @click="submitForm">确定</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits, defineProps, watch } from 'vue'
import { ControllerType, add } from "@/api/lamp/switchBox.js"
import {  getList } from '@/api/lamp/switchBox.js'
import {  getRound } from '@/api/lamp/lightEnvironment.js'


const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => null
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref(null)
const formData = ref({
  businessLight: '',
  executionStatus: 2,
  centralizedNo:[],
  loopNums: [],
})

// 监听外部传入的表单数据
watch(() => props.formData, (newVal) => {
  if (newVal) {
    console.log(newVal,">>>>>>>>>>>")
    formData.value = { ...newVal }
    getRound({centralizedNo: formData.value.centralizedNo.join(',') }).then(res=>{
      console.log(res.data.data,"res.data.data.records")
      if(res.data.code == 200){
        loopNumsList.value = res.data.data.map(element => ({
          ...element,
          loopName: `${element.loopName}-(${element.centralizedNo})`
        }));
        console.log(loopNumsList.value,"res.data.data.records")
        // forLoopList.value = res.data.data.records
      }
    })
  }
}, { immediate: true })

const rules = {
  businessLight: [
    { required: true, message: '请输入亮度值', trigger: 'blur' },
    { pattern: /^([0-9]|[1-9][0-9]{1,4}|100000)$/, message: '请输入0-100000之间的数值', trigger: 'blur' }
  ]
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      formData.value.centralizedNo = formData.value.centralizedNo.join(',')
      formData.value.loopNums = formData.value.loopNums.join(',')
      emit('submit', formData.value)
    }
  })
}

const handleCancel = () => {
  formData.value = {
    businessLight: '',
    executionStatus: 2
  }
  emit('cancel')
}

const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

const centralizedNoList = ref([])
const loopNumsList = ref([])
const selectCentralizedNoS = ref('')

const getCentralizedNoList = async () => {
  getList(1,-1,{}).then(res=>{
    if(res.data.code == 200){
      centralizedNoList.value = res.data.data.records
      console.log(centralizedNoList.value,"111111")
    }
  })
}
const centralizedNoChange = (val) => {
  console.log(val,"val")
  selectCentralizedNoS.value = val.join(',')
}

const cxloopNums = (val) => {
 console.log(val,"cxloopNums")
  if(val && selectCentralizedNoS.value !== ''){
    getRound({centralizedNo: selectCentralizedNoS.value}).then(res=>{
      console.log(res.data.data,"res.data.data.records")
      if(res.data.code == 200){
        loopNumsList.value = res.data.data.map(element => ({
          ...element,
          loopName: `${element.loopName}-(${element.centralizedNo})`
        }));
        console.log(loopNumsList.value,"res.data.data.records")
        // forLoopList.value = res.data.data.records
      }
    })
  }
}

const loopNumsChange = (val) => {
  // 处理全选逻辑
  if (val.includes('all')) {
    // 如果选中了全选，则选中所有选项
    formData.value.loopNums = loopNumsList.value.map(item => item.id)
  } else if (val.length === loopNumsList.value.length) {
    // 如果手动选择了所有选项，也要包含全选选项
    formData.value.loopNums = ['all', ...val]
  } else {
    // 移除全选选项
    formData.value.loopNums = val.filter(v => v !== 'all')
  }
}

onMounted(()=>{
  getCentralizedNoList()
})
</script>

<style lang="scss" scoped>
.peidianxiang{
    width: 100%;
    height: 100%;
    :deep(.el-form) {

    }
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      font-family: Source Han Sans;
      font-size: 18px;
      font-weight: 500;
      line-height: normal;
      text-transform: uppercase;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #333333;
    }
    
    :deep(.el-input) {
      width: 100%;
      background-color: #F7F8FA;
      border: none;
    }
    
    :deep(.el-button) {
      margin-right: 10px;
    }
}
.btn-box{
  margin: 0;
}

.switch-box{
  display: flex;
  justify-content: space-between;
  span{
    font-family: Source Han Sans;
    font-size: 18px;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
  }
}
.suoming{
  margin: 20px 0 20px;
  font-family: Source Han Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #A2AAB3;
}
.from-style{
  display: flex;
  align-items: center;
}
:deep(.custom-dialog .el-dialog__body){
  padding-top: 0;
}

:deep(.el-input) {
  .el-input__wrapper {
    width: 340px;
    background-color: #F7F8FA;
    border: none;
    box-shadow: none;
    padding: 12px;
    
    &.is-focus {
      box-shadow: none;
    }

    &.is-readonly {
      cursor: pointer;
    }
  }
  
  input {
    width: 340px;
    height: 20px;
    font-size: 14px;
    &::placeholder {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 350;
      line-height: normal;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #A2AAB3;
    }
  }
}
:deep(.el-select) {
  .el-select__wrapper {
    width: 100%;
    background-color: #F7F8FA;
    border: none;
    box-shadow: none;
    padding: 12px;
    
    &.is-focus {
      box-shadow: none;
    }

    &.is-readonly {
      cursor: pointer;
    }
  }
  
  input {
    width: 340px;
    height: 20px;
    font-size: 14px;
    &::placeholder {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 350;
      line-height: normal;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #A2AAB3;
    }
  }
}
</style>