<template>
    <div class="title">设备说明</div>
    <div class="detail">
      <div class="img-box">
          <img class="img-style" :src="props.activItemData.icon" alt="">
      </div>
      <div class="content-box">
          <el-form
            ref="ruleFormRef"
            style="max-width: 600px"
            :model="ruleForm"
            :rules="rules"
            label-width="auto"
            class="demo-ruleForm"
            label-position="top"
            status-icon >
            <el-form-item label="灯杆名称：" prop="luminaireName">
              <el-input class="inputStyles" v-model="ruleForm.luminaireName" />
            </el-form-item>
            <el-form-item label="灯杆编号：" prop="luminaireNo">
              <el-input class="inputStyles" v-model="ruleForm.luminaireNo" />
            </el-form-item>
          </el-form>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, defineProps } from 'vue'
  
  let props = defineProps({
      activItemData: {
          type: Number,
      },
  });
  
  const ruleForm = ref({ });
  const rules = ref({
    luminaireName: [
      { required: true, message: "请输入设备名称", trigger: "blur" },
      { min: 3, max: 5, message: "名称长度未3-5个字段", trigger: "blur" },
    ],
    luminaireNo: [
      { required: true, message: "请输入设备编号", trigger: "blur" },
    ],
  
  });
  
  const ruleFormRef = ref(null);
  const detailSubmitForm = async () => {
    console.log("submit!",ruleFormRef.value);
    let row = {}
    await ruleFormRef.value.validate((valid, fields) => {
      console.log("submit!",valid, fields);
      ruleForm.value.luminaireModel = props.activItemData.id
      row = { success: valid , data: ruleForm.value , icon: props.activItemData.icon }
      console.log("submit222222",row);
    });
    return row
  };
  
  
  defineExpose({
    detailSubmitForm
  });
  </script>
  <style lang="scss" scoped>
  .detail{
      width: 100%;
      height: 280px;
      display: flex;
      justify-content: space-around;
  }
  .title{
      margin-bottom: 24px;
  }
  .img-box{
      width: 280px;
      height: 280px;
      background-color: #F2F2F2;
      .img-style{
          width: 100%;
          height: 100%;
      }
  }
  .content-box{
      width: 280px;
      height: 100%;
      overflow-y: auto;
  }
  
  .indent{
      text-indent: 32px;
      text-align: left;
  }
  </style>