<template>
  <div class="container">
    <div class="from-box">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>第三方配置</span>
            <el-tag :type="status ? 'success' : 'danger'" class="status-tag">
              {{ status ? '已开启' : '已关闭' }}
            </el-tag>
          </div>
        </template>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="推送账户名称" prop="pushName">
            <el-input v-model="form.pushName" placeholder="请输入推送账户名称" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="推送URL" prop="pushUrl">
            <el-input v-model="form.pushUrl" placeholder="请输入推送URL" maxlength="200"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="200"></el-input>
          </el-form-item>
        </el-form>
        <div style="width: 100%; display: flex; justify-content: center;">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="open" type="success" v-if="!status">开 启</el-button>
          <el-button @click="close" type="danger" v-else>关 闭</el-button>
          <el-tooltip
            v-if="PZID"
            class="box-item"
            effect="dark"
            content="重置后，将无法恢复，请谨慎操作！（该操作会删除原有配置）"
            placement="top-start"
          >
            <el-button @click="reset" type="warning">重 置</el-button>
          </el-tooltip>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import { detail, update, save, chargeStatus, remove } from '@/api/lamp/tripartiteconfiguration';

const formRef = ref(null);
const status = ref(false); // 默认关闭状态
const PZID = ref(null);

const form = reactive({
  pushName: '',
  pushUrl: '',
  remark: ''
});

const rules = reactive({
  pushName: [
    { required: true, message: '请输入推送账户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符之间', trigger: 'blur' }
  ],
  pushUrl: [
    { required: true, message: '请输入推送URL', trigger: 'blur' },
    { pattern: /^(https?:\/\/)?(([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}|localhost|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(:\d{1,5})?(\/[a-zA-Z0-9\-\._~:\/?#[\]@!$&'()*+,;=]*)?$/, message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  remark: [
    { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
  ]
});

const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        let response;
        if (PZID.value) {
          // 修改操作
          const params = {
            ...form,
            id: PZID.value
          };
          response = await update(params);
          if (response.data.code === 200) {
            ElMessage.success('配置修改成功');
            await getDetail();
          } else {
            ElMessage.error(response.data.msg || '配置修改失败');
          }
        } else {
          // 新增操作
          response = await save(form);
          if (response.data.code === 200) {
            ElMessage.success('配置保存成功');
            await getDetail();
          } else {
            ElMessage.error(response.data.msg || '配置保存失败');
          }
        }
      } catch (error) {
        ElMessage.error('提交表单时发生错误');
        console.error('表单提交错误:', error);
      }
    } else {
      console.log('表单验证失败', fields);
    }
  });
};

const open = async () => {
  const params = {
    id: PZID.value,
    status: 1
  }
  const response = await chargeStatus(params);
  if (response.data.code === 200) {
    status.value = !status.value;
    ElMessage.success(status.value ? '已成功开启' : '已成功关闭');
    console.log('开启');
    await getDetail();
  } else {
    ElMessage.error(response.data.msg || '开启失败');
  }
};

const close = async () => {
  const params = {
    id: PZID.value,
    status: 0
  }
  const response = await chargeStatus(params);  
  if (response.data.code === 200) {
    status.value = !status.value;
    ElMessage.success(status.value ? '已成功开启' : '已成功关闭');
    console.log('关闭');
    await getDetail();
  } else {
    ElMessage.error(response.data.msg || '关闭失败');
  }
};

const getDetail = async () => {
  const response = await detail();
  if (response.data.code === 200) {
    form.pushName = response.data.data.pushName;
    form.pushUrl = response.data.data.pushUrl;  
    status.value = response.data.data.status;
    PZID.value = response.data.data.id;
  }
};

const reset = async () => {

  ElMessageBox.confirm(
    '重置后，将无法恢复，请谨慎操作！（该操作会删除原有配置）',
    'Warning',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
      const params = {
        id: PZID.value,
      }
      const response = await remove(params);
      if (response.data.code === 200) {
        ElMessage.success('重置成功');
        await getDetail();
      } else {
        ElMessage.error(response.data.msg || '重置失败');
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消重置',
      })
    })
}

onMounted(async () => {
  await getDetail();
});

</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 16px;
  display: flex;
  justify-content: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.status-tag {
  margin-left: 8px;
}

.from-box {
  width: 800px;
  margin: 150px auto;
}
</style>