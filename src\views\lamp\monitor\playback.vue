<template>
  <el-row class="row-container">
    <el-col :span="4" class="left-panel">
      <div class="left-flex">
        <!-- 设备树 -->
        <div class="device-tree">
          <el-tree
            :data="groupList"
            node-key="id"
            :props="{
              label: 'poleName',
              children: 'children'
            }"
            highlight-current
            accordion
            :expand-on-click-node="false"
            @node-click="onGroupClick"
            class="group-tree"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <i v-if="data.screens" class="el-icon-folder" style="color:#409EFF;margin-right:4px;"></i>
                <i v-else class="el-icon-video-camera" style="color:#67C23A;margin-right:4px;"></i>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="node.label"
                  :disabled="node.label.length<=9"
                  placement="top-start"
                >
                  <span class="node-label">
                    {{ node.label.length > 8 ? node.label.substring(0, 3) + '...' : node.label }}
                  </span>
                </el-tooltip>
              </span>
            </template>
          </el-tree>
        </div>
        <!-- 回放列表 -->
        <div class="playback-list">
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%; margin-bottom: 8px;"
            :disabled="!selectedDevice"
            @change="fetchPlaybackList"
          />
          <el-empty v-if="!selectedDevice" description="请选择设备" />
          <el-empty v-else-if="playbackList.length === 0" description="暂无回放" />
          <el-list v-else class="playback-items">
            <el-list-item
              v-for="item in playbackList"
              :key="item.id"
              @click="playPlayback(item)"
              class="playback-item"
            >
              {{ item.timeRange }}
            </el-list-item>
          </el-list>
        </div>
      </div>
    </el-col>
    <el-col :span="20" class="right-panel">
      <div class="btn-box">
        <el-radio-group v-model="radio2" @change="handleScreenModeChange">
          <el-radio-button
            v-for="item in screenModes"
            :key="item.value"
            :label="item.value"
          >{{ item.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="screen-grid">
        <HzwlPlayer 
          ref="player" 
          :videoUrl="url" 
          :playerId="playerId"
          :multiScreenOne="multiScreenOne"
          :multiScreenTwo="multiScreenTwo"
          :multiScreenFour="multiScreenFour"
          :multiScreenSix="multiScreenSix"
          :multiScreenNine="multiScreenNine"
          :multiScreenSixteen="multiScreenSixteen"
          @player-click="handlePlayerClick"
        />
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import { getMonitorList, getVideoMonitorReplay } from '@/api/lamp/monitor'

const screenModes = [
  { label: '单分屏', value: 'one', var: 'multiScreenOne' },
  // { label: '两分屏', value: 'two', var: 'multiScreenTwo' },
  { label: '四分屏', value: 'four', var: 'multiScreenFour' },
  { label: '六分屏', value: 'six', var: 'multiScreenSix' },
  { label: '九分屏', value: 'nine', var: 'multiScreenNine' },
  { label: '十六分屏', value: 'sixteen', var: 'multiScreenSixteen' }
]

const radio2 = ref('nine') // 默认九分屏

const multiScreenOne = ref(false)
const multiScreenTwo = ref(false)
const multiScreenFour = ref(false)
const multiScreenSix = ref(false)
const multiScreenNine = ref(true)
const multiScreenSixteen = ref(false)

function handleScreenModeChange(val) {
  multiScreenOne.value = val === 'one'
  multiScreenTwo.value = val === 'two'
  multiScreenFour.value = val === 'four'
  multiScreenSix.value = val === 'six'
  multiScreenNine.value = val === 'nine'
  multiScreenSixteen.value = val === 'sixteen'
}

// 假数据
const groupList = ref([])

const selectedDevice = ref(null)
const selectedDate = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'))
const playbackList = ref([])

function onGroupClick(data) {
  // 只允许点击子级节点（没有 screens 的节点）
  if (!data.children) {
    console.log(data,"data")
    selectedDevice.value = data.monitoringNumber
  }
}

// 模拟回放查询
function fetchPlaybackList(val) {
  // 兼容 el-date-picker 传入的 Date/String
  let dateStr = selectedDate.value
  if (dateStr instanceof Date) {
    dateStr = dayjs(dateStr).format('YYYY-MM-DD')
  } else if (typeof dateStr === 'string' && dateStr.includes('GMT')) {
    dateStr = dayjs(new Date(dateStr)).format('YYYY-MM-DD')
  }
  // 这里用格式化后的 dateStr
  playbackList.value = [
    { id: 1, timeRange: `${dateStr} 00:00-01:00` },
    { id: 2, timeRange: `${dateStr} 02:00-03:00` },
    { id: 3, timeRange: `${dateStr} 03:00-04:00` },
    { id: 4, timeRange: `${dateStr} 04:00-05:00` },
    { id: 5, timeRange: `${dateStr} 05:00-06:00` },
    { id: 6, timeRange: `${dateStr} 06:00-07:00` },
    { id: 7, timeRange: `${dateStr} 07:00-08:00` },
    { id: 8, timeRange: `${dateStr} 08:00-09:00` },
    { id: 9, timeRange: `${dateStr} 09:00-10:00` },
    { id: 10, timeRange: `${dateStr} 10:00-11:00` },
    { id: 11, timeRange: `${dateStr} 12:00-13:00` },
    { id: 12, timeRange: `${dateStr} 13:00-14:00` },
    { id: 13, timeRange: `${dateStr} 14:00-15:00` },
    { id: 14, timeRange: `${dateStr} 15:00-16:00` },
    { id: 15, timeRange: `${dateStr} 16:00-17:00` },
    { id: 16, timeRange: `${dateStr} 17:00-18:00` },
    { id: 17, timeRange: `${dateStr} 18:00-19:00` },
    { id: 18, timeRange: `${dateStr} 19:00-20:00` },
    { id: 19, timeRange: `${dateStr} 20:00-21:00` },
    { id: 20, timeRange: `${dateStr} 21:00-22:00` },
    { id: 21, timeRange: `${dateStr} 22:00-23:00` },
    { id: 22, timeRange: `${dateStr} 23:00-23:59` },
  ]
}

// 播放回放
function playPlayback(item) {
  // 这里可以触发右侧播放器播放
  console.log('播放回放', item)
}

function convertTreeData(list) {
  return list.map(group => ({
    id: group.id,
    poleName: group.poleName || group.name, // 兼容不同字段
    children: (group.children || []).map(screen => ({
      id: screen.monitoringNumber || screen.id,
      poleName: screen.monitoringName || screen.name,
      monitoringNumber: screen.monitoringNumber || screen.id
    }))
  }))
}

const getMonitorListData = async () => {
  const res = await getMonitorList()
  groupList.value = convertTreeData(res.data.data)
  console.log(groupList.value,"res")
}

onMounted(() => {
  getMonitorListData()
  handleScreenModeChange(radio2.value)
})
</script>

<style scoped>
*{
  box-sizing: border-box;
}
.row-container {
  width: 100%;
  padding: 8px;
  height: calc(100vh - 66px);
  background: #f5f6fa;
}
.left-panel {
  height: calc(100vh - 66px);
  min-width: 220px;
  max-width: 300px;
  padding: 0;
}
.left-card {
  height: 100%;
  background: #fff;
  border-radius: 0 8px 8px 0;
  box-shadow: 2px 0 8px #f0f1f2;
  border: none;
}
.left-flex {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.device-tree {
  flex: 1 1 60%;
  min-height: 180px;
  overflow: auto;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}
.playback-list {
  flex: 1 1 40%;
  padding: 8px 0 0 0;
  overflow: hidden;
}
.playback-items {
  padding: 0;
  margin: 0;
  height: 90%;
  list-style: none;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
.playback-item {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: background 0.2s;
}
.playback-item:hover {
  background: #f0f6ff;
  color: #2E74FF;
}
.group-tree {
  height: 100%;
  background: #fff;
  padding: 8px 0 8px 0;
}
.custom-tree-node {
  display: flex;
  align-items: center;
  font-size: 15px;
}
.node-label {
  font-weight: 500;
  color: #333;
}
.right-panel {
  height: calc(100vh - 66px);
  /* padding: 24px 24px 0 0; */
  /* background: #000; */
}
.box-card-video {
  height: 100%;
  /* background: #fff; */
  /* border-radius: 8px; */
  box-shadow: 0 2px 12px #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
}
.screen-grid {
  width: 100%;
  height: calc(100% - 40px);
  border-radius: 8px;
  align-items: center;
  justify-items: center;
}

/* el-tree 选中节点背景色和文字色 */
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #2E74FF !important;
  color: #fff !important;
}
:deep(.el-tree-node.is-current > .el-tree-node__content .node-label) {
  color: #fff !important;
}
:deep(.el-tree-node.is-current > .el-tree-node__content i) {
  color: #fff !important;
}

.btn-box{
  width: 100%;
  margin: 0px 0 16px ;
  display: flex;
  justify-content: flex-end;
}
</style>

