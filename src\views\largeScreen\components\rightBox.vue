<template>
    <div class="right-box">
        <div class="right-box-jk">
            <div class="title-box">灯杆监控</div>
            <div class="jk-number-box">
                <div class="jk-number-item" style="margin-right: 117px;">
                    <img style="width: 56px; height: 56px;" src="@/assets/largescreen/spjk-zx.png" alt="">
                    <div class="number-box-right" style="margin-left: 10px;">
                        <div class="zt-title" >在线设备数</div>
                        <div class="number">{{ jkOnline }}</div>
                    </div>
                </div>
                <div class="jk-number-item">
                    <img style="width: 56px; height: 56px;" src="@/assets/largescreen/spjk-lx.png" alt="">
                    <div class="number-box-right" style="margin-left: 10px;">
                        <div class="zt-title" >离线设备数</div>
                        <div class="number">{{ jkOffline }}</div>
                    </div>
                </div>
            </div>
            <div class="js-table" v-show="!showUrl">
                <el-table :data="jsTableData" style="width: 100%; height: 100%;" align="center">
                    <el-table-column prop="monitoringName" label="名称" width="120" align="center"/>
                    <el-table-column prop="monitoringNumber" label="编号" align="center"/>
                    <!-- 操作 -->
                    <el-table-column fixed="right" label="操作" width="80" align="center">
                        <template #default="scope">
                            <el-button style="font-size: 16px; color: #2585CF; text-decoration: underline;" color="#2585CF" link type="primary" size="small" @click="handleMonitor(scope.row)">查看监控</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="jk-viode-box" v-show="showUrl">
                <el-icon class="close-icon-stylezdy" @click="closeUrl"><Close /></el-icon>
                <HzwlPlayer
                    ref="player" 
                    :videoUrl="url"
                    playerId="playerone"
                    :multiScreenOne="true"
                    @player-click="handlePlayerClick"
                />
            </div>
        </div>
        <div class="right-box-gb">
            <div class="title-box">广播设备在线情况</div>
            <div class="gb-number-box">
                <div class="gb-number-item" style="margin-right: 117px;">
                    <img style="width: 93px; height: 64px;" src="@/assets/largescreen/qtcgq-zx.png" alt="">
                    <div class="number-box-right">
                        <div class="number">211</div>
                        <div class="zt-title gb-before-zx" >在线设备</div>
                    </div>
                </div>
                <div class="gb-number-item">
                    <img style="width: 93px; height: 64px;" src="@/assets/largescreen/qtcgq-lx.png" alt="">
                    <div class="number-box-right">
                        <div class="number">120</div>
                        <div class="zt-title gb-before-lx" >离线设备</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-box-zhpm">
            <div class="title-box">智慧屏幕设备在线情况</div>
            <div class="device-status">
                <div class="status-item">
                    <div class="status-label">设备在线数</div>
                    <div class="status-value">{{ controllerOnline }}</div>
                </div>
                <div class="progress-wrapper online-gradient">
                    <div class="progress-inner">
                        <el-progress 
                            :percentage="onlinePercentage" 
                            :show-text="false" 
                            :stroke-width="6"
                            :color="onlineColors"
                            class="custom-progress"
                        />
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">设备离线数</div>
                    <div class="status-value">{{ controllerOffline }}</div>
                </div>
                <div class="progress-wrapper offline-gradient">
                    <div class="progress-inner">
                        <el-progress 
                            :percentage="offlinePercentage" 
                            :show-text="false" 
                            :stroke-width="6"
                            :color="offlineColors"
                            class="custom-progress"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup> 
import { ref, computed, onMounted } from 'vue';
import { getMonitorOnline, getLampList } from '@/api/lamp/largeScreen';
import { getVideoMonitor } from '@/api/lamp/monitor'
import { doneList } from '@/api/work/work';

const jkOnline = ref(0)
const jkOffline = ref(0)
const jsTableData = ref(0)

// 获取监控在线情况
const fetchMonitorOnline = async () => {
    try {
        const res = await getMonitorOnline();
        if (res.data.code === 200) {
            console.log(res,"res")
            jkOnline.value = res.data.data.online.length
            jkOffline.value = res.data.data.offOnline.length
            jsTableData.value = res.data.data.online
        }
    } catch (error) {
        console.error('获取集中控制器在线情况失败:', error);
    }
};


const url = ref('')
const showUrl = ref(false)
const player = ref(null)

// 设置设备数量
const controllerOnline = ref(22);
const controllerOffline = ref(24);

// 计算总数
const totalControllers = computed(() => controllerOnline.value + controllerOffline.value);

// 计算百分比
const onlinePercentage = computed(() => {
    return totalControllers.value ? (controllerOnline.value / totalControllers.value) * 100 : 0;
});

const offlinePercentage = computed(() => {
    return totalControllers.value ? (controllerOffline.value / totalControllers.value) * 100 : 0;
});

// 设置进度条颜色
const onlineColors = [
    { color: '#23B1DD', percentage: 0 },
    { color: '#48D8F9', percentage: 100 }
];

const offlineColors = [
    { color: '#DD2323', percentage: 0 },
    { color: '#F94848', percentage: 100 }
];

// 播放
const handlePlay = async (index) => {
  try {
    await player.value.onPlayer()
  } catch (error) {
    // 使用Element Plus的消息提示
    ElMessage.warning(error.message || '请先选择要播放的播放器')
  }
}

// 查看监控
const handleMonitor = (row) => {
    console.log(row,"row")
    getVideoMonitor({deviceNo:row.monitoringNumber}).then(res => {
      console.log(res,"res")
      let videourl = res.data.data
      videourl = atob(atob(videourl))
      console.log(videourl,"videourl")
      url.value = videourl
      setTimeout(() => {
        showUrl.value = true
        handlePlay()
      }, 500)
    })
}

// 关闭监控
const closeUrl = () => {
    showUrl.value = false
}

onMounted(() => {
    fetchMonitorOnline();
})
</script>
<style scoped lang="scss">
.right-box{
    width: 487px;
    height: calc(100% - 82px);
    position: absolute;
    top: 82px;
    right: 0;
    padding-top: 16px;
    padding-right: 36px;
}
.right-box-jk{
    width: 451px;
    min-height: 417px;
    background: url(@/assets/largescreen/jk-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: 18px;
}
.right-box-gb{
    width: 451px;
    min-height: 203px;
    background: url(@/assets/largescreen/qtchq-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: 18px;
}

.right-box-zhpm{
    width: 451px;
    min-height: 282px;
    background: url(@/assets/largescreen/zhpm-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: 32px;
}

.gb-number-box{
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 46px;
    .gb-number-item{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
}

.jk-number-box{
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 46px;
    .jk-number-item{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
}

.js-table{
    height: 200px;
    padding: 0 23px;
}

.jk-viode-box{
    width: 100%;
    height: 200px;
    padding: 0 23px;
    position: relative;
}

.zt-title{
    font-family: Source Han Sans;
    font-size: 13px;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFFFFF;
    display: flex;
    align-items: center;
}

.number{
    font-family: Source Han Sans;
    font-size: 20px;
    font-weight: 500;
    line-height: 25px;
    letter-spacing: 0px;
    color: #FFFFFF;
}

.gb-before-zx::before{
    content: " ";
    display: block;
    width: 6px;
    height: 6px;
    background: #0759DA;
    margin-right: 4px;
}
.gb-before-lx::before{
    content: " ";
    display: block;
    width: 6px;
    height: 6px;
    background: #D3D5DA;
    margin-right: 4px;
}

.device-status {
    padding: 60px 30px 0;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.status-label {
    font-family: Source Han Sans;
    font-size: 14px;
    color: #FFFFFF;
}

.status-value {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: bold;
    color: #FFFFFF;
}

.progress-wrapper {
    height: 18px;
    margin-bottom: 24px;
    border-radius: 10px;
    padding: 1px;
}

.online-gradient {
    background: linear-gradient(90deg, #37A6E0 0%, rgba(255, 255, 255, 0) 100%);
}

.offline-gradient {
    margin-bottom: 0;
    background: linear-gradient(90deg, #E03737 0%, rgba(224, 55, 55, 0) 100%);
}

.progress-inner {
    height: 100%;
    background: #0c1637;
    border-radius: 9px;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 0 3px;
}

.custom-progress {
    height: 12px;
    width: 100%;
}

:deep(.el-progress-bar__outer) {
    background-color: transparent;
    border-radius: 10px !important;
    height: 8px !important;
}

:deep(.el-progress-bar__inner) {
    border-radius: 10px !important;
}


.title-box{
    width: 100%;
    padding-left: 21px;
    padding-top: 20px;
    display: flex;
    align-items: center;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    line-height: normal;
    text-align: center;
    letter-spacing: 0.11em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFFFFF;
}
.title-box::before{
    content: " ";
    display: block;
    width: 4px;
    height: 15px;
    background: #37AEE1;
    margin-right: 6px;
}

:deep( .el-table--border th.el-table__cell),
:deep( .el-table td.el-table__cell) {
  border-bottom: none !important;
}
 
:deep( .el-table--border .el-table__cell ){
  border-right:none !important;
}
 
:deep( .el-table--group, .el-table--border){
  border: none !important;
}

.el-table{
    border: none !important;
    --el-table-border-color: transparent !important;
    
}

:deep( .el-table),
:deep( .el-table .el-table__header-wrapper th),
:deep(.el-table--border) {
  background-color: transparent !important;
  color: #FFF;
}
:deep( .el-table tr),
:deep( .el-table__body tr:hover > td) {
  background-color: transparent !important;
}

.js-table-header{
    background: url("@/assets/largescreen/bg-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 40px;
}

:deep( .el-table__header){
    background: url("@/assets/largescreen/bg-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 40px;
}

.close-icon-stylezdy{
    position: absolute;
    right: 30px;
    top: 8px;
    width: 20px;
    height: 20px;
    font-size: 20px;
    z-index: 999;
    color: #2585CF;
    cursor: pointer;
}

</style>