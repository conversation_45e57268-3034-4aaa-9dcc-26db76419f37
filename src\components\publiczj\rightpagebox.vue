<template>
    <div class="menu-box">
        <div class="item-box">
            <div class="item-box-item" v-for="(item,index) in menuData" :key="index" @click="toluyou(item)">
                <!-- <div class="item-box-item-icon"></div> -->
                <div class="item-box-item-text">{{  item.name }}</div>
            </div>
        </div>
        <div class="close-btn" @click="toggleMenu">
            <el-icon><ArrowRightBold /></el-icon>
        </div>
    </div>
</template>

<script setup>
// import { ArrowRightBold } from "@element-plus/icons";
import { ref, defineEmits, defineProps} from "vue";

const emits = defineEmits(["toggleMenu","toluyou"]);
const props = defineProps({
  menuData:{
    type: Boolean,
    default: false
  }
})

const toluyou = (item) => {
    emits("toluyou",item);
}

const toggleMenu = () => {
    emits("toggleMenu");
}

</script>
<style lang="scss" scoped>
  .menu-box{
    width: 30vw;
    height: calc(100vh - 60px);
    position: fixed;
    right:0vw;
    top: 60px;
    padding: 16px;
    z-index: 999;
    background-color: #FFF;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.4);
  }
  .item-box{
    display: grid;
    grid-template-columns: repeat(2, 260px);
    grid-auto-rows: 200px;
    height: 100%;
    grid-gap: 16px;
    .item-box-item{
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #F2F2F2;
      border-radius: 4px;
      cursor: pointer;
      .item-box-item-text{
        
      }
    }
  }

  .close-btn{
    width: 20px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 30vw;
    top: calc((100vh - 60px) - ((100vh - 60px)/2 - 20px));
    cursor: pointer;
    background-color: #FFF;
    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.4);
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
  }
</style>