import request from '@/axios';

export const getList = (current, size, params) =>{
    return request({
      url: '/hztech-light/gasSensor/page',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
}

// 新增
export const add = (row) =>{
    return request({
      url: '/hztech-light/gasSensor/save',
      method: 'post',
      data: row
    })
}

// 修改
export const update = ( row ) =>{
    return request({
      url: '/hztech-light/gasSensor/update',
      method: 'post',
      data: row
    })
}

// 删除
export const remove = ( params ) =>{
    return request({
      url: '/hztech-light/gasSensor/remove',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 历史数据
export const getHistoryData = ( params ) =>{
    return request({
      url: '/hztech-light/gasSensor/historyDataPage',
      method: 'get',
      params: {
        ...params
      }
    })
}

