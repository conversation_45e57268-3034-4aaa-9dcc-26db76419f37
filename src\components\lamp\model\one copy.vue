<template>
  <div class="model-one">
    <div class="dden-box">
        <span>单灯：</span>
        <div v-if="!props.lampsLanterns?.icon" class="device-box" @click="addDevice('den')">
            <el-icon><Plus /></el-icon>
        </div>
        <div v-else class="device-box" @click="addDevice('den')">
            <img style="width: 100%; height: 100%;" :src="props.lampsLanterns.icon" alt="">
        </div>
    </div>
    <div class="guanbo-box">
        <span>广播：</span>
        <div class="device-box" @click="addDevice('guanbo')">
            <el-icon><Plus /></el-icon>
        </div>
    </div>
    <div class="jk-box">
        <div class="device-box" @click="addDevice('jiankou')">
            <el-icon><Plus /></el-icon>
        </div>
        <span>：监控</span>
    </div>
    <div class="px-box">
        <div class="device-box" @click="addDevice('px')">
            <el-icon><Plus /></el-icon>
        </div>
        <span>：屏显</span>
    </div>
    <img class="img-style" src="@/assets/lightpole.png" alt="lightpole.png">
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeMount, defineEmits, defineProps, watch } from 'vue'
// import { Plus } from '@element-plus/icons';
const emit = defineEmits(['addClick'])
const props = defineProps({
    lampsLanterns:{
        type: Object,
        default: () => ({})
    },
    editdata:{
        type: Object,
        default: () => ({})
    }
})
const addDevice = (data) => {
    emit('addClick',data)
}




onBeforeMount(()=>{
    console.log('newValue222', props.editdata)
    try {
        props.lampsLanterns.icon = JSON.parse(props.editdata.lampsLanterns)?.icon
    } catch (error) {
        
    }
    // props.lampsLanterns.icon = JSON.parse(props.editdata.lampsLanterns)?.icon
})
</script>
<style lang="scss" scoped>
.model-one{
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: center;
    position: relative;
    border: 1px solid #333;
}
.img-style{
    width: 280px;
    height: 100%;
}
.dden-box{
    display: flex;
    position: absolute;
    left: 60px;
    top: 8px;
}
.guanbo-box{
    display: flex;
    position: absolute;
    left: 120px;
    top: 158px;
}
.jk-box{
    display: flex;
    position: absolute;
    left: 290px;
    top: 200px;
}
.px-box{
    display: flex;
    position: absolute;
    left: 300px;
    top: 290px;
}
.device-box{
    width: 44px;
    height: 44px;
    font-size: 40px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #333;
}
</style>