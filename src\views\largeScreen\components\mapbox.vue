<template>
    <div id="map" class="mapstyle"></div>
    <div v-if="infoWindow.visible" :style="infoWindow.style" class="custom-info-window">
        <div class="info-content">
            <img :src="infoWindow.data.img" class="lamp-img" />
            <div>
                <div class="info-label">名称</div>
                <div class="info-value">{{ infoWindow.data.name }}</div>
                <div class="info-label">编号</div>
                <div class="info-value">{{ infoWindow.data.code }}</div>
                <div class="info-label">位置</div>
                <div class="info-value">{{ infoWindow.data.position }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import mapIcon from '@/assets/largescreen/map-icon.png'
import { getLampList } from '@/api/lamp/largeScreen';
export default {
    name: 'map',
    data(){
        return {
            marker: null,
            lampList: [],
            infoWindow: {
                visible: false,
                style: {},
                data: {}
            }
        }
    },
    mounted() {
        this.getLampListfunh()
        
    },
    methods: {
        initMap(){
            this.map = new TMap.Map('map', {
                center: new TMap.LatLng(this.lampList[0].lat, this.lampList[0].lng),
                zoom: 18,
                mapStyleId: 'style1', // 改为深色地图样式
            });
            
            this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE);//移除腾讯地图比例尺
            this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);//移除腾讯地图旋转控件
            //移除腾讯地图缩放控件
            this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM);
            
            // 添加标记点位
            
            
            this.map.on('click', (evt) => {
                console.log(evt,"marker.getPosition()");
            });
        },
        
        // 添加标记点位方法
        addMarker(position, lampData) {
            // 如果已经存在标记，先移除
            // if (this.marker) {
            //     this.marker.setMap(null);
            // }
            
            // 创建新标记
            this.marker = new TMap.MultiMarker({
                map: this.map,
                styles: {
                    // 点样式
                    "normal": new TMap.MarkerStyle({
                        width: 35,  // 点宽度
                        height: 37, // 点高度
                        anchor: { x: 16, y: 32 }, // 锚点，默认为图片中心
                        src: mapIcon
                    })
                },
                geometries: [{
                    id: lampData.id,
                    position: position,
                    styleId: 'normal'
                }]
            });

            // 监听 mouseover
            this.marker.on('mouseover', (evt) => {
                const pixel = this.map.projectToContainer(position);
                this.infoWindow = {
                    visible: true,
                    style: {
                        position: 'absolute',
                        left: (pixel.x - 180) + 'px',
                        top: (pixel.y - 180) + 'px', // 上移一点避免遮挡
                        zIndex: 999
                    },
                    data: {
                        img: lampData.icon || '@/assets/largescreen/lamp-demo.png', // 替换为实际图片
                        name: lampData.poleName,
                        code: lampData.poleNo,
                        position: lampData.poleAddress
                    }
                }
            });

            // 监听 mouseout
            this.marker.on('mouseout', () => {
                this.infoWindow.visible = false;
            });
        },
        // 更新标记位置方法
        updateMarkerPosition(latLng) {
            if (this.marker) {
                this.marker.updateGeometries([{
                    id: 'marker1',
                    position: latLng,
                    styleId: 'normal'
                }]);
                
                // 输出当前坐标
                console.log('当前标记坐标：', latLng.getLat(), latLng.getLng());
            }
        },
        // 获取灯杆点位
        getLampListfunh(){
            getLampList().then(res => {
                console.log(res,"res")
                this.lampList = res.data.data
                this.initMap()
                this.lampList.forEach(item => {
                    this.addMarker(new TMap.LatLng(item.lat, item.lng), item)
                })
            })
        }
    }
}

</script>
<style lang="scss" scoped>
    body{
        margin: 0;
        padding: 0;
        overflow: hidden;
    }
    *{
        box-sizing: border-box;
    }
    .mapstyle{
        width: 100%;
        height: calc(100% - 82px);
        position: relative;
        background-color: #080e21; // 添加深色背景
        // 添加径向渐变遮罩
        -webkit-mask-image: radial-gradient(
            ellipse at center,
            rgb(0, 0, 0) 10%,   /* 中心区域不透明（内容显示） */
            rgba(0, 0, 0, 0) 100%   /* 边缘透明（内容隐藏） */
        );
        mask-image: radial-gradient(
            ellipse at center,
            rgb(0, 0, 0) 10%,
            rgba(0, 0, 0, 0) 100%
        );
    }
    .custom-info-window {
        width: 379px;
        height: 219px;
        color: #fff;
        box-shadow: 0 2px 12px #0008;
        padding: 16px;
        font-size: 15px;
        pointer-events: none;
        background-image: url("@/assets/largescreen/map-bg.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }
    .info-content {
        display: flex;
        align-items: flex-start;
        padding-top: 22px;
    }
    .lamp-img {
        width: 159px;
        height: 159px;
        object-fit: contain;
        margin-right: 12px;
        background: #fff;
    }
    .info-label {
        font-family: Source Han Sans;
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0.05em;
        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #9E9E9E;
    }
    .info-value {
        font-family: Source Han Sans;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.05em;
        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #FFFFFF;
        margin-bottom: 6px;
        width: 168px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        
    }
</style>