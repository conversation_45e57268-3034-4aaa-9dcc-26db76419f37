# 广播类型调整说明

## 调整概述

根据接口要求，将广播类型从字符串格式调整为数字格式，直接与API接口的参数对应。

## 调整内容

### 1. 卡片类型标识调整

**调整前:**
```javascript
activeCard.value = 'file'      // 文件广播
activeCard.value = 'realtime'  // 麦克风广播
```

**调整后:**
```javascript
activeCard.value = '2'  // 文件广播
activeCard.value = '1'  // 麦克风广播
```

### 2. 广播类型参数调整

**调整前:**
```javascript
// 使用常量映射
const broadcastType = chromeSDKManager.BROADCAST_TYPE.FILE  // 值为 2
const broadcastType = chromeSDKManager.BROADCAST_TYPE.MIC   // 值为 1
```

**调整后:**
```javascript
// 直接使用activeCard的值
const broadcastType = parseInt(activeCard.value)
// activeCard.value === '1' -> broadcastType = 1 (麦克风广播)
// activeCard.value === '2' -> broadcastType = 2 (文件广播)
```

### 3. 界面逻辑调整

**模板中的条件判断:**
```html
<!-- 文件广播卡片 -->
<div class="broadcast-card" 
     :class="{ active: activeCard === '2', disabled: activeCard !== '2' }" 
     @click="selectCard('2')">

<!-- 麦克风广播卡片 -->
<div class="broadcast-card" 
     :class="{ active: activeCard === '1', disabled: activeCard !== '1' }" 
     @click="selectCard('1')">

<!-- 控件禁用状态 -->
:disabled="activeCard !== '2'"  <!-- 文件广播相关控件 -->
:disabled="activeCard !== '1'"  <!-- 麦克风广播相关控件 -->
```

**JavaScript中的条件判断:**
```javascript
if (activeCard.value === '2') {
    // 文件广播逻辑
} else if (activeCard.value === '1') {
    // 麦克风广播逻辑
}
```

## API调用示例

### 文件广播
```javascript
const deviceIds = [12345, 67890]
const broadcastType = 2  // 直接使用数字
const filePath = '/audio/system.mp3'

await chromeSDKManager.quickStartBroadcast(deviceIds, broadcastType, filePath)
```

### 麦克风广播
```javascript
const deviceIds = [12345, 67890]
const broadcastType = 1  // 直接使用数字
const filePath = ''      // 麦克风广播无需文件路径

await chromeSDKManager.quickStartBroadcast(deviceIds, broadcastType, filePath)
```

## 优势

1. **直接对应**: 卡片类型直接对应API接口参数，无需额外转换
2. **简化逻辑**: 减少了常量映射的复杂性
3. **清晰明确**: 数字类型更直观地表示广播类型
4. **易于维护**: 减少了中间转换步骤，降低出错概率

## 兼容性

- ✅ 所有现有功能保持不变
- ✅ 用户界面行为保持一致
- ✅ API调用参数正确传递
- ✅ 错误处理逻辑完整

## 测试要点

1. **卡片切换**: 验证点击卡片能正确切换广播类型
2. **控件状态**: 验证相关控件的启用/禁用状态
3. **参数传递**: 验证API调用时传递的broadcastType参数正确
4. **文件广播**: 验证文件选择和参数配置功能
5. **麦克风广播**: 验证麦克风音量设置功能
6. **设备选择**: 验证设备选择和广播功能
7. **错误处理**: 验证各种错误情况的处理

## 注意事项

1. **类型转换**: 使用 `parseInt(activeCard.value)` 确保传递给API的是数字类型
2. **字符串比较**: 在模板和JavaScript中仍使用字符串进行比较（'1', '2'）
3. **默认值**: 默认选中文件广播（activeCard.value = '2'）
4. **向后兼容**: 如果需要支持旧的字符串格式，可以添加兼容性处理

这次调整使得广播类型的处理更加直接和高效，同时保持了所有现有功能的完整性。
