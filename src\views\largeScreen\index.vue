<template>
    <div class="screen-box" id="screen-main">
        <div class="top-box"> <span>智慧灯杆数据大屏</span> </div>
        <MapBox></MapBox>
        <LeftBox></LeftBox>
        <RightBox></RightBox>
    </div>
</template>
<script setup >
import MapBox from './components/mapbox.vue'
import LeftBox from './components/leftBox.vue'
import RightBox from './components/rightBox.vue'
import Adaptor from 'adaptorjs';
import { nextTick, onMounted, ref, onUnmounted } from 'vue';
let adaptor;
onMounted(() => {
  adaptor = new Adaptor({
    designHeight: 1080,
    designWidth: 1920,
    querySelector: "#app",
    extraSelectors: [],
  });
});

onUnmounted(() => {
  adaptor.destroy();
});

</script>
<style scoped lang="scss">
.screen-box{
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    background-color: #282e39;
}

.top-box{
    width: 100%;
    height: 82px;
    background: url("@/assets/largescreen/top-bg.png")no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    span{
        font-family: Source Han Sans;
        font-size: 32px;
        font-weight: bold;
        line-height: normal;
        text-align: center;
        letter-spacing: 0.11em;
        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        background: linear-gradient(180deg, #FFFFFF 0%, #7BE4EB 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }
}
</style>