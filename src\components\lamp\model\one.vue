<template>
  <div class="model-one">
    <div class="pole-container">
      <template v-for="device in currentPoleDevices" :key="device.type">
        <div class="device-container" :class="device.type" :style="getDeviceStyle(device)">
          <div class="device-button" @click="addDevice(device.type)">
            <template v-if="device.type === 'den' && props.lampsLanterns?.icon">
              <span class="device-name">{{ getDeviceName(device.type) }}</span>
              <img style="width: 24px; height: 24px;" :src="props.lampsLanterns.icon" alt="">
            </template>
            <template v-else-if="device.type === 'jiankou' && props.lampsLanterns?.icon">
              <span class="device-name">{{ getDeviceName(device.type) }}</span>
              <img style="width: 24px; height: 24px;" :src="props.lampsLanterns.icon" alt="">
            </template>
            <template v-else>
              <template v-if="device.direction === 'left'">
                  <span class="device-name">{{ getDeviceName(device.type) }}</span>
                  <div class="plus-icon">
                      <el-icon><Plus /></el-icon>
                  </div>
              </template>
              <template v-if="device.direction === 'right'">
                  <div class="plus-icon plus-icon-right">
                      <el-icon><Plus /></el-icon>
                  </div>
                  <span class="device-name device-name-right">{{ getDeviceName(device.type) }}</span>
              </template>
            </template>
          </div>
        </div>
      </template>
      <img class="img-style" src="@/assets/lightpole.png" alt="lightpole.png">
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeMount, defineEmits, defineProps, computed } from 'vue'
import { poleDeviceConfig } from '@/config/poleConfig'

const emit = defineEmits(['addClick'])

const props = defineProps({
    lampsLanterns: {
        type: Object,
        default: () => ({})
    },
    editdata: {
        type: Object,
        default: () => ({})
    },
    poleType: {
        type: Number,
        default: 1
    }
})

const deviceNameMap = {
  den: '单灯',
  guanbo: '广播',
  jiankou: '监控',
  px: '屏显'
}

const getDeviceName = (type) => {
  return deviceNameMap[type] || type
}

const currentPoleDevices = computed(() => {
  return poleDeviceConfig[props.poleType]?.devices || []
})

const getDeviceStyle = (device) => {
  // 将position对象转换为相对单位的样式
  const { top, left } = device.position
  return {
    '--device-top': `${top}%`,
    '--device-left': `${left}%`,
  }
}

const addDevice = (type) => {
  emit('addClick', type)
}

onBeforeMount(() => {
  try {
    if (props.editdata?.lampsLanterns) {
      props.lampsLanterns.icon = JSON.parse(props.editdata.lampsLanterns)?.icon
    }
  } catch (error) {
    console.error('解析灯具数据失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.model-one {
  width: 50%;
  height: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  border-radius: 8px;
  background: #F4F4F4;
}

.pole-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.img-style {
  width: 280px;
  height: 100%;
  object-fit: contain;
}

.device-container {
  position: absolute;
  transform: translate(-50%, -50%);
  top: var(--device-top);
  left: var(--device-left);
  display: flex;
  align-items: center;
  z-index: 1;
}

.device-button {
  width: 100px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
  display: flex;
  align-items: center;
  height: 32px;
  .device-name {
    width: 80%;
    height: 26.67px;
    margin-right: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    font-family: Source Han Sans;
    font-size: 12px;
    font-weight: bold;
    line-height: 180%;
    text-transform: uppercase;
    letter-spacing: 0.08em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFFFFF;
    padding-left: 19px;
    background: url("/img/addsb-bg.png") no-repeat;
    background-size: contain;
  }
  .device-name-right{
    background: url("/img/addsb-bgS.png") no-repeat;
    background-size: contain;
    position: absolute;
    left: 31px;
  }

  .plus-icon {
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.62);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 32.16px;
    height: 32px;
    right: 7px;
    top: 0;
    .el-icon {
      font-size: 16px;
      color: white;
    }
  }
  .plus-icon-right{
    left: 0;
    top: 0;
  }
}

// 当设备已选择时的样式
.device-button img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}



</style>