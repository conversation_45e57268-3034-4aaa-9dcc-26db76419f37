# 实时广播页面功能实现

## 功能概述

在 `src/views/broadcast/realTimeBroadcasting.vue` 中实现了完整的广播控制功能，包括文件广播和麦克风广播两种模式。

## 实现的功能

### 1. 广播控制逻辑

#### 开始广播
- 用户选择设备后点击"开始广播"按钮
- 根据当前激活的卡片类型调用相应的广播接口：
  - **文件广播** (`activeCard === '2'`): 调用 `chromeSDKManager.quickStartBroadcast(deviceIds, 2, filePath)`
  - **麦克风广播** (`activeCard === '1'`): 调用 `chromeSDKManager.quickStartBroadcast(deviceIds, 1)`

#### 停止广播
- 用户再次点击广播按钮（此时显示为"停止广播"）
- 调用 `chromeSDKManager.quickStopBroadcast()` 停止广播

### 2. 广播类型处理

#### 文件广播 (`activeCard === '2'`)
- **文件选择验证**: 用户必须先选择文件（下拉选择或上传本地文件）
- **支持的文件类型**: MP3, WAV, M4A
- **文件大小限制**: 最大50MB
- **传递参数**:
  - 设备ID列表
  - 广播类型：2（文件广播）
  - 文件路径
  - 循环模式（无限循环或指定次数）
  - 广播音量

#### 麦克风广播 (`activeCard === '1'`)
- **直接开始**: 无需选择文件，可以直接开始广播
- **传递参数**:
  - 设备ID列表
  - 广播类型：1（麦克风广播）
  - 麦克风音量

### 3. 设备选择功能

- **设备表格选择**: 支持多选设备进行广播
- **选择验证**: 开始广播前验证是否选择了设备
- **设备计数显示**: 显示已选择的设备数量
- **设备状态**: 实时显示设备在线/离线状态

### 4. 用户界面改进

#### 播放按钮状态
- **禁用状态**: 未选择设备时按钮显示为禁用状态
- **状态切换**: 开始广播 ↔ 停止广播
- **视觉反馈**: 按钮颜色和图标根据状态变化

#### 文件上传功能
- **本地文件选择**: 点击"选择本地文件"按钮选择音频文件
- **文件验证**: 验证文件类型和大小
- **成功反馈**: 文件选择成功后显示文件名

### 5. 错误处理和用户反馈

- **参数验证**: 开始广播前验证必要参数
- **错误提示**: 使用 ElMessage 显示错误信息
- **成功提示**: 广播开始/停止成功时显示提示
- **控制台日志**: 详细的操作日志用于调试

### 6. 卡片切换逻辑

- **自动停止**: 切换广播类型时自动停止当前广播
- **状态重置**: 切换后重置相关状态

## 技术实现细节

### API 调用
```javascript
// 开始广播
await chromeSDKManager.quickStartBroadcast(deviceIds, broadcastType, filePath)

// 停止广播
await chromeSDKManager.quickStopBroadcast()
```

### 广播类型参数
```javascript
// 直接使用数字作为广播类型参数
const broadcastType = parseInt(activeCard.value)
// activeCard.value === '1' -> 麦克风广播
// activeCard.value === '2' -> 文件广播
```

### 设备选择处理
```javascript
const handleSelectionChange = (selection) => {
    selectedDevices.value = selection
    console.log('选中的设备:', selection)
}
```

### 文件上传处理
```javascript
const handleFileChange = (event) => {
    const file = event.target.files[0]
    // 文件类型和大小验证
    // 更新选择状态
}
```

## 使用说明

1. **选择广播类型**: 点击"文件广播"或"麦克风广播"卡片
2. **选择设备**: 在设备列表中勾选要广播的设备
3. **配置参数**:
   - 文件广播: 选择文件、设置循环模式和音量
   - 麦克风广播: 设置麦克风音量
4. **开始广播**: 点击播放按钮开始广播
5. **停止广播**: 再次点击播放按钮停止广播

## 注意事项

1. **文件路径处理**: 当前实现中上传的文件需要先上传到服务器获取实际路径
2. **设备ID获取**: 从设备对象中提取 `deviceId` 或 `id` 字段
3. **错误处理**: 所有API调用都包含完整的错误处理逻辑
4. **状态管理**: 广播状态与UI状态保持同步

## 后续优化建议

1. **文件上传**: 实现真实的文件上传到服务器功能
2. **进度显示**: 添加广播进度和时间显示
3. **音量控制**: 实现实时音量调节
4. **设备分组**: 支持按分组选择设备
5. **历史记录**: 保存广播历史记录
