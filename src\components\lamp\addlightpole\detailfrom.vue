<template>
  <div class="title">设备说明</div>
  <div class="detail">
    <div class="img-box">
      <img class="img-style" :src="props.activItemData.icon" alt="" />
    </div>
    <div class="content-box">
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        label-position="top"
        status-icon >
        <el-form-item label="设备名称：" prop="luminaireName">
          <el-input class="inputStyles" v-model="ruleForm.luminaireName" />
        </el-form-item>
        <el-form-item label="设备编号：" prop="luminaireNo">
          <el-input class="inputStyles" v-model="ruleForm.luminaireNo" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue';

let props = defineProps({
  activItemData: {
    type: Number,
  },
});

const ruleForm = ref({});
const rules = ref({
  luminaireName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 20, message: "名称长度在2-20个字符之间", trigger: "blur" },
  ],
  luminaireNo: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
});

const ruleFormRef = ref(null);
const detailSubmitForm = async () => {
  console.log('submit!', ruleFormRef.value);
  let row = {};
  await ruleFormRef.value.validate((valid, fields) => {
    console.log('submit!', valid, fields);
    ruleForm.value.luminaireModel = props.activItemData.id;
    row = { success: valid, data: ruleForm.value, icon: props.activItemData.icon };
    console.log('submit222222', row);
  });
  return row;
};

defineExpose({
  detailSubmitForm,
});
</script>
<style lang="scss" scoped>
.detail {
  width: 100%;
  height: 280px;
  display: flex;
  justify-content: space-around;
}
.title {
  margin-bottom: 24px;
}
.img-box {
  width: 280px;
  height: 280px;
  background-color: #f2f2f2;
  .img-style {
    width: 100%;
    height: 100%;
  }
}
.content-box {
  width: 280px;
  height: 100%;
  overflow-y: auto;
}

.indent {
  text-indent: 32px;
  text-align: left;
}
:deep(.el-input) {
  .el-input__wrapper {
    background-color: #F7F8FA;
    border: none;
    box-shadow: none;
    padding: 12px;
    
    &.is-focus {
      box-shadow: none;
    }

    &.is-readonly {
      cursor: pointer;
    }
  }
  
  input {
    height: 20px;
    font-size: 14px;
    &::placeholder {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 350;
      line-height: normal;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #A2AAB3;
    }
  }
}
</style>
