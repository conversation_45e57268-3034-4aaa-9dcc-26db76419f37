<template>
    <div class="title">设备说明</div>
    <div class="detail">
      <div class="img-box">
          <img class="img-style" :src="props.activItemData.icon" alt="">
      </div>
      <div class="content-box">
        <p class="indent">
            {{ props.activItemData.info }}
          </p>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, defineProps } from 'vue'
  
  let props = defineProps({
      activItemData: {
          type: Number,
      },
  });
  
  </script>
  <style lang="scss" scoped>
  .detail{
      width: 100%;
      height: 460px;
      display: flex;
      justify-content: space-around;
  }
  .title{
      margin-bottom: 24px;
  }
  .img-box{
      width: 280px;
      height: 460px;
      background-color: #F2F2F2;
      .img-style{
          width: 100%;
          height: 100%;
      }
  }
  .content-box{
      width: 280px;
      height: 100%;
      overflow-y: auto;
      text-align: center;
  }
  
  .indent{
      text-indent: 32px;
      text-align: left;
  }
  </style>