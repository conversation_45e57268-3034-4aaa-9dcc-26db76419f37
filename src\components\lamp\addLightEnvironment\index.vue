<template>
  <div class="peidianxiang">
    <el-form v-show="!showMap" :model="formData" ref="formRef" :rules="rules" label-width="88px">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
      </el-form-item>
      <el-form-item label="物联网ID" prop="deviceId">
        <el-input v-model="formData.deviceId" placeholder="请输入物联网ID" />
      </el-form-item>
      <el-form-item label="设备编号" prop="deviceNo">
        <el-input v-model="formData.deviceNo" placeholder="请输入设备编号" />
      </el-form-item>
      <el-form-item label="设备位置" prop="address">
        <el-input 
          v-model="formData.address" 
          placeholder="请选择位置" 
          readonly
          @click="showMapSelect"
        />
      </el-form-item>
      <div class="from-title">
        备注:
      </div>
      <el-form-item prop="remark" label-position="top">
        <el-input
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入备注"
          :rows="4"
        />
      </el-form-item>
    </el-form>

    <div style="width: 200%;">
      <Adress
        v-if="showMap"
        @changeAddress="handleLocationSelect"
      />
    </div>

    <div class="btn-box" v-show="!showMap">
      <el-button color="#165DFF" type="primary" @click="submitForm">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits } from 'vue'
import Adress from '../map/adress.vue';
import { ControllerType, add } from "@/api/lamp/switchBox.js"

const emit = defineEmits(['submit', 'handleClose'])

const formRef = ref(null)
const formData = ref({
  deviceName: '',
  deviceId: '',
  deviceNo: '',
  address: '',
  remark: ''
})

const showMap = ref(false)

const showMapSelect = () => {
  showMap.value = true
}

const hideMapSelect = () => {
  showMap.value = false
}

const handleLocationSelect = (address) => {
  formData.value.lat = address.lat
  formData.value.lng = address.lng
  formData.value.address = address.store_address
  console.log("address", address);
  // formData.value.location = address
  hideMapSelect()
}

const rules = {
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  deviceId: [
    { required: true, message: '请输入物联网ID', trigger: 'blur' }
  ],
  deviceNo: [
    { required: true, message: '请输入设备编号', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请选择位置', trigger: 'blur' }
  ]
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', formData.value)
    }
  })
}

onMounted(()=>{

})
</script>

<style lang="scss" scoped>
.peidianxiang{
    width: 100%;
    height: 100%;
    padding: 10px;
    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 24px;
        display: flex;
        align-items: center;
      }
    }
    
    :deep(.el-form-item__label) {
      // padding: 0 0 8px 0;
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      text-transform: uppercase;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #333333;
      display: flex;
      align-items: center;
    }
    
    :deep(.el-input) {
      .el-input__wrapper {
        background-color: #F7F8FA;
        border: none;
        box-shadow: none;
        padding: 12px;
        
        &.is-focus {
          box-shadow: none;
        }

        &.is-readonly {
          cursor: pointer;
        }
      }
      
      input {
        height: 20px;
        font-size: 14px;
        &::placeholder {
          color: #C9CDD4;
        }
      }
    }
    
    :deep(.el-textarea) {
      .el-textarea__inner {
        background-color: #F7F8FA;
        border: none;
        padding: 12px;
        font-size: 14px;
        
        &::placeholder {
          color: #C9CDD4;
        }
      }
    }
}

.btn-box {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  
  .el-button {
    width: 160px;
    height: 40px;
    border-radius: 4px;
  }
}
.from-title{
  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #333333;
  margin-bottom: 8px;
}
</style>