<template>
  <div class="container">
    <div class="menu-box">
      <div class="menu-list" @click="toggleMenu">
        <img class="list-icon" src="@/assets/img/menu-icon.png" alt="">
      </div>
      <div class="menu-title">安防可视化平台</div>
    </div>
    <div class="personal">
      <div class="personal-box">
        <div class="personal-box-img">
          <!-- <img class="img-box" src="../../assets/img/yzm.png" alt=""> -->
          李
        </div>
      </div>
    </div>
  </div>


  <transition 
    name="fade"
    enter-active-class="animate__animated animate__fadeInLeft"
    leave-active-class="animate__animated animate__fadeOutLeft">
      <div v-if="showMenu" class="menu-left-box">
        <div class="add-box">
          <el-icon><Plus /></el-icon>
        </div>
        <div class="all-box">
          安防可视化
        </div>
        <div class="item-box">
          <div class="item-box-item" @click="toluyou">
            <!-- <div class="item-box-item-icon"></div> -->
            <div class="item-box-item-text">灯杆系统</div>
          </div>
          <div class="item-box-item">
            <!-- <div class="item-box-item-icon"></div> -->
            <div class="item-box-item-text">系统模块</div>
          </div>
          <div class="item-box-item">
            <!-- <div class="item-box-item-icon"></div> -->
            <div class="item-box-item-text">门禁</div>
          </div>
          <div class="item-box-item">
            <!-- <div class="item-box-item-icon"></div> -->
            <div class="item-box-item-text">锁</div>
          </div>
          <div class="item-box-item">
            <!-- <div class="item-box-item-icon"></div> -->
            <div class="item-box-item-text">梯控</div>
          </div>
          <div class="item-box-item">
            <!-- <div class="item-box-item-icon"></div> -->
            <div class="item-box-item-text">访客</div>
          </div>
        </div>
      </div>
  </transition>


</template>

<script setup>
import { ref } from "vue";
// import { Plus } from "@element-plus/icons";
import { useRouter } from 'vue-router';


const showMenu = ref(false)
const router = useRouter();
const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const toluyou = () => {
  console.log('跳转')
  showMenu.value = !showMenu.value
  router.push('/lamp/index');
}

</script>
<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background: #FFFFFF;
    box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.08);
  }
  .menu-box{
    display: flex;
    .menu-list{
      .list-icon{
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        margin-right: 16px;
        cursor: pointer;
      }
    }
    .menu-title{
      display: flex;
      align-items: center;
    }
  }
  .personal{
    .personal-box{
       
    }
  }
  .personal-box-img{
    width: 42px;
    height: 42px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #3670F7;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
    .img-box{
      width: 100%;
      height: 100%;
    }
  }


  .menu-left-box{
    width: 30vw;
    height: calc(100vh - 60px);
    position: fixed;
    left: 0vw;
    top: 60px;
    padding: 16px;
    z-index: 999;
    background-color: #FFF;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    box-shadow: 5px 0 5px rgba(0, 0, 0, 0.1);
  }

  .add-box{
    font-size: 20px;
    margin-bottom: 16px;
    cursor: pointer;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .all-box{
    width: 100%;
    height: 168px;
    border-radius: 4px;
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #F2F2F2;
    cursor: pointer;
  }

  .item-box{
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: calc(100% - 220px);
    grid-gap: 16px;
    .item-box-item{
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #F2F2F2;
      border-radius: 4px;
      cursor: pointer;
      .item-box-item-text{
        
      }
    }
  }

</style>
