<template>
  <el-row class="row-container" >
    <el-col :span="4" class="left-panel">
      <el-tree
        :data="groupList"
        node-key="id"
        :props="{
          label: 'poleName',
          children: 'children'
        }"
        highlight-current
        accordion
        :expand-on-click-node="false"
        @node-click="onGroupClick"
        class="group-tree">
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <i v-if="data.screens" class="el-icon-folder" style="color:#409EFF;margin-right:4px;"></i>
            <i v-else class="el-icon-video-camera" style="color:#67C23A;margin-right:4px;"></i>
            <el-tooltip
              class="item"
              effect="dark"
              :content="node.label"
              :disabled="node.label.length<=9"
              placement="top-start"
            >
              <span class="node-label">
                {{ node.label.length > 8 ? node.label.substring(0, 3) + '...' : node.label }}
              </span>
            </el-tooltip>
          </span>
        </template>
      </el-tree>
    </el-col>
    <el-col :span="20" class="right-panel">
      <div class="btn-box">
        <el-radio-group v-model="radio2" @change="handleScreenModeChange" fill="#2E74FF">
          <el-radio-button
            v-for="item in screenModes"
            :key="item.value"
            :label="item.value"
          >{{ item.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="screen-grid">
        <HzwlPlayer 
          ref="player" 
          :videoUrl="url" 
          :playerId="playerId"
          :multiScreenOne="multiScreenOne"
          :multiScreenTwo="multiScreenTwo"
          :multiScreenFour="multiScreenFour"
          :multiScreenSix="multiScreenSix"
          :multiScreenNine="multiScreenNine"
          :multiScreenSixteen="multiScreenSixteen"
          @player-click="handlePlayerClick"
        />
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getMonitorList, getVideoMonitor } from '@/api/lamp/monitor'
import { ElMessage } from 'element-plus'

const url = ref('')
const player = ref(null)
const playerId = ref('HzwlPlayer')


const screenModes = [
  { label: '单分屏', value: 'one', var: 'multiScreenOne' },
  // { label: '两分屏', value: 'two', var: 'multiScreenTwo' },
  { label: '四分屏', value: 'four', var: 'multiScreenFour' },
  { label: '六分屏', value: 'six', var: 'multiScreenSix' },
  { label: '九分屏', value: 'nine', var: 'multiScreenNine' },
  { label: '十六分屏', value: 'sixteen', var: 'multiScreenSixteen' }
]

const radio2 = ref('nine') // 默认九分屏

const multiScreenOne = ref(false)
const multiScreenTwo = ref(false)
const multiScreenFour = ref(false)
const multiScreenSix = ref(false)
const multiScreenNine = ref(true)
const multiScreenSixteen = ref(false)

function handleScreenModeChange(val) {
  multiScreenOne.value = val === 'one'
  multiScreenTwo.value = val === 'two'
  multiScreenFour.value = val === 'four'
  multiScreenSix.value = val === 'six'
  multiScreenNine.value = val === 'nine'
  multiScreenSixteen.value = val === 'sixteen'
}

function convertTreeData(list) {
  return list.map(group => ({
    id: group.id,
    poleName: group.poleName || group.name, // 兼容不同字段
    children: (group.children || []).map(screen => ({
      id: screen.monitoringNumber || screen.id,
      poleName: screen.monitoringName || screen.name,
      monitoringNumber: screen.monitoringNumber || screen.id
    }))
  }))
}

// 使用转换后的数据
const groupList = ref([])

function onGroupClick(data) {
  // 只允许点击子级节点（没有 screens 的节点）
  if (!data.children) {
    console.log(data,"data")
    getVideoMonitor({deviceNo:data.monitoringNumber}).then(res => {
      console.log(res,"res")
      let videourl = res.data.data
      videourl = atob(atob(videourl))
      console.log(videourl,"videourl")
      url.value = videourl
      handlePlay()
    })
    return
    
  }
}

// 播放
const handlePlay = async (index) => {
  try {
    await player.value.onPlayer()
  } catch (error) {
    // 使用Element Plus的消息提示
    ElMessage.warning(error.message || '请先选择要播放的播放器')
  }
}

const handlePlayerClick = (data) => {
  console.log(data,"data")
}

const getMonitorListData = async () => {
  const res = await getMonitorList()
  groupList.value = convertTreeData(res.data.data)
  console.log(groupList.value,"res")
}

onMounted(() => {
  getMonitorListData()
  handleScreenModeChange(radio2.value)
})
</script>

<style scoped>
*{
  box-sizing: border-box;
}
.row-container {
  width: 100%;
  padding: 8px;
  height: calc(100vh - 66px);
  background: #f5f6fa;
}
.left-panel {
  height: calc(100vh - 66px);
  min-width: 220px;
  max-width: 300px;
  padding: 0;
}
.left-card {
  height: 100%;
  background: #fff;
  border-radius: 0 8px 8px 0;
  box-shadow: 2px 0 8px #f0f1f2;
  border: none;
}
.group-tree {
  height: 100%;
  background: #fff;
  padding: 8px 0 8px 0;
}
.custom-tree-node {
  display: flex;
  align-items: center;
  font-size: 15px;
}
.node-label {
  font-weight: 500;
  color: #333;
}
.right-panel {
  height: calc(100vh - 66px);
  /* padding: 24px 24px 0 0; */
  /* background: #000; */
}
.box-card-video {
  height: 100%;
  /* background: #fff; */
  /* border-radius: 8px; */
  box-shadow: 0 2px 12px #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
}
.screen-grid {
  width: 100%;
  height: calc(100% - 40px);
  border-radius: 8px;
  align-items: center;
  justify-items: center;
}

/* el-tree 选中节点背景色和文字色 */
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #2E74FF !important;
  color: #fff !important;
}
:deep(.el-tree-node.is-current > .el-tree-node__content .node-label) {
  color: #fff !important;
}
:deep(.el-tree-node.is-current > .el-tree-node__content i) {
  color: #fff !important;
}
.btn-box{
  width: 100%;
  margin: 0px 0 16px ;
  display: flex;
  justify-content: flex-end;
}
</style>

