<template>
  <div class="avue-logo">
    <transition name="fade">
      <span v-if="getScreen(isCollapse)" class="avue-logo_subtitle" key="0">
        {{ website.logo }}
      </span>
    </transition>
    <transition-group name="fade">
      <template v-if="getScreen(!isCollapse)">
        <div class="avue-logo_title_box">
          <span class="avue-logo_title qianm" key="1">智能</span>
          <span class="avue-logo_title" key="1">{{ website.indexTitle }}</span>
        </div>
      </template>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'logo',
  data() {
    return {};
  },
  created() {},
  computed: {
    ...mapGetters(['isCollapse']),
  },
  methods: {},
};
</script>
<style lang="scss" scope>
.avue-logo_title_box{
  display: flex;
  justify-content: center;
  align-items: center;
}
  .qianm{
    width: 50px;
    height: 31px;
    border-radius: 5px;
    opacity: 1;
    color: #fff;
    line-height: 31px;
    margin-right: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #1178FD;
  }
</style>
