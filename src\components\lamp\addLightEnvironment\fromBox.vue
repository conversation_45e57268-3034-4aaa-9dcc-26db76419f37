<template>
  <div class="peidianxiang">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px" label-position="top">
      <div class="switch-box">
        <span>灯具执行状态</span>
        <el-switch
          v-model="formData.status"
          active-color="#165DFF"
          inactive-color="#165DFF"
          :active-value="1"
          :inactive-value="2"
          size="large"
        ></el-switch>
      </div>
      <el-form-item label="亮度值" prop="brightness">
        <el-input class="input-style" v-model="formData.brightness" placeholder="请输入0-100" />
      </el-form-item>
    </el-form>
    <div class="suoming">
      这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明这是一个说明
    </div>
    <div class="btn-box">
      <el-button color="#165DFF" type="primary" @click="submitForm">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits } from 'vue'
import { ControllerType, add } from "@/api/lamp/switchBox.js"

const emit = defineEmits(['submit', 'handleClose'])

const formRef = ref(null)
const formData = ref({
  status: false,
  brightness: ''
})

const rules = {
  brightness: [
    { required: true, message: '请输入亮度值', trigger: 'blur' },
    { pattern: /^(100|[1-9]?\d)$/, message: '请输入0-100之间的数值', trigger: 'blur' }
  ]
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', formData.value)
    }
  })
}

const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

onMounted(()=>{

})
</script>

<style lang="scss" scoped>
.peidianxiang{
    width: 100%;
    height: 100%;
    :deep(.el-form) {

    }
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      font-family: Source Han Sans;
      font-size: 18px;
      font-weight: 500;
      line-height: normal;
      text-transform: uppercase;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #333333;
    }
    
    :deep(.el-input) {
      width: 100%;
      background-color: #F7F8FA;
      border: none;
    }
    
    :deep(.el-button) {
      margin-right: 10px;
    }
}
.btn-box{
  margin: 0;
}

.switch-box{
  display: flex;
  justify-content: space-between;
  span{
    font-family: Source Han Sans;
    font-size: 18px;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
  }
}
.suoming{
  margin: 20px 0 20px;
  font-family: Source Han Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #A2AAB3;
}
</style>