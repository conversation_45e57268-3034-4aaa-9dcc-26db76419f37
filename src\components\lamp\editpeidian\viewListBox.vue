<template>
  <div class="poleList">
    <div class="search-box">
        <span>灯杆名称：</span>
        <el-input v-model="search.poleName" placeholder="请输入名称" style="width: 300px; margin-right: 24px;"></el-input>
        <span>灯杆编号：</span>
        <el-input v-model="search.poleNo" placeholder="请输入编号" style="width: 300px; margin-right: 24px;"></el-input>
        <el-button type="primary" icon="Search" @click="searchList">搜索</el-button>
    </div>
    <el-table 
    :data="tableData"
    height="250" 
    border
    stripe
    style="width: 100%; height: 82%;">
        <el-table-column type="index" width="60" label="序号" :index="(index) => index + 1" />
        <el-table-column
            prop="poleNo" 
            label="灯杆编号"
            show-overflow-tooltip/>
        <el-table-column
            prop="poleName"
            label="灯杆名称" 
            show-overflow-tooltip/>
        <el-table-column
            prop="poleAddress"
            label="灯杆位置" 
            show-overflow-tooltip/>
    </el-table>
    <div class="paging-box">
      <el-pagination
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        background
        layout="prev, pager, next"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
import {
  leLightpage,
} from '@/api/lamp/switchBox.js';

const props = defineProps({
  ViewData: {
    type: Object,
    default: () => ({})
  }
})

const tableData = ref([])

const page = ref(
  {
    pageSize: 10,
    currentPage: 1,
    total: 100,
  },
)
const search = ref({
  poleName: '',
  poleNo: ''
})

const searchList = () => {
  getList()
}

const handleSizeChange = (val) => {
  page.value.pageSize = val
  getList()
}

const handleCurrentChange=(val)=>{
  console.log(`current page: ${val}`)
  page.value.currentPage = val
  getList()
}

const getList = () => {
  console.log(search.value)
  console.log(props.ViewData,"11111111111111111111111")
  search.value.centralizedNo = props.ViewData.centralizedNo
  search.value.loopNumber = props.ViewData.loopNumber
  let row = {
    ...search.value,
    current: page.value.currentPage, 
    size: page.value.pageSize
  }
  leLightpage(row).then(res => {
    console.log(res,"deviceNodeviceNodeviceNo")
    tableData.value = res.data.data.records
    page.value.total = res.data.data.total
    page.value.currentPage = res.data.data.current
  })
}

const getrightListData = () => {
  console.log(props.bindData, 'bindData');

};

onMounted(() => {
  console.log(props.ViewData,"ViewData")
  getList()
})
</script>
<style lang="scss" scoped>
.poleList{
    width: 100%;
    height: 560px;
}
</style>

<style lang="scss" scoped>
.search-box{
    margin-bottom: 16px;
}
.paging-box{
  display: flex;
  justify-content: flex-end;
  margin: 24px 0;
}
.menu-btn{
  position: fixed;
  right: 0;
  bottom: calc(50% - 130px);
  width: 52px;
  height: 262px;
  font-size: 20px;
  line-height: 52px;
  writing-mode: vertical-lr;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  cursor: pointer;
}


.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}
</style>