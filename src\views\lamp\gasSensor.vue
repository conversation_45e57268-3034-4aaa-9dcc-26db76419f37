<template>
  
  <div class="container">
    <!-- 搜索栏 -->
    <div class="search-bar" v-if="!isDetailView && !showHistoryData">
      <div style="display: flex; align-items: center; gap: 16px;">
        <div class="search-item">
          <span class="label">设备名称:</span>
          <el-input v-model="searchForm.deviceName" placeholder="请输入名称" style="width: 300px; margin-right: 24px;" />
        </div>
        <div class="search-item">
          <span class="label">编号:</span>
          <el-input v-model="searchForm.deviceNo" placeholder="请输入编号" style="width: 300px; margin-right: 24px;" />
        </div>
      </div>
      <el-button color="#165DFF" icon="Search" type="primary" @click="handleSearch">查 询</el-button>
      <el-button icon="Refresh" @click="handleReset">重 置</el-button>
    </div>
    <div v-if="!isDetailView && !showHistoryData" class="content">
      
      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div class="left">列表</div>
        <div >
          <el-button style="width: 114px;" color="#165DFF" type="primary" @click="handleAdd">新 增</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table 
        :data="tableData" 
        style="width: 100%; height: calc(100% - 100px);"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="50" /> -->
        <el-table-column prop="deviceName" label="设备名称" />
        <el-table-column prop="deviceId" label="物联网ID" />
        <el-table-column prop="deviceNo" label="设备编号" />
        <el-table-column prop="location" label="设备位置" width="240" show-overflow-tooltip  />
        <el-table-column prop="remark" label="备注" width="240" show-overflow-tooltip />
        <!-- <el-table-column prop="createTime" label="创建时间" /> -->
        <el-table-column label="操作" width="230">
          <template #default="scope">
            <el-button color="#165DFF" plain type="primary" link @click="handleHistoryData(scope.row)">历史数据</el-button>
            <el-button color="#165DFF" plain type="primary" link @click="handleView(scope.row)">详情</el-button>
            <el-button color="#165DFF" plain type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button color="#165DFF" plain type="primary" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="page.currentPage"
          v-model:page-size="page.pageSize"
          background
          layout="prev, pager, next"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <div v-else-if="showHistoryData" class="content">
      <div class="detail-header">
        <el-button color="#165DFF" type="primary" @click="backToList">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
      <historyData :data="currentDetailData" />
    </div>
    <div v-else class="content">
      <div class="detail-header">
        <el-button color="#165DFF" type="primary" @click="backToList">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
      <GasSensorDetail
        :data="currentDetailData"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑' : '新增'"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="custom-dialog"
    >
      <div v-if="!showMap">
        <el-form
          ref="formRef"
          :model="formData"
          label-width="100px"
          :rules="rules"
        >
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
          <el-form-item label="物联网ID" prop="deviceId">
            <el-input v-model="formData.deviceId" placeholder="请输入物联网ID" />
          </el-form-item>
          <el-form-item label="设备编号" prop="deviceNo">
            <el-input v-model="formData.deviceNo" placeholder="请输入设备编号" style="width: 100%" />
          </el-form-item>
          <el-form-item label="设备位置" prop="location">
            <el-input 
              v-model="formData.location" 
              placeholder="请输入设备位置" 
              readonly
              @click="handleShowMap"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark" label-position="top">
            <el-input type="textarea" rows="4" v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </div>
      <div v-else class="map-container">
        <Adress @changeAddress="handleAddressSelect" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" color="#165DFF" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template> 

<script setup>
import { ref, onMounted } from 'vue'
import GasSensorDetail from '@/components/lamp/gasSensor/GasSensorDetail.vue'
import historyData from '@/components/lamp/gasSensor/historyData.vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import Adress from "@/components/lamp/map/adress.vue"
import { ElMessage, ElMessageBox } from 'element-plus'
import { getList, add, remove, update, getHistoryData } from '@/api/lamp/gasSensor.js'

const isDetailView = ref(false)
const tableData = ref([])
const searchForm = ref({
  deviceName: '',
  deviceNo: ''
})
const page = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const currentDetailData = ref({})
const selectedRows = ref([])

const dialogVisible = ref(false)
const formRef = ref(null)
const isEdit = ref(false)
const editId = ref(null)

const initFormData = {
  deviceName: '',
  deviceId: '',
  deviceNo: '',
  location: '',
  lat: '',
  lng: '',
  remark: ''
}

const formData = ref({ ...initFormData })

const rules = {
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  deviceId: [{ required: true, message: '请输入物联网ID', trigger: 'blur' }],
  deviceNo: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
  location: [{ required: true, message: '请输入设备位置', trigger: 'blur' }]
}

const showMap = ref(false)
const showHistoryData = ref(false)

const getTableData = async () => {
  try {
    const params = {
      ...searchForm.value
    }
    const res = await getList(page.value.currentPage, page.value.pageSize, params)
    if (res.data.code === 200) {
      tableData.value = res.data.data.records || []
      page.value.total = res.data.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

const handleSearch = () => {
  page.value.currentPage = 1
  getTableData()
}

const handleReset = () => {
  searchForm.value = {
    number: '',
    name: ''
  }
  handleSearch()
}

const handleAdd = () => {
  isEdit.value = false
  formData.value = { ...initFormData }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  editId.value = row.id
  formData.value = {
    deviceName: row.deviceName,
    deviceId: row.deviceId,
    deviceNo: row.deviceNo,
    location: row.location,
    lat: row.lat,
    lng: row.lng,
    remark: row.remark || ''
  }
  dialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除该设备吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await remove({ id: row.id })
      if (res.data.code === 200) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        getTableData() // 刷新表格数据
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleView = (row) => {
  currentDetailData.value = {
    ...row,
    updateTime: new Date().toLocaleString()
  }
  isDetailView.value = true
}

const handleHistoryData = (row) => {
  currentDetailData.value = row
  showHistoryData.value = true
}

const backToList = () => {
  isDetailView.value = false
  showHistoryData.value = false
}

const handleSizeChange = (val) => {
  page.value.pageSize = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value.currentPage = val
  getTableData()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = { ...formData.value }
        let res
        if (isEdit.value) {
          submitData.id = editId.value
          res = await update(submitData)
        } else {
          res = await add(submitData)
        }
        
        if (res.data.code === 200) {
          ElMessage({
            type: 'success',
            message: `${isEdit.value ? '编辑' : '新增'}成功`
          })
          dialogVisible.value = false
          getTableData() // 刷新表格数据
        } else {
          // ElMessage.error(res.msg || `${isEdit.value ? '编辑' : '新增'}失败`)
        }
      } catch (error) {
        console.error(`${isEdit.value ? '编辑' : '新增'}失败:`, error)
        // ElMessage.error(`${isEdit.value ? '编辑' : '新增'}失败`)
      }
    }
  })
}

const handleShowMap = () => {
  showMap.value = true
}

const handleAddressSelect = (address) => {
  console.log("address》》》》》》》》", address);
  formData.value.lat = address.lat
  formData.value.lng = address.lng
  formData.value.location = address.store_address
  showMap.value = false
}

onMounted(() => {
  getTableData()
})
</script>

<style lang="scss" scoped>
*{
  box-sizing: border-box;
}

.container {
  padding: 16px;
  height: 100%;
  .content {
    height: calc(100% - 66px);
    padding: 16px;
    border-radius: 4px;
    background-color: #FFF;
  }
.search-bar {
    padding: 6px 16px;
    background-color: #fff;
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    border-radius: 8px;
    height: 50px;
    .search-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        white-space: nowrap;
      }

      .el-input {
        // width: 200px;
      }
    }
  }


  .table-toolbar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .left {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}

.detail-header {
  margin-bottom: 16px;
}



:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #FAFAFA;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #3D3D3D;
    }
  }
}

:deep(.el-button) {
  &.el-button--link {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    letter-spacing: 0.04em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #165DFF;
  }
}

:deep(.custom-dialog) {

  .el-dialog__footer {
    padding: 16px 20px;
  }
  
  .el-form-item {
    .el-form-item__label {
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      text-transform: uppercase;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #333333;
      height: 44px;
      line-height: 44px;
    }
    
    .el-form-item__content {
      display: flex;
      align-items: center;
    }
    
    .el-input__wrapper {
      background-color: #F7F8FA;
      border: none;
      box-shadow: none;
      height: 44px;
      cursor: pointer;
      
      &.is-focus {
        box-shadow: none;
      }
    }

    .el-textarea__inner {
      background-color: #F7F8FA;
      border: none;
      box-shadow: none;
      
      &:focus {
        box-shadow: none;
      }
    }
  }
}

.map-container {
  width: 200%;
  height: 400px;
}
</style>