<template>
  <div class="container">
    <div class="top-box">
      <div class="search-box">
        <!-- <el-input v-model="search.name" placeholder="请输入区域" style="width: 300px; margin-right: 24px;"></el-input> -->
        <span>设备名称：</span>
        <el-input  v-model="search.deviceName" placeholder="请输入设备名称" style="width: 300px; margin-right: 24px;"></el-input>
        <span>编号：</span>
        <el-input  v-model="search.deviceNo" placeholder="请输入编号" style="width: 300px; margin-right: 24px;"></el-input>
        <el-button color="#165DFF" type="primary" icon="Search" @click="searchData">搜索</el-button>
      </div>
      <div class="add-box">
        <el-button color="#165DFF" type="primary" icon="Plus" @click="addpeidianbox">新增</el-button>
      </div>
    </div>
    <div class="itemBig-box">
      <ItemBox 
        v-for="(item,index) in ListData" 
        :key="index" :item="item" 
        @initialization="initialization"
        @removeFun="removeFun"
        @editFun="ItemeditFun" ></ItemBox>
    </div>
    <div class="paging-box">
      <el-pagination
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        background
        layout="prev, pager, next"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog
      v-model="dialogadd"
      title="新增光感控制器"
      width="30%"
      :close-on-click-modal="false"
      :before-close="handleCloseadd">
      <Addpeidianbox 
        v-if="dialogadd" 
        @handleClose="handleCloseadd"
        @submit="addFun"></Addpeidianbox>
    </el-dialog>
    <el-dialog
      v-model="dialogEdit"
      title="编辑集中控制器"
      width="67%"
      :close-on-click-modal="false"
      :before-close="EditClose">
      <Editpeidianbox 
        v-if="dialogEdit" 
        @handleClose="EditClose"
        @submit="editFun"
        @showViewBox="showViewBox"
        :editItemData="editItemData"
        @showAddLight="showAddLight"></Editpeidianbox>
    </el-dialog>
    <el-dialog
      v-model="dialogView"
      title="回路列表"
      width="80%"
      :close-on-click-modal="false"
      :before-close="ViewClose">
      <ViewListBox
        v-if="dialogView"
      ></ViewListBox>
    </el-dialog>
    <el-dialog
      v-model="dialogBind"
      title="绑定单灯"
      width="80%"
      :close-on-click-modal="false"
      :before-close="BindClose">
      <BindBox
        v-if="dialogBind"
        @BindClose="BindClose"
        :bindData="bindData"
      ></BindBox>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ItemBox from '@/components/lamp/lightEnvironment/itemBox.vue'
// import Rightpagebox from "@/components/publiczj/rightpagebox.vue"
import Addpeidianbox from '@/components/lamp/addLightEnvironment/index.vue'
import Editpeidianbox from '@/components/lamp/editLightEnvironment/index.vue'
import ViewListBox from '@/components/lamp/editLightEnvironment/viewListBox.vue'
import BindBox from '@/components/lamp/editLightEnvironment/bindBox.vue'
import { useRouter } from 'vue-router';
import {  getList, add, remove } from '@/api/lamp/lightEnvironment.js'
import { ElMessage, ElMessageBox } from 'element-plus'


const router = useRouter();


const showMenu = ref(false)
const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const search = ref({ })

const page = ref({
  pageSize: 8,
  currentPage: 1,
  total: 0,
})

const searchData = () => {
  getListData()
}

const menuData = ref([
  {
    name: '单灯',
    // icon: ArrowRightBold,
    path: '/lamp/console',
  },
  {
    name: '集中控制器',
    // icon: ArrowRightBold,
    path: '/lamp/switchbox',
  }
])

const toluyou = (item) => {
  console.log('跳转')
  showMenu.value = !showMenu.value
  router.push(item.path);
}

const ListData = ref([])
const getListData = () => {
  getList(page.value.currentPage, page.value.pageSize, search.value).then(res=>{
    ListData.value = res.data.data.records
    page.value.total = res.data.data.total
    page.value.currentPage = res.data.data.current
    page.value.pageSize = res.data.data.size
  })
}

const initialization = () => {
  page.value =  {
    pageSize: 10,
    currentPage: 1,
    total: 0,
  },
  search.value = {}
  getListData()
}

const dialogadd = ref(false)
// 新增集中控制器
const addpeidianbox = () => {
  dialogadd.value = true
}

const handleCloseadd = () => {
  dialogadd.value = false
  initialization()
}

// 新增
const addFun = (row) => {
  add(row).then(res=>{
    console.log(res,"res");
    dialogadd.value = false;
    initialization()
  }).catch(err=>{
    console.log(err,"err");
  })
}

// 删除
const removeFun = (row) => {
  console.log('removeFun删除', row.id);
  ElMessageBox.confirm(
    '该操作将删除该光环境设备，是否继续？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      remove(row.id).then(res=>{
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
        initialization()
      }).catch(err=>{
        console.log(err,"err");
      })
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: '删除失败!',
      // })
    })
}

const dialogEdit = ref(false)

const EditClose = (done) => {
  dialogEdit.value = false
  initialization()
}

const editItemData = ref({})

// 开启编辑
const ItemeditFun = (row) => {
  console.log('ItemeditFun编辑', row);
  editItemData.value = row
  dialogEdit.value = true
}

// 编辑
const editFun = (row) => {
  console.log('editFun编辑', row);
  dialogEdit.value = true
}

const dialogView = ref(false)
const showViewBox = (row) => {
  console.log('showViewBox', row);
  dialogEdit.value  = false
  dialogView.value = true
}

const ViewClose = (done) => {
  dialogView.value = false
  dialogEdit.value  = true
}

// 绑定单灯
const dialogBind = ref(false)

const bindFun = (row) => {
  console.log('bindFun绑定', row);
  dialogBind.value = true
}

const BindClose = (done) => {
  dialogBind.value = false
  dialogEdit.value  = true
}

const bindData = ref({})
const showAddLight = (row) => {
  console.log('showAddLight', row);
  bindData.value = row
  dialogEdit.value  = false
  dialogBind.value = true
}

const handleSizeChange = (val) => {
  console.log(`size change: ${val}`)
  page.value.pageSize = val
  getListData()
}

const handleCurrentChange=(val)=>{
  console.log(`current page: ${val}`)
  page.value.currentPage = val
  getListData()
}

const handleClose = () => {
  dialogEdit.value = false
  initialization()
}

const handleGuanganClose = () => {
  dialogView.value = false
  dialogEdit.value = true
}

onMounted(()=>{
  getListData()
})

</script>
<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.container{
  height: 100%;
  padding: 16px 16px 0;
}
.itemBig-box{
  width: 100%;
  height: calc(100% - 119px);
  display: grid;
  grid-template-columns: repeat(4, 375px);
  grid-gap: 16px 24px;
  margin-top: 18px;
  .item-box{
    height: 386px;
    background-color: #fff;
    border-radius: 8px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    // padding: 14px 16px 16px;
  }
}


.add-box{
  margin-right: 24px;
}
.top-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  border-radius: 8px;
  background: #FFFFFF;
  padding: 0 16px;
}
.paging-box{
  display: flex;
  justify-content: flex-end;
  margin: 16px 0 24px;
}

.menu-btn{
  position: fixed;
  right: 0;
  bottom: calc(50% - 130px);
  width: 52px;
  height: 262px;
  font-size: 20px;
  line-height: 52px;
  writing-mode: vertical-lr;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  cursor: pointer;
}


.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}
</style>
<style lang="scss" scoped>
  .menu-box{
    width: 30vw;
    height: calc(100vh - 60px);
    position: fixed;
    right:0vw;
    top: 60px;
    padding: 16px;
    z-index: 999;
    background-color: #FFF;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.4);
  }
  .close-btn{
    width: 20px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 30vw;
    top: calc((100vh - 60px) - ((100vh - 60px)/2 - 20px));
    cursor: pointer;
    background-color: #FFF;
    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.4);
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
  }
  .item-box{
    display: grid;
    grid-template-columns: 260px;
    grid-auto-rows: 200px;
    height: calc(100% - 220px);
    grid-gap: 16px;
    .item-box-item{
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #F2F2F2;
      border-radius: 4px;
      cursor: pointer;
      .item-box-item-text{
        
      }
    }
  }
</style>