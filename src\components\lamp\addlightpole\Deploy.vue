<template>
  <div class="deploy">
    <modelOne @addClick="addClick" :lampsLanterns="props.lampsLanterns" :poleType="props.activation"></modelOne>
    <div class="form-box" v-show="showform">
        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          label-position="top"
          status-icon >
          <el-form-item label="灯杆名称：" prop="poleName">
            <el-input class="inputStyles" maxlength="20" v-model="ruleForm.poleName" placeholder="请输入灯杆名称" />
          </el-form-item>
          <el-form-item label="灯杆编号：" prop="poleNo">
            <el-input class="inputStyles" maxlength="20" v-model="ruleForm.poleNo" placeholder="请输入灯杆编号" />
          </el-form-item>
          <el-form-item label="图标：" prop="icon">
            <el-upload
              class="avatar-uploader"
              action="/api/hztech-resource/oss/endpoint/put-file"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="ruleForm.icon" :src="ruleForm.icon" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item label="灯杆位置：" prop="poleAddress">
            <el-input class="inputStyles" v-model="ruleForm.poleAddress" @click="selectAddress" placeholder="请选择灯杆地址" />
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input class="inputStyles" v-model="ruleForm.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
    </div>
    <Adress  v-if="showMap" @changeAddress="changeAddress"></Adress>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits, watch } from 'vue'
import modelOne from '../model/one.vue'
import Adress from '../map/adress.vue';
import { add } from '@/api/lamp/index.js'
// import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getToken } from "@/utils/auth";

const emit = defineEmits(['coles','showSelectfun'])

let props = defineProps({
    activation: {
        type: Number,
    },
    lampsLanterns:{
      type: Object,
      default: () => ({})
    }
});

const showform = ref(true);
const showMap = ref(false);

const ruleForm = ref({
  poleName: '',
  poleNo: '',
  poleAddress: '',
  lat: '',
  lng: '',
  icon: '',
  remark: '',
  modelNo: props.activation || ''
});

const rules = ref({
  poleName: [
    { required: true, message: "请输入灯杆名称", trigger: "blur" },
    { min: 2, max: 20, message: "名称长度在2-20个字符之间", trigger: "blur" },
  ],
  poleNo: [
    { required: true, message: "请输入灯杆编号", trigger: "blur" },
    { pattern: /^[A-Za-z0-9]+$/, message: '只能输入数字和字母', trigger: 'blur' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
  ],
  poleAddress: [
    { required: true, message: "请选择灯杆地址", trigger: ["blur", "change"] },
  ],
});

const selectAddress = () => {
  console.log('点击了');
  showform.value = false;
  showMap.value = true;
}

const changeAddress = (row) => {
  console.log('row', row);
  ruleForm.value.poleAddress = row.store_address;
  ruleForm.value.lat = row.lat;
  ruleForm.value.lng = row.lng;
  showform.value = true;
  showMap.value = false;
  // 手动触发表单验证
  if (ruleFormRef.value) {
    ruleFormRef.value.validateField('poleAddress');
  }
}

// 添加表单值变化的监听
watch(
  () => ruleForm.value.poleAddress,
  (newVal) => {
    if (newVal && ruleFormRef.value) {
      ruleFormRef.value.validateField('poleAddress');
    }
  }
);

const ruleFormRef = ref(null);
const submitForm = async () => {
  if (!ruleFormRef.value) return { success: false, data: null };
  
  try {
    await ruleFormRef.value.validate();
    return { success: true, data: ruleForm.value };
  } catch (error) {
    console.error('表单验证失败:', error);
    return { success: false, data: null };
  }
};

const resetForm = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields();
    ruleForm.value = {
      poleName: '',
      poleNo: '',
      poleAddress: '',
      lat: '',
      lng: '',
      icon: '',
      remark: '',
      modelNo: props.activation || ''
    }
  }
};

let headers = ref({
    //上传图片headers
    "hztech-auth": "bearer " + getToken(),
})

const handleAvatarSuccess = (response, uploadFile)=>{
  ruleForm.value.icon = response.data.link;
};

const beforeAvatarUpload = (rawFile)=>{
  if (rawFile.type!== 'image/jpeg') {
    ElMessage.error('请上传image/jpeg格式图片');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片文件不能大于2M');
    return false;
  }
  return true;
};

const addClick = (data)=>{
  console.log('addClick', data);
  emit('showSelectfun',data)
  // showform.value = true;
  // showMap.value = false;
}

onMounted(() => {
  if (props.activation) {
    ruleForm.value.modelNo = props.activation;
  }
});

defineExpose({
  submitForm,
  resetForm
});

</script>
<style lang="scss" scoped>
.title{
    margin-bottom: 24px;
}
.deploy{
    width: 100%;
    height: 460px;
    display: flex;
}
.form-box{
    width: 50%;
    padding: 16px 24px ;
}


.inputStyles{
  width: 100%;
  height: 40px;
}

.avatar-uploader .avatar {
  width: 60px;
  height: 60px;
  display: block;
  border: 1px dashed #8c939d;
  border-radius: 6px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #8c939d;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 60px;
  height: 60px;
  text-align: center;
  border: 1px dashed #8c939d;
  border-radius: 6px;
}

:deep(.el-input) {
  .el-input__wrapper {
    width: 340px;
    background-color: #F7F8FA;
    border: none;
    box-shadow: none;
    padding: 12px;
    
    &.is-focus {
      box-shadow: none;
    }

    &.is-readonly {
      cursor: pointer;
    }
  }
  
  input {
    width: 340px;
    height: 20px;
    font-size: 14px;
    &::placeholder {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 350;
      line-height: normal;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #A2AAB3;
    }
  }
}
</style>