chrome sdk接口文档
备注：1.8~1.18接口只有服务器账号才有权限调用，账号统一为”mainalc”,密码为对讲服务器登录密码。
更新时间	更新描述
2023.01.16	新增广播任务获取接口（1.52~1.53，2.51~2.52）
2022.08.10	新增linux服务器相关接口（1.51，2.50）
2021.12.13	新增获取录像列表（1.46，2.48）、录像播放（1.47）
2021.11.06	设备通知回调新增广播状态的通知（2.3）
2021.11.01	新增设备音量接口（1.42~1.43，2.44~2.45）
新增广播音量接口（1.44~1.45，2.46~2.47）
新增实时广播接口（1.40~1.41）
修改广播任务回调参数（2.36）
获取广播任务接口新增参数任务类型（1.32）
上传、修改广播任务接口新增参数taskType,bcType（1.33）
2021.09.25	新增二维码下发相关接口（1.37，2.40）
新增设备坐标相关接口（1.38~1.39，2.41~2.42）
2021.06.07	新增广播相关接口（1.25~1.36，2.29~2.39）
废弃多文件对讲相关接口（1.21~1.24，2.25~2.28）
2021.01.09	新增多设备对讲接口，1.21~1.24, 2.24~2.28

主动调用接口
1.1 登录
接口：login(string username, string password,string serverAddr)
说明：登录服务器
参数：username    表示登录账号
	  password    表示登录密码
	  serverAddr   表示服务器ip地址
返回值：登录结果通过回调接口onLogin返回
1.2 登出
接口：logout(int loginHandle)
说明：登出服务器
参数：loginHandle	表示登录账号标识，onLogin回调返回

1.3 播放视频
接口：playRT(object video, int deviceid, int loginHandle, bool isTalk)
说明：播放视频
参数：video	表示播放视频标签
deviceid	表示播放设备id
loginHandle	表示登录账号标识
isTalk  是否打开对讲 （true false）
		

1.4 关闭视频
接口：stopRT(int deviceid, int loginHandle)
说明：关闭视频
参数： deviceid	表示播放设备id
loginHandle	表示登录账号标识
1.5 打开设备的数字输出
接口：doControl(int deviceid, int loginHandle, int doIndex)
说明：用于开锁等相关的继电器操作
参数： deviceid	表示播放设备id
loginHandle	表示登录账号标识
doIndex     数字输出的索引号，目前支持1和2

1.6 打开对讲
接口：startTalk(int deviceid)
说明：打开对讲, 调用playRT接口播放视频，isTalk = true 时默认打开对讲
参数： deviceid	表示播放设备id 
1.7 播放设备音频
接口：playDeviceAudio (int deviceid)
说明：播放设备音频  调用playRT接口播放视频，isTalk = true 时默认打开设备音频
参数： deviceid	表示播放设备id 

1.8 添加分组
接口：addGroup(int requestId, int loginHandle, String groupType, String groupName, String groupContact, String groupPhone)
说明：添加分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
groupType        分组类型，详见参数说明
groupNmae 		分组名称
groupContact 	    分组联系人
groupPhone 	     分组联系电话
1.9 编辑分组
接口：editGroup(int requestId, int loginHandle, int groupId, String groupName, String groupContact, String groupPhone)
说明：编辑分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
groupId          分组ID
groupName 		分组名称
groupContact 	    分组联系人
groupPhone 	     分组联系电话
1.10 删除分组
接口：delGroup(int requestId, int loginHandle, int groupId)
说明：编辑分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
groupId          分组ID 
1.11 获取用户列表
接口：getUsers(int requestId, int loginHandle)
说明：获取所有用户列表
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
1.12 获取用户分组
接口：getUserGroups(int requestId, int loginHandle, int userId)
说明：获取用户所拥有的分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
userId          用户ID 
1.13 添加用户
接口：addUser(int requestId, int loginHandle, String userName, String password)
说明：添加用户
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
userName			用户名
password			密码
1.14 编辑用户密码
接口：editPassword(int requestId, int loginHandle, int userId, String password)
说明：编辑用户密码
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
userId			用户ID
password			用户密码
1.15 删除用户
接口：delUser(int requestId, int loginHandle, int userId)
说明：编辑分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
userId			用户ID
1.16 授权分组
接口：authorizeGroup (int requestId, int loginHandle, int userId, int [] groupIds)
说明：授权用户分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
userId			用户ID
groupIds			分组ID数组
1.17 编辑设备
接口：editDevice(int requestId, int loginHandle, int deviceId, String deviceName, String deviceContact, String devicePhone)
说明：编辑设备
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
userId			用户ID
deviceId			设备ID
deviceName       设备名称
devceiContact     设备联系人
devicePhone		设备联系电话

1.18 移动设备
接口：moveDevice(int requestId, int loginHandle, int deviceId, int groupId)
说明：移动设备到另一分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
deviceId			设备ID 
groupId			分组ID
1.19 获取设备推送配置
接口：getWebPush(int requestId, int loginHandle, int deviceId)
说明：移动设备到另一分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
deviceId			设备ID 
1.20 编辑设备推送配置
接口：editWebPush(int requestId, int loginHandle, int deviceId, String pushUrl, String context, String isAuth, String userName, String password)
说明：移动设备到另一分组
参数： requestId	       请求id,前端自己生成，回调会返回该id
loginHandle      表示登录账号标识
deviceId			设备ID 
pushUrl			推送接收url
context			用户数据
isAuth			是否需要认证   0-否 1-是 
userName			认证用户名 
password 	    认证密码

1.21 打开多设备麦克风对讲（已废弃）
接口：startMultiMicTalk (int loginHandle, array deviceIds)
说明：同时对讲多台设备 
参数：loginHandle      表示登录账号标识
deviceIds			设备ID 数组
1.22 停止多设备麦克风对讲（已废弃）
接口：stopMultiMicTalk (int loginHandle,int isAll, array deviceIds)
说明：停止对讲多台设备 
参数：loginHandle      表示登录账号标识
		isAll			true:停止所有，false：停止指定设备		
deviceIds			设备ID 数组，当isAll=false必传

1.23 打开多设备文件语音（已废弃）
接口：startMultiFileTalk (int loginHandle, array deviceIds，string audioFilePath)
说明：同时对多台设备进行文件语音
参数：loginHandle      表示登录账号标识
deviceIds			设备ID 数组
audioFilePath			音频文件路径
1.24 停止多设备文件语音（已废弃）
接口：stopMultiFileTalk (int loginHandle,int isAll, array deviceIds)
说明：停止对多台设备进行文件语音 
参数：loginHandle      表示登录账号标识
		isAll			true:停止所有，false：停止指定设备		
deviceIds			设备ID 数组，当isAll=false必传
1.25 打开广播
接口：startBroadcast (int loginHandle,int [ ] deviceIds, int broadcastType,string bcFilePath)
说明：打开广播
参数：loginHandle      表示登录账号标识
		deviceIds			广播的设备
broadcastType		广播类型（1：麦克风广播；2：文件广播）
bcFilePath   		广播文件路径，文件广播时必填
1.26更新广播文件
接口：updateBroadcastFile (string bcFilePath)
说明：更换文件广播的广播文件
参数： bcFilePath   		广播文件路径

1.27 关闭广播
接口：stopBroadcast (int loginHandle)
说明：关闭广播
参数： loginHandle      表示登录账号标识

1.28 设置广播状态
接口：setBroadcastState (string bcPlayState)
说明：设置广播状态
参数： bcPlayState  广播状态（play : 播放； pause: 暂停）	
1.29 获取音频文件列表
接口：getAudioFileList (int requestId,int loginHandle)
说明：获取音频文件列表
参数： loginHandle      表示登录账号标识
requestId 		接口请求ID
1.30 上传音频文件
接口：uploadAudioFile (int requestId,int loginHandle, string fileName, string filePath)
说明：上传音频文件
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
fileName			文件名称
filePath			文件路径

1.31 删除音频文件
接口：deltetAudioFile (int requestId,int loginHandle,int fileId)
说明：删除音频文件
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
fileId			文件ID（由文件列表返回）

1.32 获取广播任务列表
接口：getBCTaskList(int requestId,int loginHandle,string taskType)
说明：获取广播任务列表
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskType			任务类型（realtime：实时任务，timed：定时任务）默认值：timed
1.33 上传广播任务
接口：uploadBCTask(int requestId,int loginHandle, string taskName, int volume, 
int [] deviceIds, int [] fileIds, bool enable,string execMode,
int [] weekDay, string startTimeOfDay, Object expireObj, 
string timeMode, int modeContent, string taskType, string bcType)
说明：上传广播任务
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskName   		任务名称
volume			广播音量（0~100）
deviceIds			广播的设备列表
fileIds			广播播放的文件列表（数组元素的顺序就是播放的顺序）
enable			广播启用/禁用
execMode		执行模式（“Sigle“, “EveryDay”,”EveryWeek”,分别代表单次、每天、每周）
weekDay			执行模式为“EveryWeek“时有效，1～6代表周一～周六，0代表周日
startTimeOfDay   任务开始时间（时间格式，如10:08:08）
expireObj		 任务有效期，object格式
				{
					enable		有效期启用/禁用
					beginDate    开始日期（日期格式，如：2021-06-06）
					endDate		截止日期（日期格式，如：2021-06-06）
}
		timeMode		播放模式（“seconds“: 时长，单位为秒；”times“: 次数）
		modeContent    timeMode=“seconds“ 填时长，timeMode=“times“, 填次数
taskType	        任务类型（realtime：实时任务，timed：定时任务）默认值：timed
bcType	广播类型（mic：麦克风，file：文件）默认值：file，类型为mic时，取的是服务器的麦克风数据

1.34 修改广播任务
接口：editBCTask(int requestId,int loginHandle, int taskId, string taskName, int volume, 
int [] deviceIds, int [] fileIds, bool enable,string execMode,
int [] weekDay, string startTimeOfDay, Object expireObj, 
string timeMode, int modeContent, string taskType, string bcType)
说明：修改广播任务
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskId			任务ID
taskName   		任务名称
volume			广播音量（0~100）
deviceIds			广播的设备列表
fileIds			广播播放的文件列表
enable			广播启用/禁用
execMode		执行模式（“Sigle“, “EveryDay”,”EveryWeek”,分别代表单次、每天、每周）
weekDay			执行模式为“EveryWeek“时有效，1～6代表周一～周六，0代表周日
startTimeOfDay   任务开始时间（时间格式，如10:08:08）
expireObj		 任务有效期，object格式
				{
					enable		有效期启用/禁用
					beginDate    开始日期（日期格式，如：2021-06-06）
					endDate		截止日期（日期格式，如：2021-06-06）
}
		timeMode		播放模式（“seconds“: 时长，单位为秒；”times“: 次数）
		modeContent    timeMode=“seconds“ 填时长，timeMode=“times“, 填次数
taskType	       任务类型（realtime：实时任务，timed：定时任务）默认值：timed
bcType			广播类型（mic：麦克风，file：文件）默认值：file

1.35 删除广播任务
接口：deleteBCTask(int requestId,int loginHandle, int taskId)
说明：删除广播任务
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskId			任务ID
1.36 试听广播任务（实时文件广播）
接口：tryBCTask(int requestId,int loginHandle, int taskId, string action)
说明：广播任务试听
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskId			任务ID
action			试听动作（“start”: 开始， “stop”: 停止）

1.37 二维码下发
接口：configPayStatus(int requestId,int loginHandle, int deviceId, int status, int divCode, int orgCode, string carNumber, int carType, string qrText, string parkingTime, string validDate, string cost)
说明：配置显示屏界面缴费状态和二维码
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID
status			缴费状态（0: 待缴费，1: 缴费成功）
divCode		// 一级行政区代码，如浙江 33,北京 11
// 以下代码单独定义 
// 学: 101  警: 102  使: 103
orgCode			// 发牌机关代码，一级行政区代码后边的一位的ASCII值
				// 如:“浙 A•12345”为‘A‘的ASCII值65
carNumber		车牌号，传空则显示屏上隐藏
carType	车辆类型 （ 0: 隐藏显示，1: 业主车，2: 包年车，3: 包月车，4: 临停车，5: 无牌车）
qrText	二维码原始内容
parkingTime	停车时长，（停车天数(填-1 隐藏) ，小时(填-1 隐藏) ，分钟(填-1 隐藏)， 中间使用”:“分隔，如：-1:1:20，则表示1小时20分
validDate		车辆有效期，中间使用”:“分隔，如：2021:09:01
年(填-1 隐藏)，月(填-1 隐藏) ，日(填-1 隐藏)
cost				缴费金额，-1隐藏金额，金额范围0~65534
1.38 获取设备额外信息（设备坐标）
接口：getDeviceExtra(int requestId,int loginHandle, int deviceId)
说明：获取设备额外信息
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID，0表示获取所有设备
1.39 设置设备额外信息（设备坐标）
接口：setDeviceExtra(int requestId,int loginHandle, int deviceId, string location, string contact, string phone, string remark)
说明：设置设备额外信息
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID
location			坐标，格式为：经度_纬度，如：118.6691_36.1341
contact			联系人名称
phone			联系电话
remark			备注



1.40上传实时广播任务
接口：uploadBCTaskRealtime(int requestId,int loginHandle, string taskName, int volume, 
int [] deviceIds, int [] fileIds,int time, string bcType)
说明：上传广播任务，也可用1.33接口实现，回调使用2.37
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskName   		任务名称
volume			广播音量（0~100）
deviceIds			广播的设备列表
fileIds			广播播放的文件列表（数组元素的顺序就是播放的顺序）
		times			播放次数
bcType			广播类型（mic：麦克风广播，file：文件广播）
1.41 修改实时广播任务
接口：editBCTask(int requestId,int loginHandle, int taskId, string taskName, int volume, 
int [] deviceIds, int [] fileIds, int time, string bcType)
说明：修改广播任务，也可用1.34接口实现，回调使用2.38
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
taskId			任务ID
taskName   		任务名称
volume			广播音量（0~100）
deviceIds			广播的设备列表
fileIds			广播播放的文件列表
times			播放次数
bcType	广播类型（mic：麦克风广播，file：文件广播）广播类型不允许修改
1.42 获取设备音量
接口：getDeviceVolume(int requestId,int loginHandle, int deviceId, string type)
说明：获取设备麦克风或喇叭音量
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID
type				音量类型（input: 麦克风音量，output：喇叭音量）
1.43 设置设备音量
接口：setDeviceVolume(int requestId,int loginHandle, int deviceId, string type, int [] volume)
说明：设置设备麦克风或喇叭音量
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID
type				音量类型（input: 麦克风音量，output：喇叭音量）
volume          设备音量，数组类型，每个音频通道一个配置（暂只支持单通道）

1.44 获取广播音量
接口：getBroadcastVolume(int requestId,int loginHandle, int deviceId)
说明：获取广播音量（只能在广播时才能调用）
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID
type				音量类型（input: 麦克风音量，output：喇叭音量）
1.45 设置广播音量
接口：setDeviceVolume(int requestId,int loginHandle, int deviceId, int [] volume)
说明：设置广播音量（只能在广播时才能调用，且只单次有效）
参数：  requestId 		接口请求ID
loginHandle      表示登录账号标识
deviceId			设备ID
volume          设备音量，数组类型，每个音频通道一个配置（暂只支持单通道）
1.46 获取录像列表
接口：getRecordList(int loginHandle, int deviceId, int begintime, int endtime)
说明：获取录像列表（获取所有录像，开始时间和结束时间都传0）
参数：  loginHandle      表示登录账号标识
deviceId			设备ID
begintime       录像开始时间
endtime         录像结束时间
1.47 播放录像
接口：playBack(int loginHandle, int deviceId, int begintime,Object video)
说明：播放录像（必须先调用获取录像列表接口才能播放，且一次只能播放一个录像；录像暂只支持实时流播放，无法控制进度）
参数：  loginHandle      表示登录账号标识
deviceId			设备ID
begintime       录像开始时间
video			播放窗口对象
1.48 开始侦听
接口：startDetect (int deviceId)
说明：开始侦听设备音频（该功能只是用来播放设备的音频）
参数：  deviceId			设备ID
1.49 停止侦听
接口：stopDetect (int deviceId)
说明：停止侦听设备音频（请勿在播放音视频时调用此功能，否则会导致音视频停止）
参数：  deviceId			设备ID
1.50 搜索日志
接口：searchLog(int requestId, int ulTimeBeg, int ulTimeEnd, int pageIndex, int onePageItems, Object option )
说明：搜索服务器日志，总账号可以查询所有日志，坐席账号只能查用户操作日志和报警设备报警日志
参数：  requestId		接口请求ID
ulTimeBeg		开始时间戳
ulTimeEnd		结束时间戳
pageIndex		页数index，0开始
onePageItems   一页显示的数量
option 			可选参数
option.ulUserID   账号ID,管理员账号登录，查询所有日志传65534，管理员日志传0，其它用户为用户ID；坐席账号登录固定传65534
option.ulCameraID 设备序列号
option.ulType  	日志类型，值定义
option.ulEvent	日志事件，值定义

1.51 linux服务器接口
1.51.1 获取系统配置
接口：getSystemCfg(int requestId)
说明：设备播放检测音频
参数：  requestId		接口请求ID

1.51.2 设置系统配置
接口：getSystemCfg(int requestId, string serverName, int serverPort, string password, int playrttimeout, int alarmingtime, int leveltime, int recordEnable, int recordRemainSpace, string [ ] recordDisk )
说明：设置系统配置
参数：  requestId		接口请求ID
serverName      服务器名称
serverPort		服务器端口
password		服务器密码
playrttimeout     监视超时时间，范围0~6000000（毫秒）
alarmingtime	    报警超时时间，范围1000~3600000（毫秒）
leveltime  		分级通知间隔，范围1000~600000（毫秒）
recordEnable		录像启用/禁用
recordRemainSpace	录像剩余空间覆盖
recordDisk		录像地址

1.51.3 批量控制DO
接口：openDo(int requestId, int [] cameraIds, int index, )
说明：同时设置多台设备DO, 用于开锁等相关的继电器操作
参数：  requestId		接口请求ID
cameraIds		设备ID
index				数字输出的索引号，目前支持1和2
1.51.4 获取设备来人提示音规则列表
接口：getTipAudioInfo (int requestId )
说明：获取来人提示音的所有规则
参数：  requestId		接口请求ID
1.51.5 添加/修改规则列表
接口：addOrModTipAudio (int requestId, int ruleId, int ruleIndex, string name, int trigSrc, array timeSection )
说明：添加或修改规则列表，最多存在10个
参数：  requestId		接口请求ID
		ruleId			规则ID，添加时传0
		ruleIndex        规则index，添加时传0
		name			规则名称
		trigSrc			地感出发 0：否，1：是
		timeSection 	   	时间段，数组类型，最多6个时间段，时间段不允许冲突
timeSection:
字段	类型	描述
startH	int	开始时间小时，0~23
startM	int	开始时间分钟，0~59
endH	int	结束时间小时，0~23
endM	int	结束时间小时，0~59
volume	int	音量，0~100
enable	int	启用，0：禁用，1：启用
1.51.6 获取设备铃声规则
接口：getDeviceTipAudioName (int requestId, int [] cameraIds )
说明：获取设备铃声规则
参数：  requestId		接口请求ID
		cameraIds		设备ID，数组
1.51.7 上传文件（升级文件、铃声文件）
接口：upLoadFirmware (int requestId, string fileName,ArrayBuffer fileBuffer)
说明：上传文件，用于上传升级文件或导入铃声文件，回调使用2.33，2.34
参数：  requestId		接口请求ID
		fileName			文件名称，升级文件后缀为”.image”,铃声文件为“.wav”
		filleBuffer		文件buffer
1.51.8 开始升级或导入
接口：startUpdate(int requestId, string fileName,int deviceId, int flag)
说明：开始升级设备或导入铃声文件
参数：  requestId		接口请求ID
		fileName			文件名称，需要与上传文件时fileName值相同
		deviceId			设备ID
		flag				升级标识，0:固件升级，1，按键提示音 2，来人提示音
1.51.9 停止升级
接口：stopUpdate(int requestId, int deviceId)
说明：停止升级设备
参数：  requestId		接口请求ID 
		deviceId			设备ID 
1.51.10 查询升级（导入）状态
接口：queryUpdateStatus (int requestId, int deviceId)
说明：查询升级或导入状态
参数：  requestId		接口请求ID 
		deviceId			设备ID 
1.51.11 获取设备配置信息
接口：getCameraInfo(int requestId, int [] deviceIds)
说明：获取设备配置
参数：  requestId		接口请求ID 
		deviceIds			设备ID数组 
1.51.12 删除设备
接口：deleteDevice(int requestId, int [] deviceIds)
说明：删除设备
参数：  requestId		接口请求ID 
		deviceIds			设备ID数组 


1.52 获取广播任务ID列表（任务列表多时使用此接口）
接口：getTaskIdList(int requestId,)
说明：获取定时广播任务列表ID
参数：  requestId		接口请求ID 
1.53 获取广播任务信息
接口：getTaskIdInfo(int requestId, int [] taskIds)
说明：获取定时广播任务列表ID
参数：  requestId		接口请求ID 
taskIds			任务ID


事件回调接口
2.1登录回调
接口：onLogin(object)
说明：登录回调
object：method：”systemManager.onLogin”
 error： “success”
params： { “loginHandle”: 123}
回调事例：
{"method":"systemManager.onLogin","error":"success","params":{"loginHandle":61456512}}
2.2设备列表回调
接口：onDeviceList (object)
说明：设备回调
object：method：” configManager.onDeviceList”
 error： “success”
params： {
	list: [{
		deviceId: 111,
		deviceName: “aaa”,
		deviceType: “alarm”,
		action: “Offline”}
],
			loginHandle: 123
}
回调事例： 
{"method":"configManager.onDeviceList","error":"success","params":{"list":[{"deviceId":912462,"parentId":127,"deviceName":"HD-7FE2B48E","deviceType":"Alarm","action":"Offline"}],"loginHandle":61456512}}


2.3设备通知回调
接口：onNotify (object)
说明：设备回调
object：method：” eventManager.notify”
params： {
	code: “DeivceStatus” 表示通知类型，详见参数说明
		    deviceId: 111,
	action: “Offline,  表示设备状态，详见参数说明
			loginHandle: 123
}
回调事例：
{"method":"eventManager.notify","params":{"code":"DeviceStatus","action":"Offline","deviceId":13298,"parentId":105,"loginHandle":61456512}}

2.4播放回调
接口：onPlayRT(object)
说明：设备播放回调
object：method：” systemManager.onPlayRT”,
error： “success”,
params： {
		    deviceId: 111, 
			loginHandle: 123
}
回调事例：
{"method":"systemManager.onPlayRT","error":"success","params":{"loginHandle":61456512,"deviceId":15519}}

2.5 数据解析异常回调
接口：onParseMsgError(object)
说明：设备回调
object：异常数据
2.6 设备音频流回调 
接口：onDeviceAudioData(arraybuffer, deviceId)
说明：设备回调
arraybuffer：PCM码流 
deviceId：设备ID
事例：
var dhWeb = new DHAlarmWeb();
dhWeb. onDeviceAudioData = function(data, deviceId){
	//dealwith arraybuffer data
}
2.7 设备视频流回调 
接口：onDeviceVideoData(arraybuffer, deviceId)
说明：设备视频流回调
arraybuffer：H.264码流 
deviceId：设备ID
事例：
var dhWeb = new DHAlarmWeb();
dhWeb. onDeviceVideoData = function(data, deviceId){
	//dealwith arraybuffer data
}
2.8 本地音频流回调 
接口：onLocalAudioData(arraybuffer, deviceId)
说明：本地电脑麦克风码流回调
arraybuffer：PCM码流 
deviceId：设备ID
事例：
var dhWeb = new DHAlarmWeb();
dhWeb. onLocalAudioData = function(data, deviceId){
	//dealwith arraybuffer data
}
2.9 客户端与服务器断开回调
接口：onAlarmServerClosed ()
说明：客户端与服务器断开回调
2.10 SDK异常回调 
接口：onDHAlarmWebError (jsonObject)
说明：SDK异常回调
jsonObject : {
	clientid：“”,
	error: “fail”,
	msg: {
		method: "systemManager.onError", 
error: "loginTimeout"
}
}
备注：loginTimeout:登录超时；socketError:socket异常；dataTimeout:获取数据超时

回调事例：
{“clientid:” "error: "fail",msg: {method: "systemManager.onError", error: "loginTimeout"}


2.11 分组列表回调 
接口：onGroupList (object)
说明：分组列表回调
object：method：” configManager.onGroupList”
 error： “success”
params： {
	list: [{
"groupId": 2, 
"parentId ": 1, //parentId为0表示顶级分组
			"groupName": "默认",
"groupType": "normal"
}],
			loginHandle: 123
}
回调事例：
{"method":"configManager.onGroupList","error":"success","params":{"list":[{"groupId":128,"parentId":0,"groupName": "测试环境    ","groupType":"normal"}],"loginHandle":61456512}}
2.12 添加分组回调 
接口：onAddGroup (object)
说明：添加分组回调
object：method：” configManager.onAddGroup”
 error： “success”
id: “111“	 //添加分组接口请求ID
params： { 
"groupId": 2, 
			"groupName": "默认",
"groupContact": "",
"groupPhone": ""
					 loginHandle: 123
}
回调事例：
{"params":{"groupId":173,"groupName":"test001","groupContact":"cc","groupPhone":"057112345678","loginHandle":65856704},"method":"configManager.onAddGroup","error":"success","id":10069}
2.12 编辑分组回调 
接口：onEditGroup (object)
说明：编辑分组回调
object：method：” configManager.onEditGroup”
 error： “success”
id: “111“	 //编辑分组接口请求ID
params： { 
"groupId": 2, 
			"groupName": "默认",
"groupContact": "",
"groupPhone": ""
					 loginHandle: 123
}
回调事例：
{"params":{"groupId":173,"groupName":"test00","groupContact":"cc","groupPhone":"057112345678","loginHandle":65856704},"method":"configManager.onEditGroup","error":"success","id":10070}
2.13 删除分组回调 
接口：onDelGroup (object)
说明：删除分组回调
object：method：” configManager.onDelGroup”
 error： “success”
id: “111“	 //删除分组接口请求ID
params： { 
"groupId": 2, 
					 loginHandle: 123
}
回调事例：
{"params":{"groupId":173,"loginHandle":65856704},"method":"configManager.onDelGroup","error":"success","id":10071}
2.14 用户列表回调 
接口：onUserList (object)
说明：用户列表回调
object：method：” configManager.onUserList”
 error： “success”
id: “111“	 //获取用户接口请求ID
params： {
	list: [{
"userId": 2, 
"userName ": "alarm1"
},
{
"userId": 3, 
"userName ": "alarm2"
}],
			loginHandle: 123
}
回调事例：
{"params":{"list":[{"userId":102,"userName":"ceshi"},{"userId":104,"userName":"alarm"},{"userId":105,"userName":"zzys"}],"loginHandle":65856704},"method":"configManager.onUserList","error":"success","id":10001}
2.15 用户分组回调 
接口：onUserGroups (object)
说明：用户分组回调
object：method：” configManager.onUserGroups”
 error： “success”
id:	“1121” //获取用户分组接口请求ID
params： {
"userId": 3, 
"groupIds": [1,2]
					"loginHandle": 123
}
回调事例：
{"params":{"userId":140,"groupIds":[128,135],"loginHandle":65856704},"method":"configManager.onUserGroups","error":"success","id":10037}
2.16 添加用户回调 
接口：onAddUser (object)
说明：添加用户回调
object：method：” configManager.onAddUser”
 error： “success”
id:	“1121” //添加用户接口请求ID
params： {
"userId": 3, 
"userName": "alarm"
					"loginHandle": 123
}
回调事例：
{"params":{"userId":175,"userName":"ceshi001","loginHandle":65856704},"method":"configManager.onAddUser","error":"success","id":10072}
2.17 修改密码回调 
接口：onEditPassword (object)
说明：添加用户回调
object：method：” configManager.onEditPassword”
 error： “success”
id:	“1121” //修改密码接口请求ID
params： {
"userId": 3, 
					"loginHandle": 123
}
回调事例：
{"params":{"userId":175,"loginHandle":65856704},"method":"configManager.onEditPassword","error":"success","id":10073}
2.18 删除用户回调 
接口：onDelUser (object)
说明：删除用户回调
object：method：” configManager.onDelUser”
 error： “success”
id:	“1121” //删除用户接口请求ID
params： {
"userId": 3, 
					"loginHandle": 123
}
回调事例：
{"params":{"userId":175,"loginHandle":65856704},"method":"configManager.onDelUser","error":"success","id":10076}
2.19 授权分组回调 
接口：onAuthorizeGroup (object)
说明：授权分组回调
object：method：” configManager.onAuthorizeGroup”
 error： “success”
id:	“1121” //授权分组接口请求ID
params： {
"userId": 3, 
					"loginHandle": 123
}
回调事例：
{"params":{"userId":175,"loginHandle":65856704},"method":"configManager.onAuthorizeGroup","error":"success","id":10074}
2.20 编辑设备回调 
接口：onEditDevice(object)
说明：编辑设备回调
object：method：” configManager.onEditDevice”
 error： “success”
id:	“1121” //编辑设备接口请求ID
params： {
"deviceId": 3, 
"deviceName": , "出口"
"deviceContact": "", 
"devicePhone": "", 
					"loginHandle": 123
}
回调事例： 
{"params":{"deviceId":15519,"deviceName":"P6显示屏","deviceContact":"","devicePhone":"","loginHandle":65856704},"method":"configManager.onEditDevice","error":"success","id":10077}
2.21 移动设备回调 
接口：onMoveDevice(object)
说明：移动设备回调
object：method：” configManager.onMoveDevice”
 error： “success”
id:	“1121” //移动设备接口请求ID
params： {
"groupId": 3, 
					"loginHandle": 123
}
回调事例： 
{"params":{"groupId":147,"loginHandle":65856704},"method":"configManager.onMoveDevice","error":"success","id":10079}
2.22 推送配置回调 
接口：onGetWebPush(object)
说明：获取设备推送配置回调
object：method：” configManager.onGetWebPush”
 error： “success”
id:	“1121” //获取设备推送配置接口请求ID
params： {
"deviceId": 3, 
					"loginHandle": 123,
"url": "http://www.baidu.com",
"context": "",
"isAuth": 0,
"userName": "",
"password": ""
}
回调事例： 
{"method":"configManager.onGetWebPush","id":10022,"error":"success","params":{"deviceId":3,"url":"http://www.baidu.com","context":"","isAuth":0,"userName":"","password":"","loginHandle":15102208},"id":10022}
2.23 编辑推送配置回调 
接口：onEditWebPush(object)
说明：修改设备推送配置回调
object：method：” configManager.onEditWebPush”
 error： “success”
id:	“1121” //修改设备推送配置接口请求ID
回调事例： 
{"method":"configManager.onEditWebPush","id":10022,"error":"success","id":10022}

2.24 音频文件播放结束回调
接口：onPlayAudioFileEnd()
说明：音频文件播放完成 
2.25 报警多设备对讲停止时回调（已废弃）
接口：onAlarmMultiTalkStopped(deviceId)
说明：多设备对讲时，设备报警时自动关闭该设备对讲回调
2.26 多设备对讲开启完成时回调（已废弃）
接口：onMultiTalkStartFinished()
说明：多设备对讲全部开启完成回调
2.27 多设备对讲异常关闭回调（已废弃）
接口：onMultiTalkClosedError(deviceId, isAll)
说明：多设备对讲，所有通道都异常关闭时回调 
deviceId：关闭的设备ID
isAll：true-已经全部关闭 false-未全部关闭

2.28 多设备对讲数量限制回调（已废弃）
接口：onMultiTalkCountLimit()
说明：多设备对讲，对讲设备超出100台回调，超出时只会开启前100台

2.29 广播打开回调
接口：onStartBroadcast(msg)
说明：打开广播回调 
msg：method：” configManager. onStartBroadcast”
 error： “success”
id:	“1121” //打开广播接口请求ID
回调事例： 
{"method":"deviceManager.onStartBroadcas,"error":"success","params":{"loginHandle":50011792}}

2.30 广播通道关闭回调
接口：onBroadcastWsClosed(event)
说明：广播websocket通道关闭回调
event：websocket关闭信息，具体可查看websocket的closeEvent事件的官方文档
2.31 获取音频文件列表回调
接口：onGetAudioFileList(msg)
说明：获取音频文件列表回调
msg:  method：” audioManager.onGetAudioFileList”
 error： “success”
id:	“1000” //获取文件列表接口请求ID
params： { 
					“loginHandle“: 123,
					“files“: [
							{
								id: 1,
								name: “a.mp3”
								seconds: 120
},
{
								id: 2,
								name: “b.mp3”
								seconds: 300
},
		
]
}
回调事例： 
{"error":"success","id":1000,"method":"audioManager.onGetAudioFileList","params":{"files":[{"id":1,"name":"西海歌.mp3","seconds":343},{"id":2,"name":"懂得雨天.mp3","seconds":267}],"loginHandle":50011792}}

2.32 删除音频文件回调
接口：onDeleteAudioFile(msg)
说明：删除音频文件回调
msg:  method：” audioManager.onDeleteAudioFile”
 error： “success”
id:	“1000” //获取文件列表接口请求ID
params： { 
					“loginHandle“: 123,
}
回调事例： 
{"error":"success","id":1002,"method":"audioManager.onDeleteAudioFile","params":{"loginHandle":50011792}}
2.33 文件上传回调
接口：onUploadAudioFile(msg)
说明：文件上传指令回调，只有在成功后才会开始上传文件
msg:  method：” audioManager. onUploadAudioFile”
 error： “success”
id:	“1000” //获取文件列表接口请求ID
params： { 
					“loginHandle“: 123,
}
回调事例： 
{"error":"success","id":1004,"method":"audioManager.onUploadAudioFile","params":{"loginHandle":50011792}}
2.34 文件上传进度回调
接口：onUploadAudioFileProgress(progress)
说明：文件上传进度
 progress:  上传进度，0~1之间，保留小数点后四位，如：0.3421

2.35 音频文件解码失败回调
接口：onDecodeAudioError(msg)
说明：上传的音频文件解码失败
2.36 获取广播任务列表回调
接口：onGetBCTaskList(msg)
说明：上传的音频文件解码失败
msg: method：” audioManager.onGetBCTaskList”
 error： “success”
id:	“1000” //获取文件列表接口请求ID
params： { 
					“loginHandle“: 123,
					“tasks”: [
							{
								"deviceIds": [16990],  //播放设备ID
               					"duration": {			//按时长执行
                   					 "enable": false,	//启用禁用（与loop二选一）
                   					 "seconds": 0		//时长（秒）
                				},
"loop": {				//按次数执行
                   					"enable": true,   //启用禁用（与duration二选一）
                    				"times": 1		//次数
               					},
                				"enable": true,		//任务启用禁用
                                "execMode": "Single",	//模式（EveryWeek，EveryDay、Single）
                				"expire": {			//任务有效期
                   					"beginDate": "2021-06-02",
                    				"enable": true,
                    				"endDate": "2021-06-02"
               					},
                				"fileIds": [7, 1, 5, 4],
                				"id": 1,  				//文件ID
                				"name": "aa",			//任务名称
                				"startTimeOfDay": "15:50:00",  //启动时间
                				"volume": 40,			//播放音量
               					"weekDays": [1, 2, 3, 4, 5]  //每周执行模式有效
								“bcType”: “mic”   //广播类型（mic：麦克风，file：文件）
								“taskType”: //任务类型（realtime：实时，timed：定时）
}
]
}

回调事例： 
{"error":"success","id":1000,"method":"audioManager.onGetBCTaskList","params":{"loginHandle":48701072,"tasks":[{"deviceIds":[16990],"duration":{"enable":false,"seconds":0},"enable":true,"execMode":"Single","expire":{"beginDate":"2021-06-02","enable":true,"endDate":"2021-06-02"},"fileIds":[7,1,5,4],"id":1,"loop":{"enable":true,"times":1},"name":"aa","startTimeOfDay":"15:50:00","volume":40,"weekDays":[1,2,3,4,5]},{"deviceIds":[16990],"duration":{"enable":false,"seconds":0},"enable":true,"execMode":"Single","expire":{"beginDate":"2021-06-04","enable":true,"endDate":"2021-06-04"},"fileIds":[3,4],"id":2,"loop":{"enable":true,"times":1},"name":"qq","startTimeOfDay":"09:00:28","volume":60,"weekDays":[1,2,3,4,5]}}}


2.37 上传广播任务回调
接口：onUploadBCTask(msg)
说明：上传广播任务回调
msg: method：” audioManager.onUploadBCTask”
 error： “success”
id:	“1000” //上传广播任务接口请求ID
params： { 
					“loginHandle“: 123,
					“taskId”: 3432
				}
回调事例： 
{"error":"success","id":1001,"method":"audioManager.onUploadBCTask","params":{"loginHandle":48701072,"taskId":3435973836}}
2.38 修改广播任务回调
接口：onEditBCTask(msg)
说明：修改广播任务回调
msg: method：” audioManager.onEditBCTask”
 error： “success”
id:	“1000” //上传广播任务接口请求ID
params： { 
					“loginHandle“: 123
				}
回调事例： 
{"error":"success","id":1001,"method":"audioManager.onEditBCTask","params":{"loginHandle":48701072}}
2.39 删除广播任务回调
接口：onDeleteBCTask(msg)
说明：删除广播任务回调
msg: method：” audioManager.onDeleteBCTask”
 error： “success”
id:	“1000” //上传广播任务接口请求ID
params： { 
					“loginHandle“: 123
				}
回调事例： 
{"error":"success","id":1001,"method":"audioManager. onDeleteBCTask ","params":{"loginHandle":48701072}}
2.40 配置透传回调
接口：onConfigTunnel (msg)
说明：配置透传回调
msg: method：” deviceManager.onConfigTunnel”
 error： “success”
params： { 
					“loginHandle“: 123,
					“deviceId”: 1120006，
					“tunnelData“: “{\"id\":2000,\"result\":true}”
				}
回调事例： 
{"method":"deviceManager.onConfigTunnel","params":{"deviceId":1120006,"loginHandle":56814528,"tunnelData":"{\"id\":2000,\"result\":true}\n"}}
2.41 获取设备额外信息回调
接口：onGetDeviceExtra (msg)
说明：获取设备额外信息回调（设备坐标等）
msg: method：” deviceManager.onGetDeviceExtra”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
			list: [{
				deviceId: 16990,
				location: “120.1211_30.1213”,
				contact: “张三”,
				phone: “13012341234”.
				remark: “”
},
{
				deviceId: 16991,
				location: “120.1211_30.1213”,
				contact: “张三”,
				phone: “13012341234”.
				remark: “”
}

],
				}
回调事例： 
{"error":"success","id":1000,"method":"deviceManager.onGetDeviceExtra","params":{"list":[{"contact":"","deviceId":16990,"location":"","phone":"","remark":""},{"contact":"","deviceId":19105,"location":"118.669707_36.134738","phone":"","remark":""},{"contact":"小张","deviceId":971651,"location":"","phone":"130123456","remark":"XX停车场出口"}],"loginHandle":56815608}}
2.42 设置设备额外信息回调
接口：onSetDeviceExtra (msg)
说明：设置设备额外信息回调（设备坐标等）
msg: 	method：” deviceManager.onSetDeviceExtra”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
			deviceId: 1120006,
				}
回调事例： 
{"error":"success","id":1002,"method":"deviceManager.onSetDeviceExtra","params":{"deviceId":1120006,"loginHandle":56814744}}
2.43 二维码下发回调
接口：onConfigPayStatus(msg)
说明：二维码下发回调
msg: 	 
loginHandle：56815608， 
deviceId: 1120006,
reqData: {
	id: 3,
	deviceId: 112006
},
tunnelData: "{\"id\":3,\"result\":true}\n"}"				
回调事例： 
{"deviceId":926214,"loginHandle":52218456,"reqData":"{"id":3,"deviceId":112006},"tunnelData":"{\"id\":3,\"result\":true}\n"}"
2.44 获取设备音量回调
接口：onGetDeviceVolume (msg)
说明：获取设备音量回调
msg: 	 
loginHandle：56815608， 
deviceId: 1120006,
reqData: {
	id: 3,
	deviceId: 112006
type: “output” 
},
tunnelData: "{\"id\":3, \"deviceId\":112006,\"params\":[30],\"result\":true}\n"}"				
回调事例： 
{"deviceId":926214,"loginHandle":52218456,"reqData":"{"id":3,"deviceId":112006,"type":"output"},"tunnelData":"{\"id\":3,\"deviceId\":112006,\"params\":[30],\"result\":true}\n"}"
2.45 设置设备音量回调
接口：onSetDeviceVolume (msg)
说明：设置设备音量回调
msg: 	 
loginHandle：56815608， 
deviceId: 1120006,
reqData: {
	id: 3,
	deviceId: 112006,
type: “output” ,
volume: [30]
},
tunnelData: "{\"id\":3, \"result\":true}\n"}"				
回调事例： 
{"deviceId":926214,"loginHandle":52218456,"reqData":"{"id":3,"deviceId":112006,"type":"output","volume":[30]},"tunnelData":"{\"id\":3,\"result\":true}\n"}"
2.46 获取广播音量回调
接口：onGetBroadcastVolume (msg)
说明：获取广播音量回调
msg: 	 
loginHandle：56815608， 
deviceId: 1120006,
reqData: {
	id: 3,
	deviceId: 112006 
},
tunnelData:"{\"id\":3,\"params\":{\"volume\":[30]},\"result\":true}\n"}"				
回调事例： 
{"deviceId":926214,"loginHandle":52218456,"reqData":"{"id":3,"deviceId":112006},"tunnelData":"{\"id\":3,\"params\":{\"volume\":[30]},\"result\":true}\n"}"
2.47 设置广播音量回调
接口：onSetBroadcastVolume (msg)
说明：设置广播音量回调
msg: 	 
loginHandle：56815608， 
deviceId: 1120006,
reqData: {
	id: 3,
	deviceId: 112006, 
	volume: [30]
},
tunnelData: "{\"id\":3, \"result\":true}\n"}"				
回调事例： 
{"deviceId":926214,"loginHandle":52218456,"reqData":"{"id":3,"deviceId":112006,"volume":[30]},"tunnelData":"{\"id\":3,\"result\":true}\n"}"
2.48 录像列表回调
接口：onGetRecordList (msg)
说明：录像列表回调（搜索的记录可能会存在时间在搜索的时间之外的记录，需前端自己做过滤）
msg: 	 
loginHandle：56815608， 
deviceId: 980880,
recordCount: 4,
records: {
	begintime: 1639078322,  //开始时间
	duration: 25,  //时长
},		
回调事例： 
{"method":"deviceManager.onGetRecordList","error":"success","params":{"loginHandle":48362560,"deviceId":980880,"recordCount":4,"records":[{"begintime":1639078161,"duration":25},{"begintime":1639078322,"duration":25},{"begintime":1639078364,"duration":35},{"begintime":1639127701,"duration":80}]}}
2.49 搜索日志回调
接口：onSearchLog(msg)
说明：获取系统配置回调
msg: 	method：”configManager.onSearchLog”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
pageIndex: 0,		//页数index
totalPages: 2,  //总数
infor:[{
device: “HD-14531”,  //设备名称
				event:”3”,   //事件，值参考附录参数Log ulEvent
				group: “test1,       //分组
				sn: “14531”,		// 序列号
				source: “linuxserver”,	//来源
				time:“2022-06-15 23:09:55“	,
				type: “2“, //事件，值参考附录参数Log ulType
				user: “”
}]
				}
回调事例： 
{"error":"success","id":17,"method":"configManager.onSearchLog","params":{"infor":[{"device":"HD-7F1CA748","event":"3","group":"test","sn":"923175","source":"test","time":"2023-01-12 18:06:25","type":"2","user":""},{"device":"HD-7F1CA748","event":"8","group":"test","sn":"923175","source":"test","time":"2023-01-11 15:43:54","type":"2","user":""},{"device":"HD-7F1CA748","event":"4","group":"test","sn":"923175","source":"test","time":"2023-01-11 15:43:40","type":"2","user":"alarm"},{"device":"HD-7F1CA748","event":"3","group":"test","sn":"923175","source":"test","time":"2023-01-11 15:43:26","type":"2","user":""}],"loginHandle":46847088,"pagIndex":0,"totalPages":4}}
2.50 linux服务器接口
2.50.1 获取系统配置回调
接口：onGetSystemCfg(msg)
说明：获取系统配置回调
msg: 	method：”configManager.onGetSystemCfg”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
			appName: “linuxserver”,  //服务器名称
			alarmingtime: 30000,   //报警超时事件（毫秒）
			appPort: “9534,       //服务器端口
			leveltime: 20000,		// 分级通知间隔（毫秒）
			password: “123456”,	//服务器密码
			playrttimeout: 0		//监视超时时间 （毫秒）
			recordDisk: [{“path”: “./recordTest”}] //录像地址
			recordEnable: 1		//录像启用/禁用 0：禁用，1：启用
			recordRemainSpace: 5   //磁盘剩余空间录像覆盖（G）
				}
回调事例： 
{"error":"success","id":10005,"method":"configManager.onGetSystemCfg","params":{"alarmingtime":30000,"appName":"Linuxserver","appPort":9534,"leveltime":20000,"loginHandle":51502344,"password":"123456","playrttimeout":0,"recordDisk":[{"path":"./recodeTest"}],"recordEnable":1,"recordRemainSpace":5}}
2.50.2 设置系统配置回调
接口：onSetSystemCfg(msg)
说明：设置系统配置回调
msg: 	method：”configManager.onSetSystemCfg”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
				}
回调事例： 
{"error":"success","id":10013,"method":"configManager.onSetSystemCfg","params":{"loginHandle":51502344}}
2.50.3 批量控制DO回调
接口：onOpenDo(msg)
说明：批量控制DO回调
msg: 	method：”rsp_openDo”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
				}
回调事例： 
{"error":"success","id":10015,"method":"rsp_openDo","params":{"loginHandle":60869832}}
2.50.4 获取铃声规则列表回调
接口：onGetTipAudioInfo(msg)
说明：获取所有铃声规则列表回调
msg: 	method：”rsp_tipAudioInforGet”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
allInfor: [
	{
		“name”: “1”,
“ruleid”: 1,
		“ruleindex”: 12,
“trigSrc”: 0,
“timeSection”: [
	{
		“startH”: 0,
“startM”: 0,

“endH”: 1,
“starM: 59,
“volume”: 60,
“enable”:1, 
}，
{
		“startH”: 2,
“startM”: 0,

“endH”: 23,
“starM: 59,
“volume”: 60,
“enable”:0, 
}
]

}
]
				}
回调事例： 
{"error":"success","id":10019,"method":"rsp_tipAudioInforGet","params":{"allInfor":[{"name":"1","ruleid":357773176,"ruleindex":63,"timeSection":[{"enable":1,"endH":23,"endM":59,"startH":0,"startM":0,"volume":40},{"enable":0,"endH":0,"endM":0,"startH":0,"startM":0,"volume":0},{"enable":0,"endH":0,"endM":0,"startH":0,"startM":0,"volume":0},{"enable":0,"endH":0,"endM":0,"startH":0,"startM":0,"volume":0},{"enable":0,"endH":0,"endM":0,"startH":0,"startM":0,"volume":0},{"enable":0,"endH":0,"endM":0,"startH":0,"startM":0,"volume":0}],"trigsrc":1}],"loginHandle":60869832}}
2.50.5 添加修改铃声规则回调
接口：onAddOrModTipAudio(msg)
说明：添加修改铃声规则回调
msg: 	method：”rsp_tipAudioAddOrMod”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
ruleid: 357773176
				}
回调事例： 
{"error":"success","id":10020,"method":"rsp_tipAudioAddOrMod","params":{"loginHandle":60869832,"ruleid":357773176}}
2.50.6 获取设备铃声规则回调
接口：onGetTipAudioName(msg)
说明：获取设备铃声规则回调
msg: 	method：”rsp_tipAudioGetName”
 error： “success”
id：		“1000“，
params： { 
loginHandle：56815608， 
allCameras: [
	{
		“cameraId”: 923175,
“ruleName”:”1”,
“ruleid”: 357773176,
}
]
				}
回调事例： 
{"error":"success","id":10023,"method":"rsp_tipAudioGetName","params":{"allCameras":[{"cameraId":923175,"ruleName":"1","ruleid":357773176}],"loginHandle":60869832}}
2.50.7 开始升级或导入回调
接口：onStartUpdate(msg)
说明：开始升级或导入回调
msg: 	method：”rsp_startUpdate”
 error： “success”
id：		“1000“，
params： { 
loginHandle：54252968， 
cameraId: 980915
				}
回调事例： 
{"error":"success","id":10012,"method":"rsp_startUpdate","params":{"cameraId":980915,"loginHandle":54252968}

2.50.8 停止升级回调
接口：onStopUpdate(msg)
说明：停止升级回调
msg: 	method：”rsp_stopUpdate”
 error： “success”
id：		“1000“，
params： { 
loginHandle：54252968， 
cameraId: 980915
				}
回调事例： 
{"error":"success","id":10013,"method":"rsp_stopUpdate","params":{"cameraId":980915,"loginHandle":54252968}
2.50.9 获取升级或导入状态回调
接口：onQueryUpdateStatus(msg)
说明：获取升级或导入状态回调，只有当stateStr为“write_over”才表示完全升级或导入成功
msg: 	method：”rsp_queryUpdateStatus”
 error： “success”
id：		“1000“，
params： { 
loginHandle：54252968， 
cameraId: 980915，
persent: 100, //进度
stateStr: “send_ing(or write_ing/write_over/wrong)” //状态字符串 
				}
回调事例： 
{"error":"success","id":10014,"method":"rsp_queryUpdateStatus","params":{"cameraId":980915,"loginHandle":54252968,"persent":100,"stateStr":"write_over"}}
2.50.10 获取设备配置信息回调
接口：onGetCameraInfo(msg)
说明：获取设备配置信息回调
msg: 	method：”rsp_getCameraInfor”
 error： “success”
id：		“1000“，
params： { 
loginHandle：54252968， 
allCameras: [
{
cameraId: 923175,
contact: “张三”,
location: ‘121.1222_90.1212’,
phone: ‘13011111111’,
remark: ‘’
mainVersion: ‘..3.3.22ba’
}]
				}
回调事例： 
{"error":"success","id":10007,"method":"rsp_getCameraInfor","params":{"allCameras":[{"cameraId":923175,"contact":"","location":"","mainVersion":"1.3.1.15la","phone":"","remark":""},{"cameraId":980915,"contact":"","location":"","mainVersion":"1.3.3.45ma","phone":"","remark":""}],"loginHandle":51107376}}
2.50.11 删除设备回调
接口：onDeleteDevice(msg)
说明：删除设备回调
msg: 	method：”rsp_reMoveCamera”
 error： “success”
id：		“1000“，
params： { 
loginHandle：54252968,
Returns: [{
cameraId: 1304287,
return: 1
}]
				}
回调事例： 
{"error":"success","id":10008,"method":"rsp_reMoveCamera","params":{"loginHandle":51107376,"returns":[{"cameraId":1304287,"return":1}]}}

2.50.12设备被移动回调
接口：onDeviceMove (object)
说明：设备所属分组发生改变后会收到此回调
object：method：” configManager.onDeviceMove”
 error： “success”
params： {
	list: [{
		deviceId: 111,
		deviceName: “aaa”,
		deviceType: “alarm”,
		action: “Offline”,
parentId: ‘128’
}
],
			loginHandle: 123
}
回调事例： 
{"method":"configManager.onDeviceMove","error":"success","params":{"list":[{"deviceId":912462,"parentId":127,"deviceName":"HD-7FE2B48E","deviceType":"Alarm","action":"Offline"}],"loginHandle":61456512}}

2.51 获取广播任务ID回调
接口：onGetTaskIdList(msg)
说明：获取定时广播任务ID回调
msg:    method：”rsp_getTaskIdList”
 error： “success”
id:	“1000” //接口请求ID
params： {
taskIds: [1,2,7],
			loginHandle: 123
}
	
回调事例： 
{"error":"success","id":13,"method":"rsp_getTaskIdList","params":{"loginHandle":60482864,"taskIds":[1,2,7]}}
2.52 根据任务ID获取广播任务详情回调
接口：onGetTaskIdInfo(msg)
说明：根据任务ID获取广播任务详情回调
msg:   method：”rsp_getTaskIdInfo”
 error： “success”
id:	“1000” //获取文件列表接口请求ID
params： { 
					“loginHandle“: 123,
					“taskInfoList”: [
							{
								"deviceIds": [16990],  //播放设备ID
               					"duration": {			//按时长执行
                   					 "enable": false,	//启用禁用（与loop二选一）
                   					 "seconds": 0		//时长（秒）
                				},
"loop": {				//按次数执行
                   					"enable": true,   //启用禁用（与duration二选一）
                    				"times": 1		//次数
               					},
                				"enable": true,		//任务启用禁用
                                "execMode": "Single",	//模式（EveryWeek，EveryDay、Single）
                				"expire": {			//任务有效期
                   					"beginDate": "2021-06-02",
                    				"enable": true,
                    				"endDate": "2021-06-02"
               					},
                				"fileIds": [7, 1, 5, 4],
                				"id": 1,  				//文件ID
                				"name": "aa",			//任务名称
                				"startTimeOfDay": "15:50:00",  //启动时间
                				"volume": 40,			//播放音量
               					"weekDays": [1, 2, 3, 4, 5]  //每周执行模式有效
								“bcType”: “mic”   //广播类型（mic：麦克风，file：文件）
								“taskType”: //任务类型（realtime：实时，timed：定时）
}
]
}
	
回调事例： 
{"error":"success","id":14,"method":"rsp_getTaskIdInfo","params":{"loginHandle":60482864,"taskInfoList":[{"bcType":"file","deviceIds":[],"duration":{"enable":false,"seconds":0},"enable":true,"execMode":"Single","expire":{"beginDate":"2022-11-28","enable":true,"endDate":"2022-11-28"},"fileIds":[142],"id":1,"loop":{"enable":true,"times":1},"name":"44","startTimeOfDay":"11:21:58","taskType":"timed","volume":50,"weekDays":[1,2,3,4,5]}]}}

附录
参数说明：
deviceType: “Alarm”   报警设备
			“Ipc”    联动设备
action： “Normal”  设备在线或报警已被处理
		“Offline”   设备离线
		“Start”	  设备正在呼叫
		“Dealing”  报警正在被处理
groupType: “normal”  普通分组
		   “linkage“ 联动分组
		   “defense“ 联防分组
notify code: “DeviceStatus”    设备状态
			“BroadcastStatus” 广播状态
Log ulType： 1：系统日志，2：报警设备日志，3：分组日志，4：用户日志
Log ulEvent:  
系统日志：
            0x00000001：系统启动
            0x00000002：系统关闭
            0x00000003：系统异常
            0x00000006：报警中心名称修改
            0x00000007：应用端口修改
            0x00000008：系统密码修改
			0x00000009：报警超时时间修改
            0x0000000A：授权配置导入
			0x0000000B：电子地图导入
报警设备日志：
			0x10000000：报警设备网络日志
            0x00000001：报警设备注册登入
			0x00000002：报警设备断线
			0x20000000：报警设备报警日志
            0x00000003：报警设备报警
			0x00000004：报警设备报警处理
			0x00000008： 报警设备报警结束
            0x00000005：报警设备报警超时
            0x30000000：报警设备配置日志
			0x00000006：报警设备修改名称
            0x00000007：报警设备移动
			0x00000009：报警设备删除
分组日志：
			0x00000001：分组添加
            0x00000002：分组删除
			0x00000003：分组修改名称
			0x00000004：分组移动
用户日志：
            0x20000000：用户网络日志
			0x00000004：用户登入
			0x00000005：用户登出
            0x30000000：用户操作日志
            0x00000008：用户观看报警设备
			0x00000009：用户停止观看报警设备
            0x0000000C：用户启用报警设备录像
			0x0000000D：用户停止报警设备录像
			0x10000000：用户配置日志
            0x00000001：用户添加
			0x00000002：用户删除
			0x00000003：用户修改密码
            0x00000006：用户授权分组
			0x00000007：用户取消分组授权	




			
