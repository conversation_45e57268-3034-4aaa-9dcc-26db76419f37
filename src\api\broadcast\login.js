/**
 * Chrome SDK 登录接口封装
 * 基于Chrome SDK接口文档
 */

/**
 * Chrome SDK 登录管理类
 */
class ChromeSDKLogin {
  constructor() {
    this.dhWeb = null;
    this.loginHandle = null;
    this.isLoggedIn = false;
    this.callbacks = {
      onLogin: null,
      onLogout: null,
      onError: null
    };
  }

  /**
   * 初始化Chrome SDK
   * @param {Object} dhWebInstance - DHAlarmWeb实例
   */
  init(dhWebInstance) {
    this.dhWeb = dhWebInstance;
    this.setupCallbacks();
  }

  /**
   * 设置回调函数
   */
  setupCallbacks() {
    if (!this.dhWeb) return;

    // 登录回调
    this.dhWeb.onLogin = (result) => {
      console.log('登录回调:', result);
      if (result.error === 'success') {
        this.loginHandle = result.params.loginHandle;
        this.isLoggedIn = true;
        if (this.callbacks.onLogin) {
          this.callbacks.onLogin(result);
        }
      } else {
        this.isLoggedIn = false;
        if (this.callbacks.onError) {
          this.callbacks.onError(result);
        }
      }
    };

    // SDK异常回调
    this.dhWeb.onDHAlarmWebError = (error) => {
      console.error('SDK异常:', error);
      if (error.msg && error.msg.error === 'loginTimeout') {
        this.isLoggedIn = false;
        this.loginHandle = null;
      }
      if (this.callbacks.onError) {
        this.callbacks.onError(error);
      }
    };

    // 服务器断开回调
    this.dhWeb.onAlarmServerClosed = () => {
      console.log('服务器连接断开');
      this.isLoggedIn = false;
      this.loginHandle = null;
      if (this.callbacks.onLogout) {
        this.callbacks.onLogout();
      }
    };
  }

  /**
   * 登录服务器
   * @param {string} username - 登录账号
   * @param {string} password - 登录密码
   * @param {string} serverAddr - 服务器IP地址
   * @returns {Promise} 登录Promise
   */
  login(username, password, serverAddr) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      // 设置临时回调
      const originalOnLogin = this.callbacks.onLogin;
      const originalOnError = this.callbacks.onError;

      this.callbacks.onLogin = (result) => {
        this.callbacks.onLogin = originalOnLogin;
        this.callbacks.onError = originalOnError;
        resolve(result);
        if (originalOnLogin) originalOnLogin(result);
      };

      this.callbacks.onError = (error) => {
        this.callbacks.onLogin = originalOnLogin;
        this.callbacks.onError = originalOnError;
        reject(error);
        if (originalOnError) originalOnError(error);
      };

      // 调用SDK登录接口
      try {
        this.dhWeb.login(username, password, serverAddr);
      } catch (error) {
        this.callbacks.onLogin = originalOnLogin;
        this.callbacks.onError = originalOnError;
        reject(error);
      }
    });
  }

  /**
   * 登出服务器
   * @returns {Promise} 登出Promise
   */
  logout() {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      if (!this.loginHandle) {
        reject(new Error('未登录或登录句柄无效'));
        return;
      }

      try {
        this.dhWeb.logout(this.loginHandle);
        this.isLoggedIn = false;
        this.loginHandle = null;
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 设置登录成功回调
   * @param {Function} callback - 回调函数
   */
  onLoginSuccess(callback) {
    this.callbacks.onLogin = callback;
  }

  /**
   * 设置登出回调
   * @param {Function} callback - 回调函数
   */
  onLogoutCallback(callback) {
    this.callbacks.onLogout = callback;
  }

  /**
   * 设置错误回调
   * @param {Function} callback - 回调函数
   */
  onErrorCallback(callback) {
    this.callbacks.onError = callback;
  }

  /**
   * 获取登录状态
   * @returns {boolean} 是否已登录
   */
  getLoginStatus() {
    return this.isLoggedIn;
  }

  /**
   * 获取登录句柄
   * @returns {number|null} 登录句柄
   */
  getLoginHandle() {
    return this.loginHandle;
  }
}

// 创建单例实例
const chromeSDKLogin = new ChromeSDKLogin();

export default chromeSDKLogin;