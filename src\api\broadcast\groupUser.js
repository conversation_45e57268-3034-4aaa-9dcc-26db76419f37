/**
 * Chrome SDK 分组和用户管理接口
 * 基于Chrome SDK接口文档
 */
import chromeSDK from './chromeSDK.js';

/**
 * 分组和用户管理类
 */
class GroupUserManager {
  constructor() {
    this.sdk = chromeSDK;
  }

  /**
   * 生成请求ID
   * @returns {number} 请求ID
   */
  generateRequestId() {
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  // ==================== 分组管理接口 ====================

  /**
   * 添加分组
   * @param {string} groupType - 分组类型
   * @param {string} groupName - 分组名称
   * @param {string} groupContact - 分组联系人
   * @param {string} groupPhone - 分组联系电话
   * @returns {Promise} 操作结果
   */
  addGroup(groupType, groupName, groupContact = '', groupPhone = '') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onAddGroup;
      this.sdk.callbacks.onAddGroup = (result) => {
        this.sdk.callbacks.onAddGroup = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.addGroup(requestId, loginHandle, groupType, groupName, groupContact, groupPhone);
      } catch (error) {
        this.sdk.callbacks.onAddGroup = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 编辑分组
   * @param {number} groupId - 分组ID
   * @param {string} groupName - 分组名称
   * @param {string} groupContact - 分组联系人
   * @param {string} groupPhone - 分组联系电话
   * @returns {Promise} 操作结果
   */
  editGroup(groupId, groupName, groupContact = '', groupPhone = '') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onEditGroup;
      this.sdk.callbacks.onEditGroup = (result) => {
        this.sdk.callbacks.onEditGroup = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.editGroup(requestId, loginHandle, groupId, groupName, groupContact, groupPhone);
      } catch (error) {
        this.sdk.callbacks.onEditGroup = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 删除分组
   * @param {number} groupId - 分组ID
   * @returns {Promise} 操作结果
   */
  delGroup(groupId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onDelGroup;
      this.sdk.callbacks.onDelGroup = (result) => {
        this.sdk.callbacks.onDelGroup = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.delGroup(requestId, loginHandle, groupId);
      } catch (error) {
        this.sdk.callbacks.onDelGroup = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 用户管理接口 ====================

  /**
   * 获取用户列表
   * @returns {Promise} 用户列表
   */
  getUsers() {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onUserList;
      this.sdk.callbacks.onUserList = (result) => {
        this.sdk.callbacks.onUserList = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getUsers(requestId, loginHandle);
      } catch (error) {
        this.sdk.callbacks.onUserList = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 获取用户分组
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户分组
   */
  getUserGroups(userId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onUserGroups;
      this.sdk.callbacks.onUserGroups = (result) => {
        this.sdk.callbacks.onUserGroups = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getUserGroups(requestId, loginHandle, userId);
      } catch (error) {
        this.sdk.callbacks.onUserGroups = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 添加用户
   * @param {string} userName - 用户名
   * @param {string} password - 密码
   * @returns {Promise} 操作结果
   */
  addUser(userName, password) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onAddUser;
      this.sdk.callbacks.onAddUser = (result) => {
        this.sdk.callbacks.onAddUser = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.addUser(requestId, loginHandle, userName, password);
      } catch (error) {
        this.sdk.callbacks.onAddUser = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 编辑用户密码
   * @param {number} userId - 用户ID
   * @param {string} password - 用户密码
   * @returns {Promise} 操作结果
   */
  editPassword(userId, password) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onEditPassword;
      this.sdk.callbacks.onEditPassword = (result) => {
        this.sdk.callbacks.onEditPassword = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.editPassword(requestId, loginHandle, userId, password);
      } catch (error) {
        this.sdk.callbacks.onEditPassword = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 删除用户
   * @param {number} userId - 用户ID
   * @returns {Promise} 操作结果
   */
  delUser(userId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onDelUser;
      this.sdk.callbacks.onDelUser = (result) => {
        this.sdk.callbacks.onDelUser = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.delUser(requestId, loginHandle, userId);
      } catch (error) {
        this.sdk.callbacks.onDelUser = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 授权分组
   * @param {number} userId - 用户ID
   * @param {number[]} groupIds - 分组ID数组
   * @returns {Promise} 操作结果
   */
  authorizeGroup(userId, groupIds) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onAuthorizeGroup;
      this.sdk.callbacks.onAuthorizeGroup = (result) => {
        this.sdk.callbacks.onAuthorizeGroup = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.authorizeGroup(requestId, loginHandle, userId, groupIds);
      } catch (error) {
        this.sdk.callbacks.onAuthorizeGroup = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 设备管理接口 ====================

  /**
   * 编辑设备
   * @param {number} deviceId - 设备ID
   * @param {string} deviceName - 设备名称
   * @param {string} deviceContact - 设备联系人
   * @param {string} devicePhone - 设备联系电话
   * @returns {Promise} 操作结果
   */
  editDevice(deviceId, deviceName, deviceContact = '', devicePhone = '') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onEditDevice;
      this.sdk.callbacks.onEditDevice = (result) => {
        this.sdk.callbacks.onEditDevice = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.editDevice(requestId, loginHandle, deviceId, deviceName, deviceContact, devicePhone);
      } catch (error) {
        this.sdk.callbacks.onEditDevice = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 移动设备
   * @param {number} deviceId - 设备ID
   * @param {number} groupId - 分组ID
   * @returns {Promise} 操作结果
   */
  moveDevice(deviceId, groupId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onMoveDevice;
      this.sdk.callbacks.onMoveDevice = (result) => {
        this.sdk.callbacks.onMoveDevice = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.moveDevice(requestId, loginHandle, deviceId, groupId);
      } catch (error) {
        this.sdk.callbacks.onMoveDevice = originalCallback;
        reject(error);
      }
    });
  }
}

// 创建单例实例
const groupUserManager = new GroupUserManager();

export default groupUserManager;
