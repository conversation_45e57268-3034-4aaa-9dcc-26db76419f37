/**
 * Chrome SDK 接口封装
 * 基于Chrome SDK接口文档
 */
import chromeSDKLogin from './login.js';

/**
 * Chrome SDK 主要接口类
 */
class ChromeSDK {
  constructor() {
    this.dhWeb = null;
    this.loginManager = chromeSDKLogin;
    this.callbacks = {};
  }

  /**
   * 初始化Chrome SDK
   * @param {Object} dhWebInstance - DHAlarmWeb实例
   */
  init(dhWebInstance) {
    this.dhWeb = dhWebInstance;
    this.loginManager.init(dhWebInstance);
    this.setupCallbacks();
  }

  /**
   * 设置所有回调函数
   */
  setupCallbacks() {
    if (!this.dhWeb) return;

    // 设备列表回调
    this.dhWeb.onDeviceList = (result) => {
      console.log('设备列表回调:', result);
      if (this.callbacks.onDeviceList) {
        this.callbacks.onDeviceList(result);
      }
    };

    // 设备通知回调
    this.dhWeb.onNotify = (result) => {
      console.log('设备通知回调:', result);
      if (this.callbacks.onNotify) {
        this.callbacks.onNotify(result);
      }
    };

    // 播放回调
    this.dhWeb.onPlayRT = (result) => {
      console.log('播放回调:', result);
      if (this.callbacks.onPlayRT) {
        this.callbacks.onPlayRT(result);
      }
    };

    // 分组列表回调
    this.dhWeb.onGroupList = (result) => {
      console.log('分组列表回调:', result);
      if (this.callbacks.onGroupList) {
        this.callbacks.onGroupList(result);
      }
    };

    // 用户列表回调
    this.dhWeb.onUserList = (result) => {
      console.log('用户列表回调:', result);
      if (this.callbacks.onUserList) {
        this.callbacks.onUserList(result);
      }
    };

    // 广播相关回调
    this.dhWeb.onStartBroadcast = (result) => {
      console.log('广播开始回调:', result);
      if (this.callbacks.onStartBroadcast) {
        this.callbacks.onStartBroadcast(result);
      }
    };

    this.dhWeb.onGetAudioFileList = (result) => {
      console.log('音频文件列表回调:', result);
      if (this.callbacks.onGetAudioFileList) {
        this.callbacks.onGetAudioFileList(result);
      }
    };

    this.dhWeb.onUploadAudioFile = (result) => {
      console.log('音频文件上传回调:', result);
      if (this.callbacks.onUploadAudioFile) {
        this.callbacks.onUploadAudioFile(result);
      }
    };

    this.dhWeb.onUploadAudioFileProgress = (progress) => {
      console.log('音频文件上传进度:', progress);
      if (this.callbacks.onUploadAudioFileProgress) {
        this.callbacks.onUploadAudioFileProgress(progress);
      }
    };

    this.dhWeb.onGetBCTaskList = (result) => {
      console.log('广播任务列表回调:', result);
      if (this.callbacks.onGetBCTaskList) {
        this.callbacks.onGetBCTaskList(result);
      }
    };

    this.dhWeb.onUploadBCTask = (result) => {
      console.log('上传广播任务回调:', result);
      if (this.callbacks.onUploadBCTask) {
        this.callbacks.onUploadBCTask(result);
      }
    };

    this.dhWeb.onEditBCTask = (result) => {
      console.log('修改广播任务回调:', result);
      if (this.callbacks.onEditBCTask) {
        this.callbacks.onEditBCTask(result);
      }
    };

    this.dhWeb.onDeleteBCTask = (result) => {
      console.log('删除广播任务回调:', result);
      if (this.callbacks.onDeleteBCTask) {
        this.callbacks.onDeleteBCTask(result);
      }
    };

    // 录像相关回调
    this.dhWeb.onGetRecordList = (result) => {
      console.log('录像列表回调:', result);
      if (this.callbacks.onGetRecordList) {
        this.callbacks.onGetRecordList(result);
      }
    };

    // 设备额外信息回调
    this.dhWeb.onGetDeviceExtra = (result) => {
      console.log('获取设备额外信息回调:', result);
      if (this.callbacks.onGetDeviceExtra) {
        this.callbacks.onGetDeviceExtra(result);
      }
    };

    this.dhWeb.onSetDeviceExtra = (result) => {
      console.log('设置设备额外信息回调:', result);
      if (this.callbacks.onSetDeviceExtra) {
        this.callbacks.onSetDeviceExtra(result);
      }
    };

    // 音量相关回调
    this.dhWeb.onGetDeviceVolume = (result) => {
      console.log('获取设备音量回调:', result);
      if (this.callbacks.onGetDeviceVolume) {
        this.callbacks.onGetDeviceVolume(result);
      }
    };

    this.dhWeb.onSetDeviceVolume = (result) => {
      console.log('设置设备音量回调:', result);
      if (this.callbacks.onSetDeviceVolume) {
        this.callbacks.onSetDeviceVolume(result);
      }
    };

    this.dhWeb.onGetBroadcastVolume = (result) => {
      console.log('获取广播音量回调:', result);
      if (this.callbacks.onGetBroadcastVolume) {
        this.callbacks.onGetBroadcastVolume(result);
      }
    };

    this.dhWeb.onSetBroadcastVolume = (result) => {
      console.log('设置广播音量回调:', result);
      if (this.callbacks.onSetBroadcastVolume) {
        this.callbacks.onSetBroadcastVolume(result);
      }
    };

    // 系统配置回调
    this.dhWeb.onGetSystemCfg = (result) => {
      console.log('获取系统配置回调:', result);
      if (this.callbacks.onGetSystemCfg) {
        this.callbacks.onGetSystemCfg(result);
      }
    };

    this.dhWeb.onSetSystemCfg = (result) => {
      console.log('设置系统配置回调:', result);
      if (this.callbacks.onSetSystemCfg) {
        this.callbacks.onSetSystemCfg(result);
      }
    };

    // 日志搜索回调
    this.dhWeb.onSearchLog = (result) => {
      console.log('搜索日志回调:', result);
      if (this.callbacks.onSearchLog) {
        this.callbacks.onSearchLog(result);
      }
    };
  }

  /**
   * 获取登录管理器
   * @returns {ChromeSDKLogin} 登录管理器实例
   */
  getLoginManager() {
    return this.loginManager;
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return this.loginManager.getLoginStatus();
  }

  /**
   * 获取登录句柄
   * @returns {number|null} 登录句柄
   */
  getLoginHandle() {
    return this.loginManager.getLoginHandle();
  }

  /**
   * 设置回调函数
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    this.callbacks[eventName] = callback;
  }

  /**
   * 移除回调函数
   * @param {string} eventName - 事件名称
   */
  off(eventName) {
    delete this.callbacks[eventName];
  }

  // ==================== 视频播放相关接口 ====================

  /**
   * 播放视频
   * @param {Object} video - 播放视频标签
   * @param {number} deviceId - 播放设备id
   * @param {boolean} isTalk - 是否打开对讲
   * @returns {Promise} 播放结果
   */
  playRT(video, deviceId, isTalk = false) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      try {
        this.dhWeb.playRT(video, deviceId, loginHandle, isTalk);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 关闭视频
   * @param {number} deviceId - 播放设备id
   * @returns {Promise} 关闭结果
   */
  stopRT(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      try {
        this.dhWeb.stopRT(deviceId, loginHandle);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 打开设备的数字输出
   * @param {number} deviceId - 播放设备id
   * @param {number} doIndex - 数字输出的索引号，目前支持1和2
   * @returns {Promise} 操作结果
   */
  doControl(deviceId, doIndex) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      try {
        this.dhWeb.doControl(deviceId, loginHandle, doIndex);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 打开对讲
   * @param {number} deviceId - 播放设备id
   * @returns {Promise} 操作结果
   */
  startTalk(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      try {
        this.dhWeb.startTalk(deviceId);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 播放设备音频
   * @param {number} deviceId - 播放设备id
   * @returns {Promise} 操作结果
   */
  playDeviceAudio(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      try {
        this.dhWeb.playDeviceAudio(deviceId);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 开始侦听
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 操作结果
   */
  startDetect(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      try {
        this.dhWeb.startDetect(deviceId);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 停止侦听
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 操作结果
   */
  stopDetect(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      try {
        this.dhWeb.stopDetect(deviceId);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }
}

// 创建单例实例
const chromeSDK = new ChromeSDK();

export default chromeSDK;
