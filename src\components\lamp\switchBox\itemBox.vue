<template>
  <div class="itemBox">
    <div class="top-box">
      <div class="icon-title">
        <div class="title">{{ props.item.deviceName || '集中控制器' }}</div>
        <div class="item-edit">
          <el-icon style="font-size: 22px; color: #333333; margin-right: 20px;"  @click="deletelightpole(props.item)"><Delete /></el-icon>
          <el-icon style="font-size: 22px; color: #333333;" @click="editlightpole(props.item)"><Edit /></el-icon>
        </div>
      </div>
      <div class="totalBtn-box">
        <div class="total-right">
          <div class="time-control"> 时间控制 </div>
          <el-button color="#2283FF" type="primary"  size="small" @click="dialogtime = true">配 置</el-button>
        </div>
        <div class="total-left">
          <div class="time-control">回路总开关</div>
          <el-switch 
            v-model="props.item.allLoopStatus" 
            @change="changeSingallleLamp($event,props.item)"
            :active-value="1"
            :inactive-value="2"/>
        </div>
      </div>
      <div class="text-box">
        <div class="number-box">
          <span>状态：</span>
          <el-tag
            :type="props.item.status === 1 ? 'success' : 'danger'"
            effect="dark"
          >
            <span style="color: #fff;">{{ props.item.status === 1 ? '在线' : '离线' }}</span>
          </el-tag>
        </div>
        <div class="number-box">
          <span>编号：</span>
          <span class="fontsizestyles">{{  props.item.deviceNo }}</span>
        </div>
        <div class="position">
          <span>位置：</span>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="props.item.address"
            placement="top"
          >
            <span class="fontsizestyle">{{ props.item.address }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="btn-box">
      <div class="btn-box-list" v-for="(item,index) in props.item.loops" :key="index">
          <div class="loop-name">
            <span>{{ item.loopName }}</span>
          </div>
          <el-switch 
              v-model="item.status"
              @change="changeSingleLamp($event,item)"
              :active-value="1"
              :inactive-value="2"/>
          <div class="status-box">
            <div class="status-style" v-if="item.status === 1"></div>
            <div class="status-styles" v-if="item.status === 2"></div>
          </div>
      </div>
    </div>

    
  </div>
  <el-dialog
    v-model="dialogtime"
    title="时间控制配置"
    width="80%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    align-center>
    <DeployTime v-if="dialogtime" :item="props.item"></DeployTime>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import DeployTime from './DeployTime.vue'
import {  SingleLamp, SingallleLamp } from '@/api/lamp/console.js'

const dialogtime = ref(false)
const total = ref(false)

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['initialization','removeFun','editFun'])

const SingleLampstatus = ref(false)
const changeSingleLamp = (val,data) => {
  
  console.log(val,data,"111")
  let row = {
    luminaireNo:data.centralizedNo,
    loopNumber:data.loopNumber,
    status: val,
  }
  console.log(row,"111")

  SingleLamp(row).then(res => {
    console.log(res)
    emit('initialization')
  }).catch(err => {
    console.log(err)
    emit('initialization')
  })
}

const SingallleLampstatus = ref(false)
const changeSingallleLamp = (val,data) => {
  console.log(val,data,"222")
  let row = {
    luminaireNo:data.deviceNo,
    status: val,
  }
  SingallleLamp(row).then(res => {
    console.log(res)
    emit('initialization')
  }).catch(err => {
    emit('initialization')
    console.log(err)
  })
}

const deletelightpole = (row) => {
  emit('removeFun',row)
}

const editlightpole = (row) => {
  emit('editFun',row)
}

const handleClose = () => {
  dialogtime.value = false
}

</script>
<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.itemBox{
    height: 386px;
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: 8px;
    padding: 15px;
    background-color: #FFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.icon-title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title{
    font-family: Source Han Sans;
    font-size: 18px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.03em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
    user-select: none;
    display: flex;
    align-items: center;
  }
  .title::before{
    content: "";
    display: inline-block;
    width: 4px;
    height: 24px;
    border-radius: 81px;
    background: #2E74FF;
    margin-right: 6px;
  }
  .item-edit{
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}


.text-box{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: 20px;
  font-size: 12px;
  .position{
    display: flex;
    user-select: none;
    justify-content: space-between;
    span{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      text-align: right;
      margin-bottom: 24px;
    }
    .fontsizestyle{
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      user-select: none;
      color: #333333;
    }
  }
  .number-box{
    display: flex;
    user-select: none;
    justify-content: space-between;
    span{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      text-align: right;
      margin-bottom: 24px;
    }
     .fontsizestyles{
      width: 80px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      user-select: none;
      color: #333333;
    }
  }
}

.btn-box{
  width: 100%;
  flex: 1;
  height: 200px;
  overflow-y: auto;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin: 0;
  :deep(.el-table__inner-wrapper::before) {
    display: none;
  }
  :deep(.el-table__cell) {
    border-bottom: none;
  }
  .btn-box-list{
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .loop-name{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
    }
  }
}


.totalBtn-box{
  width: 100%;
  height: 44px;
  display: flex;
  .total-left{
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .time-control{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      margin-right: 24px;
    }
  }
  .total-right{
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .time-control{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      margin-right: 24px;
    }
  }
}

.status-style{
  width: 20px;
  height: 20px; 
  border-radius: 50%;
  background-color: #63a103;
}

.status-styles{
  width: 20px;
  height: 20px; 
  border-radius: 50%;
  border: 1px solid #333;
  background-color: #FFF;
}


</style>