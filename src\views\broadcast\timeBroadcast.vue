<template>
    <div class="broadcast-container">
        <div class="top-search-box">
            <el-row :gutter="16" class="search-row">
                <!-- 第一行：下拉选择器 -->
                <el-col :span="8">
                    <div class="search-item">
                        <span class="search-label">广播类型</span>
                        <el-select
                            v-model="searchForm.broadcastType"
                            placeholder="选择广播类型"
                            style="width: 100%"
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="麦克风广播" value="mic"></el-option>
                            <el-option label="文件广播" value="file"></el-option>
                        </el-select>
                    </div>
                </el-col>

                <el-col :span="8">
                    <div class="search-item">
                        <span class="search-label">任务模式</span>
                        <el-select
                            v-model="searchForm.taskMode"
                            placeholder="全部"
                            style="width: 100%"
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="实时任务" value="realtime"></el-option>
                            <el-option label="定时任务" value="timed"></el-option>
                        </el-select>
                    </div>
                </el-col>

                <el-col :span="8">
                    <div class="search-item">
                        <span class="search-label">任务状态</span>
                        <el-select
                            v-model="searchForm.taskStatus"
                            placeholder="已排队"
                            style="width: 100%"
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="已排队" value="queued"></el-option>
                            <el-option label="执行中" value="running"></el-option>
                            <el-option label="已完成" value="completed"></el-option>
                            <el-option label="已暂停" value="paused"></el-option>
                        </el-select>
                    </div>
                </el-col>
            </el-row>

            <el-row :gutter="16" class="search-row">
                <!-- 第二行：日期选择器和操作按钮 -->
                <el-col :span="8">
                    <div class="search-item">
                        <span class="search-label">日期选择</span>
                        <el-date-picker
                            v-model="searchForm.startDate"
                            type="date"
                            placeholder="开始日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 100%"
                        />
                    </div>
                </el-col>

                <el-col :span="8">
                    <div class="search-item">
                        <span class="search-label">截至日期</span>
                        <el-date-picker
                            v-model="searchForm.endDate"
                            type="date"
                            placeholder="选择任务截止"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 100%"
                        />
                    </div>
                </el-col>

                <el-col :span="8">
                    <div class="search-actions">
                        <el-button
                            style="border-radius: 4px; background-color: #165DFF;"
                            type="primary"
                            @click="handleSearch"
                            :loading="searching"
                            icon="Search"
                        >
                            筛选
                        </el-button>
                        <el-button
                            style="border-radius: 4px;background-color: #F2F3F5; border: none;"
                            @click="resetSearch"
                            icon="Refresh"
                        >
                            重置
                        </el-button>
                    </div>
                </el-col>
            </el-row>
        </div>
        <div class="table-container">
            <div class="title-btn">
                <div class="title">定时广播任务列表</div>
                <div class="btn">
                    <el-button class="btn-add btn-style" color="#165DFF"  @click="handleAdd">创建任务</el-button>
                    <el-button class="btn-delete btn-style" color="#FFD1DE"  @click="handledelt">批量删除</el-button>
                    <el-button  class="btn-qy btn-style" style="background-color: rgba(22, 93, 255, 0.2); color: #165DFF;" @click="handleOpenAll">批量启用</el-button>
                </div>
            </div>
            <div class="car-item-box">
                <div class="car-item" v-for="item in carItemList" :key="item">
                    <div class="car-item-title">
                        <div class="car-item-title-left">{{ item.name }}</div>
                        <div class="car-item-title-right">
                            <i class="st-icon"></i>
                            <i class="bj-icon"></i>
                            <i class="qt-icon"></i>
                        </div>
                    </div>
                    <div class="car-item-type" v-if="item.execMode === 'Single'"> 单次 </div>
                    <div class="car-item-type" v-if="item.execMode === 'EveryDay'"> 每天 </div> 
                    <div class="car-item-type" v-if="item.execMode === 'EveryWeek'"> 每周 </div>
                    <div class="car-item-content">
                        <div class="content-item">
                            <div class="content-item-left"> 广播类型 </div>
                            <div class="content-item-right type-style" v-if="item.bcType === 'mic'"> 麦克风 </div>
                            <div class="content-item-right type-style" v-if="item.bcType === 'file'"> 文件播放 </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-left"> 任务状态 </div>
                            <div class="content-item-right zt-style"> 已排队 </div>
                        </div>
                        <div class="content-item" v-if="item.execMode !== 'EveryWeek'">
                            <div class="content-item-left"> 日期选择 </div>
                            <div class="content-item-right"> {{ item.expire.beginDate }}~{{ item.expire.endDate }} </div>
                            <!-- <div class="content-item-right"> 星期一、星期二、星期三、星期四、星期五、星期六、星期日 </div> -->
                        </div>
                        <div class="content-item" v-else>
                            <div class="content-item-left"> 日期选择 </div>
                            <div class="content-item-right"> {{ item.expire.beginDate }}~{{ item.expire.endDate }} </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-left"> 启动时间 </div>
                            <div class="content-item-right"> {{ item.startTimeOfDay }} </div>
                        </div>
                        <div class="content-item" v-if="item.duration.enable">
                            <div class="content-item-left"> 时长 </div>
                            <div class="content-item-right"> {{item.loop.seconds}}(秒) </div>
                        </div>
                        <div class="content-item" v-if="item.loop.enable">
                            <div class="content-item-left"> 次数 </div>
                            <div class="content-item-right"> {{item.loop.times}}次 </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="fye-box">
                <el-pagination background layout="prev, pager, next" :total="1000" />
            </div>
        </div>

        <!-- 创建任务弹窗 -->
        <el-dialog
            v-model="createTaskDialog.visible"
            width="600px"
            :show-close="false"
            :before-close="handleCloseDialog"
        >
            <template #title>
                <div class="dialog-title-box">
                    <div class="dialog-title">
                        <el-icon class="close-icon" @click="handleCloseDialog">
                            <Close />
                        </el-icon>
                        <span class="title-text">{{ createTaskDialog.isEdit ? '编辑任务' : '创建任务' }}</span>
                    </div>
                    <el-button
                        class="complete-btn"
                        @click="handleCreateTask"
                        :loading="createTaskDialog.submitting"
                    >
                        完成
                    </el-button>
                </div>
            </template>
            <el-form
                ref="taskFormRef"
                :model="createTaskDialog.form"
                :rules="taskFormRules"
                label-width="100px"
                label-position="left"
            >
                <!-- 任务名称 -->
                <!-- 任务名称 -->
                <el-form-item label="任务名称" prop="taskName">
                    <el-input
                        v-model="createTaskDialog.form.taskName"
                        placeholder="请输入任务名称"
                        maxlength="50"
                    />
                </el-form-item>

                <!-- 广播类型 -->
                <el-form-item label="广播类型" prop="bcType">
                    <el-select
                        v-model="createTaskDialog.form.bcType"
                        placeholder="请选择广播类型"
                        style="width: 100%"
                    >
                        <el-option label="文件广播" value="file"></el-option>
                        <el-option label="麦克风广播" value="mic"></el-option>
                    </el-select>
                </el-form-item>

                <!-- 任务类型 -->
                <el-form-item label="任务类型" prop="taskType">
                    <el-select
                        v-model="createTaskDialog.form.taskType"
                        placeholder="请选择任务类型"
                        style="width: 100%"
                    >
                        <el-option label="定时任务" value="timed"></el-option>
                        <el-option label="实时任务" value="realtime"></el-option>
                    </el-select>
                </el-form-item>

                <!-- 执行模式 -->
                <el-form-item label="执行模式" prop="execMode">
                    <el-select
                        v-model="createTaskDialog.form.execMode"
                        placeholder="请选择执行模式"
                        style="width: 100%"
                        @change="handleExecModeChange"
                    >
                        <el-option label="单次执行" value="Single"></el-option>
                        <el-option label="每天执行" value="EveryDay"></el-option>
                        <el-option label="每周执行" value="EveryWeek"></el-option>
                    </el-select>
                </el-form-item>

                <!-- 周几执行（仅当执行模式为每周时显示） -->
                <el-form-item
                    v-if="createTaskDialog.form.execMode === 'EveryWeek'"
                    label="执行日期"
                    prop="weekDay"
                >
                    <el-checkbox-group v-model="createTaskDialog.form.weekDay">
                        <el-checkbox :label="0">周日</el-checkbox>
                        <el-checkbox :label="1">周一</el-checkbox>
                        <el-checkbox :label="2">周二</el-checkbox>
                        <el-checkbox :label="3">周三</el-checkbox>
                        <el-checkbox :label="4">周四</el-checkbox>
                        <el-checkbox :label="5">周五</el-checkbox>
                        <el-checkbox :label="6">周六</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <!-- 选择设备 -->
                <el-form-item label="选择设备" prop="deviceIds">
                    <el-select
                        v-model="createTaskDialog.form.deviceIds"
                        placeholder="请选择设备"
                        multiple
                        style="width: 100%"
                    >
                        <el-option
                            v-for="device in deviceOptions"
                            :key="device.deviceId"
                            :label="device.deviceName"
                            :value="device.deviceId"
                        />
                    </el-select>
                </el-form-item>

                <!-- 任务启用状态 -->
                <el-form-item label="任务状态" prop="enable">
                    <el-switch
                        v-model="createTaskDialog.form.enable"
                        active-text="启用"
                        inactive-text="禁用"
                    />
                </el-form-item>

                <!-- 有效期设置 -->
                <el-form-item label="有效期设置">
                    <el-switch
                        v-model="createTaskDialog.form.expireObj.enable"
                        active-text="启用有效期"
                        inactive-text="不限制"
                        style="margin-bottom: 16px;"
                    />
                    <el-row v-if="createTaskDialog.form.expireObj.enable" :gutter="16">
                        <el-col :span="11">
                            <el-date-picker
                                v-model="createTaskDialog.form.expireObj.beginDate"
                                type="date"
                                placeholder="开始日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                style="width: 100%"
                            />
                        </el-col>
                        <el-col :span="2" class="text-center">
                            <span>—</span>
                        </el-col>
                        <el-col :span="11">
                            <el-date-picker
                                v-model="createTaskDialog.form.expireObj.endDate"
                                type="date"
                                placeholder="结束日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                style="width: 100%"
                            />
                        </el-col>
                    </el-row>
                </el-form-item>

                <!-- 任务开始时间 -->
                <el-form-item label="开始时间" prop="startTimeOfDay">
                    <el-time-picker
                        v-model="createTaskDialog.form.startTimeOfDay"
                        placeholder="请选择任务开始时间"
                        format="HH:mm:ss"
                        value-format="HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>

                <!-- 播放模式 -->
                <el-form-item label="播放模式" prop="timeMode">
                    <el-radio-group v-model="createTaskDialog.form.timeMode" @change="handleTimeModeChange">
                        <el-radio label="times">按次数播放</el-radio>
                        <el-radio label="seconds">时间</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 播放参数 -->
                <el-form-item
                    :label="createTaskDialog.form.timeMode === 'times' ? '播放次数' : '结束时间'"
                    prop="modeContent"
                >
                    <el-input-number
                        v-if="createTaskDialog.form.timeMode === 'times'"
                        v-model="createTaskDialog.form.modeContent"
                        :min="1"
                        :max="999"
                        placeholder="请输入播放次数"
                        style="width: 100%"
                    />
                    <el-time-picker
                        v-else
                        v-model="createTaskDialog.form.endTime"
                        placeholder="请选择结束时间"
                        format="HH:mm:ss"
                        value-format="HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>

                <!-- 音量 -->
                <el-form-item label="音量">
                    <div class="volume-control-wrapper">
                        <el-slider
                            v-model="createTaskDialog.form.volume"
                            :min="0"
                            :max="100"
                            :step="1"
                            :show-input-controls="false"
                            style="flex: 1; margin-right: 16px;"
                        />
                        <span class="volume-value">{{ createTaskDialog.form.volume }}</span>
                    </div>
                </el-form-item>

                <!-- 已选文件 -->
                <div class="file-upload-area">
                    <div class="upload-header">
                        <span class="file-section-title">已选文件</span>
                        <el-button class="from-btn" @click="handleUploadFile">上传/选择文件</el-button>
                    </div>

                    <!-- 文件列表 -->
                    <div class="selected-files">
                        <el-table
                            :data="createTaskDialog.form.fileIds"
                            style="width: 100%"
                            :show-header="true"
                            size="small"
                        >
                            <el-table-column prop="name" label="文件名称" width="100">
                                <template #default="scope">
                                    <span class="file-name">{{ scope.row.name }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="duration" label="文件时间" align="center">
                                <template #default="scope">
                                    <span class="file-duration">{{ scope.row.duration }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="80" align="center">
                                <template #default="scope">
                                    <el-button
                                        type="danger"
                                        text
                                        size="small"
                                        @click="removeFile(scope.$index)"
                                    >
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </el-form>
        </el-dialog>

        <!-- 文件上传组件 -->
        <FileUploadDialog
            v-if="fileUploadDialog.visible"
            v-model="fileUploadDialog.visible"
            :available-files="availableFiles"
            @confirm="handleFileUploadConfirm"
            @close="handleFileUploadClose"
        />
    </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import chromeSDKManager from '@/api/broadcast/index.js'
import FileUploadDialog from '@/components/FileUploadDialog.vue'

const carItemList= ref([])
// 设备选项
const deviceOptions = ref([ ])

const initSDK = async ()=>{
  try {
    // 假设DHAlarmWeb已经加载
    if (typeof DHAlarmWeb !== 'undefined') {
      const dhWeb = new DHAlarmWeb();
      chromeSDKManager.init(dhWeb);
      console.log('Chrome SDK初始化成功');
      login()
    } else {
      throw new Error('DHAlarmWeb未加载');
    }
  } catch (error) {
    console.error('SDK初始化失败:', error);
    throw error;
  }
}

// 登录
const login = async () => {
  try {
    const result = await chromeSDKManager.quickLogin('mainalc', '123456', '************');
    // const result = await chromeSDKManager.quickLogin('mainalc', '123456789', '***********');
    if (result) {
      console.log('登录成功',result);
      getDataList()
      ondeviceList()
    } else {
      console.log('登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
  }
}
const getDataList = async () => {
  try {
    const result = await chromeSDKManager.quickGetBCTaskList('timed');
    if (result) {
      console.log('获取数据成功',result);
      carItemList.value = result.params.tasks;
    } else {
      console.log('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

const ondeviceList = (result) => {
  chromeSDKManager.on('onDeviceList', (result) => {
    console.log('设备列表回调:', result);

    // 检查返回的数据结构
    if (!result || !result.params) {
      console.warn('设备列表回调数据格式异常:', result);
      return;
    }

    // 处理单个设备或设备列表
    let newDevices = [];
    if (result.params.list && Array.isArray(result.params.list)) {
      // 如果是设备列表数组
      newDevices = result.params.list;
    } else if (result.params.device || result.params.deviceId) {
      // 如果是单个设备信息
      newDevices = [result.params];
    } else {
      console.warn('无法识别的设备数据格式:', result.params);
      return;
    }

    // 累积存储设备信息并去重
    newDevices.forEach(device => {
      if (!device) return;

      // 确定设备的唯一标识符（优先级：deviceId > id > mac > ip）
      const deviceKey = device.deviceId || device.id || device.mac || device.ip;
      if (!deviceKey) {
        console.warn('设备缺少唯一标识符:', device);
        return;
      }

      // 检查设备是否已存在（基于唯一标识符去重）
      const existingIndex = deviceOptions.value.findIndex(existingDevice => {
        const existingKey = existingDevice.deviceId || existingDevice.id || existingDevice.mac || existingDevice.ip;
        return existingKey === deviceKey;
      });

      if (existingIndex !== -1) {
        // 设备已存在，更新设备信息（保持最新状态）
        deviceOptions.value[existingIndex] = { ...deviceOptions.value[existingIndex], ...device };
        console.log('更新已存在设备:', deviceKey, device);
      } else {
        // 新设备，添加到列表
        deviceOptions.value.push(device);
        console.log('添加新设备:', deviceKey, device);
      }
    });

    console.log('当前设备总数:', deviceOptions.value.length);
  });
}

// 清空设备列表（在重新搜索设备前调用）
const clearDeviceList = () => {
  deviceOptions.value = [];
  console.log('设备列表已清空');
}

// 手动添加设备到列表（用于测试或特殊情况）
const addDeviceToList = (device) => {
  if (!device) return false;

  const deviceKey = device.deviceId || device.id || device.mac || device.ip;
  if (!deviceKey) {
    console.warn('设备缺少唯一标识符:', device);
    return false;
  }

  const existingIndex = deviceOptions.value.findIndex(existingDevice => {
    const existingKey = existingDevice.deviceId || existingDevice.id || existingDevice.mac || existingDevice.ip;
    return existingKey === deviceKey;
  });

  if (existingIndex === -1) {
    deviceOptions.value.push(device);
    console.log('手动添加设备:', deviceKey, device);
    return true;
  } else {
    console.log('设备已存在，跳过添加:', deviceKey);
    return false;
  }
}

// 刷新设备列表（主动触发设备搜索）
const refreshDeviceList = () => {
  console.log('开始刷新设备列表...');

  // 清空当前设备列表
  clearDeviceList();

  // 这里可以调用SDK的刷新设备列表方法
  // 注意：具体的API方法需要根据SDK文档确定
  try {
    // 示例：如果SDK有刷新设备列表的方法
    // chromeSDKManager.refreshDeviceList();

    // 或者重新登录来触发设备列表更新
    // login();

    console.log('设备列表刷新请求已发送');
  } catch (error) {
    console.error('刷新设备列表失败:', error);
  }
}





// 搜索表单数据
const searchForm = reactive({
  broadcastType: '',      // 广播类型
  taskMode: '',           // 任务模式
  taskStatus: '',         // 任务状态
  startDate: '',          // 开始日期
  endDate: ''             // 结束日期
})

// 搜索状态
const searching = ref(false)

// 创建任务弹窗
const createTaskDialog = reactive({
  visible: false,
  submitting: false,
  isEdit: false,                     // 是否为编辑模式
  editTaskId: null,                  // 编辑的任务ID
  form: {
    taskName: '',                    // 任务名称
    volume: 50,                      // 广播音量（0~100）
    deviceIds: [],                   // 广播的设备列表
    fileIds: [],                     // 广播播放的文件列表（数组元素的顺序就是播放的顺序）
    enable: true,                    // 广播启用/禁用
    execMode: 'Single',              // 执行模式（"Single", "EveryDay","EveryWeek"）
    weekDay: [],                     // 执行模式为"EveryWeek"时有效，1～6代表周一～周六，0代表周日
    startTimeOfDay: '',              // 任务开始时间（时间格式，如10:08:08）
    endTime: '',                     // 结束时间（当timeMode为seconds时使用）
    expireObj: {                     // 任务有效期
      enable: false,                 // 有效期启用/禁用
      beginDate: '',                 // 开始日期（日期格式，如：2021-06-06）
      endDate: ''                    // 截止日期（日期格式，如：2021-06-06）
    },
    timeMode: 'times',               // 播放模式（"seconds": 时长，单位为秒；"times": 次数）
    modeContent: 1,                  // timeMode="seconds" 填时长，timeMode="times", 填次数
    taskType: 'timed',               // 任务类型（realtime：实时任务，timed：定时任务）
    bcType: 'file'                   // 广播类型（mic：麦克风，file：文件）
  }
})

// 文件上传弹窗数据
const fileUploadDialog = reactive({
  visible: false
})

// 可选择的文件列表
const availableFiles = ref([
  { id: 1, name: '系统提示音.mp3', duration: '00:00:03' },
  { id: 2, name: '警报声.wav', duration: '00:00:05' },
  { id: 3, name: '通知铃声.mp3', duration: '00:00:02' },
  { id: 4, name: '背景音乐.mp3', duration: '00:03:45' }
])



// 表单引用
const taskFormRef = ref(null)

// 表单验证规则
const taskFormRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 50, message: '任务名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  bcType: [
    { required: true, message: '请选择广播类型', trigger: 'change' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  execMode: [
    { required: true, message: '请选择执行模式', trigger: 'change' }
  ],
  deviceIds: [
    { required: true, message: '请选择设备', trigger: 'change' }
  ],

  startTimeOfDay: [
    { required: true, message: '请选择任务开始时间', trigger: 'change' }
  ],
  timeMode: [
    { required: true, message: '请选择播放模式', trigger: 'change' }
  ],
  modeContent: [
    {
      validator: (_rule, value, callback) => {
        if (createTaskDialog.form.timeMode === 'times') {
          if (!value || value < 1) {
            callback(new Error('播放次数必须大于0'));
          } else {
            callback();
          }
        } else {
          // 时间模式下验证结束时间
          if (!createTaskDialog.form.endTime) {
            callback(new Error('请选择结束时间'));
          } else {
            callback();
          }
        }
      },
      trigger: 'blur'
    }
  ],
  weekDay: [
    {
      validator: (_rule, value, callback) => {
        if (createTaskDialog.form.execMode === 'EveryWeek' && (!value || value.length === 0)) {
          callback(new Error('执行模式为每周时，请选择执行日期'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
}

// 处理搜索
const handleSearch = async () => {
  searching.value = true

  try {
    console.log('搜索参数:', searchForm)

    // 这里添加实际的搜索逻辑
    // 例如调用API获取数据

    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    console.log('搜索完成')
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    searching.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.assign(searchForm, {
    broadcastType: '',
    taskMode: '',
    taskStatus: '',
    startDate: '',
    endDate: ''
  })
  console.log('搜索表单已重置')
}

// 处理创建任务按钮点击
const handleAdd = () => {
  createTaskDialog.visible = true
  // 重置表单
  resetTaskForm()
}

// 重置任务表单
const resetTaskForm = () => {
  Object.assign(createTaskDialog.form, {
    fileType: '',
    taskName: '',
    executeMode: '',
    selectedDevices: [],
    startTime: '',
    endTime: '',
    launchTime: '',
    executeType: 'count',
    executeCount: '',
    executeTime: '',
    volume: 17,
    selectedFiles: []
  })

  // 清除表单验证
  if (taskFormRef.value) {
    taskFormRef.value.clearValidate()
  }
}

// 处理关闭弹窗
const handleCloseDialog = () => {
  createTaskDialog.visible = false
  resetTaskForm()
}

// 处理文件上传 - 打开文件选择弹窗
const handleUploadFile = () => {
  fileUploadDialog.visible = true
}

// 处理文件上传组件确认事件
const handleFileUploadConfirm = (selectedFiles) => {
  // 将选中的文件添加到主表单
  selectedFiles.forEach(file => {
    const exists = createTaskDialog.form.fileIds.some(f => f.id === file.id)
    if (!exists) {
      createTaskDialog.form.fileIds.push(file)
    }
  })

  console.log('已添加文件:', selectedFiles)
  fileUploadDialog.visible = false
}

// 处理文件上传组件关闭事件
const handleFileUploadClose = () => {
  fileUploadDialog.visible = false
}



// 移除文件
const removeFile = (index) => {
  createTaskDialog.form.fileIds.splice(index, 1)
  console.log('文件已移除，索引:', index)
}

// 处理执行模式变化
const handleExecModeChange = (value) => {
  // 如果不是每周执行，清空周几选择
  if (value !== 'EveryWeek') {
    createTaskDialog.form.weekDay = []
  }
}

// 处理播放模式变化
const handleTimeModeChange = (value) => {
  // 清除相关字段的验证错误
  if (taskFormRef.value) {
    taskFormRef.value.clearValidate(['modeContent'])
  }

  // 重置相关字段
  if (value === 'times') {
    createTaskDialog.form.modeContent = 1
    createTaskDialog.form.endTime = ''
  } else {
    createTaskDialog.form.modeContent = null
  }
}

// 处理创建/编辑任务
const handleCreateTask = async () => {
  if (!taskFormRef.value) return

  try {
    // 表单验证
    await taskFormRef.value.validate()

    // 额外检查文件是否已选择
    if (!createTaskDialog.form.fileIds || createTaskDialog.form.fileIds.length === 0) {
      ElMessage.error('请选择至少一个文件')
      return
    }

    createTaskDialog.submitting = true

    // 准备接口参数
    let modeContent = createTaskDialog.form.modeContent;

    // 如果是时间模式，需要计算时长（秒）
    if (createTaskDialog.form.timeMode === 'seconds') {
      const startTime = createTaskDialog.form.startTimeOfDay;
      const endTime = createTaskDialog.form.endTime;

      if (startTime && endTime) {
        // 计算时间差（秒）
        const start = new Date(`2000-01-01 ${startTime}`);
        const end = new Date(`2000-01-01 ${endTime}`);
        let diffInSeconds = (end - start) / 1000;

        // 如果结束时间小于开始时间，说明跨天了
        if (diffInSeconds < 0) {
          diffInSeconds += 24 * 60 * 60; // 加上一天的秒数
        }

        modeContent = diffInSeconds;
      }
    }

    const taskConfig = {
      taskName: createTaskDialog.form.taskName,
      volume: createTaskDialog.form.volume,
      deviceIds: createTaskDialog.form.deviceIds,
      fileIds: createTaskDialog.form.fileIds.map(file => file.id), // 只传递文件ID数组
      enable: createTaskDialog.form.enable,
      execMode: createTaskDialog.form.execMode,
      weekDay: createTaskDialog.form.weekDay,
      startTimeOfDay: createTaskDialog.form.startTimeOfDay,
      expireObj: createTaskDialog.form.expireObj,
      timeMode: createTaskDialog.form.timeMode,
      modeContent: modeContent,
      taskType: createTaskDialog.form.taskType,
      bcType: createTaskDialog.form.bcType
    }

    console.log(createTaskDialog.isEdit ? '编辑任务参数:' : '创建任务参数:', taskConfig)
    let result
    if (createTaskDialog.isEdit) {
      // 编辑任务
      result = await chromeSDKManager.getBroadcastManager().editBCTask(createTaskDialog.editTaskId, taskConfig)
      console.log('任务编辑成功:', result)
      ElMessage.success('任务编辑成功')
    } else {
      // 创建任务
      result = await chromeSDKManager.getBroadcastManager().uploadBCTask(taskConfig)
      console.log('任务创建成功:', result)
      ElMessage.success('任务创建成功')
    }

    // 关闭弹窗
    handleCloseDialog()

    // 刷新任务列表
    await getDataList()

  } catch (error) {
    console.error(createTaskDialog.isEdit ? '编辑任务失败:' : '创建任务失败:', error)
    ElMessage.error((createTaskDialog.isEdit ? '编辑任务失败: ' : '创建任务失败: ') + error.message)
  } finally {
    createTaskDialog.submitting = false
  }
}

// 打开创建任务弹窗
const handleOpenDialog = () => {
  resetForm()
  createTaskDialog.isEdit = false
  createTaskDialog.editTaskId = null
  createTaskDialog.visible = true
}

// 打开编辑任务弹窗
const handleEditTask = (task) => {
  resetForm()
  createTaskDialog.isEdit = true
  createTaskDialog.editTaskId = task.taskId

  // 填充表单数据
  createTaskDialog.form.taskName = task.taskName || ''
  createTaskDialog.form.volume = task.volume || 50
  createTaskDialog.form.deviceIds = task.deviceIds || []
  createTaskDialog.form.fileIds = task.fileIds || []
  createTaskDialog.form.enable = task.enable !== undefined ? task.enable : true
  createTaskDialog.form.execMode = task.execMode || 'Single'
  createTaskDialog.form.weekDay = task.weekDay || []
  createTaskDialog.form.startTimeOfDay = task.startTimeOfDay || ''
  createTaskDialog.form.endTime = task.endTime || ''
  createTaskDialog.form.expireObj = task.expireObj || {
    enable: false,
    beginDate: '',
    endDate: ''
  }
  createTaskDialog.form.timeMode = task.timeMode || 'times'
  createTaskDialog.form.modeContent = task.modeContent || 1
  createTaskDialog.form.taskType = task.taskType || 'timed'
  createTaskDialog.form.bcType = task.bcType || 'file'

  createTaskDialog.visible = true
}

// 重置表单
const resetForm = () => {
  createTaskDialog.form.taskName = ''
  createTaskDialog.form.volume = 50
  createTaskDialog.form.deviceIds = []
  createTaskDialog.form.fileIds = [] // 重置时清空文件列表
  createTaskDialog.form.enable = true
  createTaskDialog.form.execMode = 'Single'
  createTaskDialog.form.weekDay = []
  createTaskDialog.form.startTimeOfDay = ''
  createTaskDialog.form.endTime = ''
  createTaskDialog.form.expireObj = {
    enable: false,
    beginDate: '',
    endDate: ''
  }
  createTaskDialog.form.timeMode = 'times'
  createTaskDialog.form.modeContent = 1
  createTaskDialog.form.taskType = 'timed'
  createTaskDialog.form.bcType = 'file'

  // 清除表单验证
  if (taskFormRef.value) {
    taskFormRef.value.clearValidate()
  }
}

// 其他按钮处理函数
const handledelt = () => {
  console.log('批量删除')
}

const handleOpenAll = () => {
  console.log('批量启用')
}

onMounted(() => {
  initSDK()
})

</script>
<style lang="scss" scoped>

.broadcast-container {
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  padding: 16px;
}

.top-search-box {
  width: 100%;
  max-height: 162px;
  border-radius: 8px;
  background: #ffffff;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-row {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 4px;
  min-width: 62px;
}

.search-actions {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  height: 100%;
}

// Element Plus 组件样式调整
:deep(.el-select) {
  .el-select__wrapper {
    border-radius: 6px;
    box-shadow: none;
    background-color: #F7F8FA;
    border: none;
    &:hover {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}

:deep(.el-input) {
  width: 100%;

  .el-input__wrapper {
    border-radius: 4px;
    box-shadow: none;
    background-color: #F7F8FA;
    border: none;
    &:hover {
      box-shadow: 0 0 0 1px #409eff inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;

  &.el-button--primary {
    background: #409eff;
    border-color: #409eff;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }
}

.table-container{
    width: 100%;
    height: calc(100vh - 236px);
    margin-top: 16px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.title-btn{
    padding: 16px 16px 0 16px;
    display: flex;
    justify-content: space-between;
}

.title{
    font-family: Source Han Sans;
    font-size: 18px;
    font-weight: 500;
    line-height: 22px;
    letter-spacing: 0.04em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
}

.btn-style{
    border-radius: 4px;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.05em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFFFFF;
}

.btn-delete{
    color: #FA5151;
}
.btn-qy{
    color: #165DFF;
}

.car-item-box{
    width: 100%;
    padding: 14px;
    display: grid;
    // 一排四个，一共三排
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 356px);
    grid-gap: 16px;
}

.car-item{
    background-color: #fff;
    padding: 13px 15px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.car-item-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .car-item-title-left{
        font-family: Source Han Sans;
        font-size: 18px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.03em;
        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #333333;
        display: flex;
        align-items: center;
        gap: 6px;
        &::before{
            content: "";
            width: 4px;
            height: 24px;
            border-radius: 81px;
            background: #2E74FF;
        }
    }
    .car-item-title-right{
        display: flex;
        gap: 16px;
        i{
            width: 20px;
            height: 20px;
            display: block;
        }
        .st-icon{
            background: url("/img/icon/st-icon.png") no-repeat center;
            background-size: cover;
        }
        .bj-icon{
            background: url("/img/icon/bj-icon.png") no-repeat center;
            background-size: cover;
        }
        .qt-icon{
            background: url("/img/icon/qt-icon.png") no-repeat center;
            background-size: cover;
        }
    }
}

.car-item-type{
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0.03em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFBB44;
    display: flex;
    align-items: center;
    gap: 6px;
    padding-left: 10px;
    &::before{
        content: "";
        width: 8px;
        height: 8px;
        background: #FFBB44;
        border-radius: 50%;
    }
}

.car-item-content{
    display: flex;
    flex-direction: column;
    gap: 26px;
}

.content-item{
    display: flex;
    justify-content: space-between;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0.03em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    .content-item-left{
        color: #666666;
    }
    .content-item-right{
        color: #333333;
    }
    .type-style{
        width: 87px;
        height: 29px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #2283FF;
        border-radius: 4px;
        color: #FFFFFF !important;
    }

    .zt-style{
        color: #07C160;
    }
}




.fye-box{
    width: 100%;
    display: flex;
    padding: 16px 16px 16px 0;
    justify-content: flex-end;
}

// 创建任务弹窗样式
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 16px 20px;
    background: #ffffff;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}

// 自定义弹窗标题样式
.dialog-title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-icon {
  font-size: 24px;
  color: #666666;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #606266;
  }
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.complete-btn {
  background: #2283FF;
  border-color: #2283FF;
  border-radius: 4px;
  font-size: 14px;
  padding: 8px 16px;
  color: #FFFFFF;
  &:hover {
    background: #5a95f5;
    border-color: #5a95f5;
  }

  &:focus {
    background: #4285f4;
    border-color: #4285f4;
  }
}

.execute-type-container {
  display: flex;
  gap: 16px;
  width: 100%;
}

.execute-option {
  display: flex;
  align-items: center;
  gap: 12px;

  .option-desc {
    color: #909399;
    font-size: 14px;
    min-width: 60px;
  }

  .option-input {
    max-width: 200px;

    &:disabled {
      .el-input__wrapper {
        background-color: #f5f7fa;
        color: #c0c4cc;
        cursor: not-allowed;
      }
    }
  }
}

// 执行类型单选按钮样式
:deep(.execute-option .el-radio) {
  margin-right: 0;

  .el-radio__input {
    .el-radio__inner {
      width: 16px;
      height: 16px;
      border: 2px solid #dcdfe6;

      &::after {
        width: 6px;
        height: 6px;
        background: #409eff;
      }
    }

    &.is-checked {
      .el-radio__inner {
        border-color: #409eff;
        background: #ffffff;
      }
    }
  }

  .el-radio__label {
    color: #606266;
    font-size: 14px;
    padding-left: 8px;
  }
}

.volume-control-wrapper {
  display: flex;
  align-items: center;
  width: 100%;

  .volume-value {
    min-width: 30px;
    text-align: center;
    color: #409eff;
    font-weight: 500;
  }
}

.file-upload-area {
  .selected-files {
    margin-top: 16px;

    // 表格样式定制
    :deep(.el-table) {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      overflow: hidden;

      .el-table__header {
        th {
          background: #f5f7fa;
          color: #606266;
          font-weight: 500;
          border-bottom: 1px solid #ebeef5;
          padding: 12px 8px;
        }
      }

      .el-table__body {
        tr {
          &:hover {
            background: #f5f7fa;
          }

          td {
            padding: 12px 8px;
            border-bottom: 1px solid #ebeef5;

            &:last-child {
              border-right: none;
            }
          }

          &:last-child td {
            border-bottom: none;
          }
        }
      }

      .file-name {
        color: #303133;
        font-size: 14px;
      }

      .file-duration {
        color: #909399;
        font-family: monospace;
        font-size: 14px;
      }

      .el-button {
        color: #f56c6c;

        &:hover {
          color: #f78989;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;

  .el-button {
    border-radius: 6px;
    padding: 8px 24px;
  }
}

.text-center {
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    color: #909399;
  }
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
        font-family: Source Han Sans;
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        text-transform: uppercase;
        letter-spacing: 0em;
        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #333333;
        font-weight: 500;
    }

    .el-form-item__content {
      .el-input__wrapper {
        border-radius: 6px;
      }

      .el-select .el-input__wrapper {
        border-radius: 6px;
      }

      .el-date-editor {
        border-radius: 6px;
      }
    }
  }
}

.from-btn{
    border-radius: 4px;
    background-color: #2283FF;
    color: #fff;
}
.upload-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.file-section-title{
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
}


</style>
