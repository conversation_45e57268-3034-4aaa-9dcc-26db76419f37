<template>
  <div class="container">
    <div class="top-box">
      <div class="search-box">
        <!-- <el-input v-model="search.name" placeholder="请输入区域" style="width: 300px; margin-right: 24px;"></el-input> -->
        <el-input v-model="search.luminaireNo" placeholder="请输入编号" style="width: 300px; margin-right: 24px;"></el-input>
        <el-button type="primary" icon="Search" @click="searchData">搜索</el-button>
      </div>
    </div>
    <div class="itemBig-box">
      <ItemBox v-for="(item,index) in ListData" :key="index" :item="item" @initialization="initialization"></ItemBox>
    </div>
    <div class="paging-box">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="1000"
      ></el-pagination>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
// import { ArrowRightBold } from "@element-plus/icons";
import ItemBox from '@/components/lamp/console/itemBox.vue'
import Rightpagebox from "@/components/publiczj/rightpagebox.vue"
import { useRouter } from 'vue-router';
import {  getList } from '@/api/lamp/console.js'

const router = useRouter();

const menuData = ref([
  {
    name: '单灯',
    // icon: ArrowRightBold,
    path: '/lamp/console',
  },
  {
    name: '集中控制器',
    // icon: ArrowRightBold,
    path: '/lamp/switchbox',
  }
])

const showMenu = ref(false)
const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const search = ref({
  luminaireNo: ''
})

const page = ref(
  {
    pageSize: 10,
    currentPage: 1,
    total: 0,
  },
)

const searchData = () => {
  getListData()
}

const toluyou = (item) => {
  console.log('跳转',item)
  showMenu.value = !showMenu.value
  router.push(item.path);
}

const ListData = ref([])
const getListData = () => {
  getList(page.value.currentPage, page.value.pageSize, search.value).then(res=>{
    ListData.value = res.data.data.records
    page.value.total = res.data.data.total
    page.value.currentPage = res.data.data.current
    page.value.pageSize = res.data.data.size
  })
}

const initialization = () => {
  page.value =  {
    pageSize: 10,
    currentPage: 1,
    total: 0,
  },
  search.value = {}
  getListData()
}

onMounted(()=>{
  getListData()
})

</script>
<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.container{
  height: 100%;
  padding: 16px 16px 0;
}
.itemBig-box{
  width: 100%;
  height: calc(100% - 112px);
  display: grid;
  grid-template-columns: repeat(5, 288px);
  grid-gap: 10px 80px;
  margin-top: 24px;
  .item-box{
    height: 366px;
    background-color: #fff;
    display: flex;
  }
}


.add-box{
  margin-right: 24px;
}
.top-box{
  display: flex;
}
.paging-box{
  display: flex;
  justify-content: flex-end;
  margin: 24px 0;
}

.menu-btn{
  position: fixed;
  right: 0;
  bottom: calc(50% - 130px);
  width: 52px;
  height: 262px;
  font-size: 20px;
  line-height: 52px;
  writing-mode: vertical-lr;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  cursor: pointer;
}


.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}
</style>
<style lang="scss" scoped>
</style>