import request from '@/axios';

export const getList = (current, size, params) =>{
    return request({
      url: '/hztech-light/lampsLanterns/page',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
}

// 删除单灯
export const remove = ( params ) =>{
    return request({
      url: '/hztech-light/lampsLanterns/remove',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 修改回路状态
export const SingleLamp = ( params ) =>{
    return request({
      url: '/hztech-light/loop/controlSingleLamp',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 修改all回路状态
export const SingallleLamp = ( params ) =>{
    return request({
      url: '/hztech-light/loop/controlAlleLamp',
      method: 'get',
      params: {
        ...params
      }
    })
}


// 获取时间控制配置页数据
export const getpeizTimeData = ( params ) =>{
  return request({
    url: '/hztech-light/lightingStrategy/page',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 新增策略
export const add = ( row ) =>{
  return request({
    url: '/hztech-light/lightingStrategy/save',
    method: 'post',
    data:row
  })
}

// 修改策略状态
export const chargeStatus = ( params ) =>{
  return request({
    url: '/hztech-light/lightingStrategy/chargeStatus',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const StrategyRemove = ( params ) =>{
  return request({
    url: '/hztech-light/lightingStrategy/remove',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 修改单灯亮度
export const singleLampSwitch = ( params ) =>{
  return request({
    url: '/hztech-light/lampsLanterns/singleLampSwitch',
    method: 'get',
    params: {
      ...params
    }
  })
}