<template>
  <div class="itemBox">
    <div class="top-box">
      <div class="icon-title">
        <div class="title">{{ props.item.deviceName || '集中控制器' }}</div>
        <div class="item-edit">
          <el-icon style="font-size: 22px; color: #333333; margin-right: 20px;"  @click="deletelightpole(props.item)"><Delete /></el-icon>
        </div>
      </div>
      <!-- <div class="totalBtn-box">
        <div class="total-right">
          <div class="time-control"> 时间控制 </div>
          <el-button color="#2283FF" type="primary"  size="small" @click="dialogtime = true">配 置</el-button>
        </div>
        <div class="total-left">
          <div class="time-control">回路总开关</div>
          <el-switch 
            v-model="props.item.allLoopStatus" 
            @change="changeSingallleLamp($event,props.item)"
            :active-value="1"
            :inactive-value="2"/>
        </div>
      </div> -->
      <div class="totalBtn-box">
        <div class="total-right">
          <div class="time-control"> 光感控制 </div>
          <el-button color="#2283FF" type="primary"  size="small" @click="dialogguangan = true">配 置</el-button>
        </div>
      </div>
      <div class="text-box">
        <div class="number-box">
          <span>光照度：</span>
          <span class="fontsizestyles">{{  props.item.business }}</span>
        </div>
        <div class="number-box">
          <span>编号：</span>
          <span class="fontsizestyles">{{  props.item.deviceNo }}</span>
        </div>
        <div class="number-box">
          <span>物联网ID：</span>
          <span class="fontsizestyles">{{  props.item.deviceId }}</span>
        </div>
        <div class="position" style="margin-bottom: 0px;">
          <span style="margin-bottom: 0px;">位置：</span>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="props.item.address"
            placement="top"
          >
            <span style="margin-bottom: 0px;" class="fontsizestyle">{{ props.item.address }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
    <!-- <div class="btn-box">
      <div class="btn-box-list" style="margin-bottom: 0px;">
          <div class="loop-name">
            开关状态
          </div>
          <el-switch 
              v-model="props.item.status"
              @change="changeSingleLamp($event,item)"
              :active-value="1"
              :inactive-value="2"/>
      </div>
    </div> -->

    
  </div>
  <el-dialog
    v-model="dialogtime"
    title="时间配置"
    width="80%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    align-center>
    <DeployTime v-if="dialogtime" :item="props.item"></DeployTime>
  </el-dialog>
  <el-dialog
    v-model="dialogguangan"
    title="光感配置"
    width="50%"
    :before-close="handleGuanganClose"
    :close-on-click-modal="false"
    align-center>
    <Guanggan v-if="dialogguangan" :item="props.item" @submit="submit"></Guanggan>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import DeployTime from './DeployTime.vue'
import Guanggan from './Guanggan.vue'
import {  SingleLamp, SingallleLamp } from '@/api/lamp/console.js'
import { chargeDeviceStatus } from '@/api/lamp/lightEnvironment.js'



const dialogtime = ref(false)
const dialogguangan = ref(false)
const total = ref(false)

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['initialization', 'removeFun', 'editFun'])

const SingleLampstatus = ref(false)
const changeSingleLamp = (val,data) => {
  
  console.log(val,data,"111")
  let row = {
    id:data.id,
    status: val,
  }
  console.log(row,"111")

  chargeDeviceStatus(row).then(res => {
    console.log(res)
    emit('initialization')
  }).catch(err => {
    console.log(err)
    emit('initialization')
  })
}

const SingallleLampstatus = ref(false)
const changeSingallleLamp = (val,data) => {
  console.log(val,data,"222")
  let row = {
    luminaireNo:data.deviceNo,
    status: val,
  }
  SingallleLamp(row).then(res => {
    console.log(res)
    emit('initialization')
  }).catch(err => {
    emit('initialization')
    console.log(err)
  })
}

const deletelightpole = (row) => {
  emit('removeFun',row)
}

const handleClose = () => {
  dialogtime.value = false
}

const handleGuanganClose = () => {
  dialogguangan.value = false
}

const submit = () => {
  dialogguangan.value = false
  emit('initialization')
}

</script>
<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.itemBox{
    height: 300px;
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: 8px;
    padding: 15px;
    background-color: #FFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.icon-title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 12px;
  .title{
    font-family: Source Han Sans;
    font-size: 18px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.03em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #333333;
    user-select: none;
    display: flex;
    align-items: center;
  }
  .title::before{
    content: "";
    display: inline-block;
    width: 4px;
    height: 24px;
    border-radius: 81px;
    background: #2E74FF;
    margin-right: 6px;
  }
  .item-edit{
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}


.text-box{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: 20px;
  font-size: 12px;
  .position{
    display: flex;
    user-select: none;
    justify-content: space-between;
    span{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      text-align: right;
      margin-bottom: 24px;
    }
    .fontsizestyle{
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      user-select: none;
      color: #333333;
    }
  }
  .number-box{
    display: flex;
    user-select: none;
    justify-content: space-between;
    span{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      text-align: right;
      margin-bottom: 24px;
    }
     .fontsizestyles{
      width: 80px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      user-select: none;
      color: #333333;
    }
  }
}

.btn-box{
  width: 100%;
  flex: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin: 0;
  .btn-box-list{
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .loop-name{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
    }
  }
}


.totalBtn-box{
  width: 100%;
  height: 44px;
  display: flex;
  .total-left{
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .time-control{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      margin-right: 24px;
    }
  }
  .total-right{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .time-control{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.03em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #666666;
      margin-right: 24px;
    }
  }
}

.status-style{
  width: 20px;
  height: 20px; 
  border-radius: 50%;
  background-color: #63a103;
}

.status-styles{
  width: 20px;
  height: 20px; 
  border-radius: 50%;
  border: 1px solid #333;
  background-color: #FFF;
}


</style>