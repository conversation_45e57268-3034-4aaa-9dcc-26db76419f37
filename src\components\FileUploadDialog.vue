<template>
  <el-dialog
    v-model="visible"
    :show-close="false"
    :title="null"
    width="600px"
    :before-close="handleClose"
    class="file-upload-dialog"
  >
    <!-- 自定义头部 -->
    <div class="custom-header">
      <div class="header-left">
        <el-button
          type="text"
          class="close-btn"
          @click="handleClose"
        >
          <el-icon><Close /></el-icon>
        </el-button>
        <span class="header-title">上传/选择文件</span>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          size="small"
          class="complete-btn"
          @click="handleConfirm"
        >
          完成
        </el-button>
      </div>
    </div>

    <div class="file-upload-content">
      <!-- TTS转换区域 -->
      <!-- <div class="tts-section">
        <div class="tts-item">
          <span class="tts-label">TTS标题</span>
          <el-input
            v-model="formData.ttsText"
            placeholder="请输入内容"
            class="tts-input"
          />
        </div>

        <div class="tts-item">
          <span class="tts-label">TTS内容</span>
          <el-input
            v-model="formData.ttsContent"
            type="textarea"
            rows="4"
            placeholder="请输入内容"
            class="tts-input"
          />
        </div>
        <div class="tts-actions">
          <el-button size="small" class="preview-btn" @click="handleTTSPreview">试听</el-button>
          <el-button size="small" type="primary" class="upload-btn" @click="handleTTSUpload">上传</el-button>
        </div>
      </div> -->

      <!-- 音频文件区域 -->
      <div class="audio-section">
        <div class="audio-item">
          <span class="audio-label">录音文件</span>
          <el-input
            v-model="formData.audioFileName"
            placeholder="请输入文件名称"
            class="audio-input"
          />
          <div class="audio-actions">
            <el-button
              size="small"
              :type="isRecording ? 'danger' : 'primary'"
              class="record-btn"
              @click="toggleRecording"
              :disabled="!formData.audioFileName"
            >
              <el-icon><Microphone v-if="!isRecording" /><VideoPause v-else /></el-icon>
              {{ isRecording ? '停止录音' : '开始录音' }}
            </el-button>
            <el-button
              size="small"
              class="preview-btn"
              @click="handleAudioPreview"
              :disabled="!recordedBlob"
            >
              试听
            </el-button>
            <el-button
              size="small"
              type="primary"
              class="upload-btn"
              @click="handleAudioUpload"
              :disabled="!recordedBlob"
            >
              上传
            </el-button>
          </div>
        </div>

        <!-- 录音状态显示 -->
        <div v-if="isRecording" class="recording-status">
          <div class="recording-indicator">
            <div class="recording-dot"></div>
            <span>正在录音... {{ recordingTime }}</span>
          </div>
        </div>

        <!-- 录音预览 -->
        <div v-if="recordedBlob && !isRecording" class="audio-preview">
          <audio ref="audioPreview" controls style="width: 100%; margin-top: 10px;">
            <source :src="audioUrl" type="audio/wav">
            您的浏览器不支持音频播放。
          </audio>
        </div>
      </div>

      <!-- 上传本地文件 -->
      <div class="local-upload-section">
        <el-button type="primary" class="local-upload-btn" @click="handleLocalUpload">
          上传本地文件
        </el-button>
      </div>

      <!-- 已选文件列表 -->
      <div class="selected-files-section">
        <h4>已选文件</h4>
        <el-table
          ref="fileTable"
          :data="selectedFiles"
          style="width: 100%"
          size="small"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" :selectable="selectable" width="55" />
          <el-table-column prop="name" label="文件名称" />
          <el-table-column label="时长" width="100">
            <template #default="scope">
              {{ formatDuration(scope.row.seconds) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <el-button
                type="danger"
                text
                size="small"
                @click="removeSelectedFile(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onUnmounted, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, Microphone, VideoPause } from '@element-plus/icons-vue'
import broadcastManager from '@/api/broadcast/broadcast.js'
import chromeSDKManager from '@/api/broadcast/index.js'

const getMP3List = ()=>{
  chromeSDKManager.quickGetAudioFileList().then(res => {
    console.log(res,"res")
    selectedFiles.value = res.params.files

    // 清空之前的选择状态
    selectedTableFiles.value = []
    if (fileTable.value) {
      fileTable.value.clearSelection()
    }
  })
}


// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm', 'close'])

// 响应式数据
const visible = ref(props.modelValue)
const selectedFiles = ref([])

const formData = reactive({
  ttsText: '',
  ttsContent: '',
  audioFileName: ''
})

// 录音相关数据
const isRecording = ref(false)
const recordedBlob = ref(null)
const audioUrl = ref('')
const recordingTime = ref('00:00')
const mediaRecorder = ref(null)
const audioStream = ref(null)
const recordingTimer = ref(null)
const audioPreview = ref(null)

// 表格选择相关数据
const fileTable = ref(null)
const selectedTableFiles = ref([])

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
  emit('close')
}

const handleConfirm = () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请选择至少一个文件')
    return
  }

  // 检查是否有勾选的文件
  if (selectedTableFiles.value.length === 0) {
    ElMessage.warning('请勾选要确认的文件')
    return
  }

  emit('confirm', selectedTableFiles.value)
  handleClose()
}

// 表格选择变化处理
const handleSelectionChange = (selection) => {
  selectedTableFiles.value = selection
}

// 控制行是否可选择（所有行都可选择）
const selectable = (row, index) => {
  return true
}

const resetForm = () => {
  formData.ttsText = ''
  formData.ttsContent = ''
  formData.audioFileName = ''

  // 清理所有文件的 blob URL
  selectedFiles.value.forEach(file => {
    if (file.blobUrl) {
      URL.revokeObjectURL(file.blobUrl)
      console.log(`重置表单时清理 blob URL: ${file.blobUrl}`)
    }
    if (file.url && file.type === 'recording') {
      URL.revokeObjectURL(file.url)
      console.log(`重置表单时清理录音 URL: ${file.url}`)
    }
  })

  selectedFiles.value = []

  // 清空表格选择状态
  selectedTableFiles.value = []
  if (fileTable.value) {
    fileTable.value.clearSelection()
  }

  // 重置录音相关数据
  if (isRecording.value) {
    stopRecording()
  }
  resetRecording()
}

// TTS 相关方法
const handleTTSPreview = () => {
  if (!formData.ttsContent) {
    ElMessage.warning('请输入TTS内容')
    return
  }
  console.log('TTS试听:', formData.ttsContent)
  // 这里可以调用TTS试听接口
}

const handleTTSUpload = () => {
  if (!formData.ttsText || !formData.ttsContent) {
    ElMessage.warning('请输入TTS标题和内容')
    return
  }
  
  // 模拟上传TTS文件
  const ttsFile = {
    id: Date.now(),
    name: formData.ttsText,
    duration: '00:00:30',
    type: 'tts'
  }
  
  selectedFiles.value.push(ttsFile)
  ElMessage.success('TTS文件上传成功')
  
  // 清空TTS表单
  formData.ttsText = ''
  formData.ttsContent = ''
}

// 录音相关方法
const toggleRecording = async () => {
  if (!formData.audioFileName) {
    ElMessage.warning('请先输入文件名称')
    return
  }

  if (isRecording.value) {
    stopRecording()
  } else {
    await startRecording()
  }
}

const startRecording = async () => {
  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    audioStream.value = stream

    // 创建 MediaRecorder
    mediaRecorder.value = new MediaRecorder(stream)
    const chunks = []

    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data)
      }
    }

    mediaRecorder.value.onstop = () => {
      const blob = new Blob(chunks, { type: 'audio/wav' })
      recordedBlob.value = blob
      audioUrl.value = URL.createObjectURL(blob)

      // 停止所有音频轨道
      stream.getTracks().forEach(track => track.stop())
    }

    // 开始录音
    mediaRecorder.value.start()
    isRecording.value = true

    // 开始计时
    let seconds = 0
    recordingTimer.value = setInterval(() => {
      seconds++
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      recordingTime.value = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }, 1000)

    ElMessage.success('开始录音')
  } catch (error) {
    console.error('录音失败:', error)
    ElMessage.error('无法访问麦克风，请检查权限设置')
  }
}

const stopRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
    mediaRecorder.value.stop()
  }

  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }

  isRecording.value = false
  ElMessage.success('录音已停止')
}

// 音频相关方法
const handleAudioPreview = () => {
  if (recordedBlob.value && audioPreview.value) {
    audioPreview.value.play()
  } else {
    ElMessage.warning('没有可播放的录音')
  }
}

const handleAudioUpload = async () => {
  if (!formData.audioFileName) {
    ElMessage.warning('请输入录音文件名称')
    return
  }

  if (!recordedBlob.value) {
    ElMessage.warning('请先录制音频')
    return
  }

  try {
    // 创建临时文件路径（实际项目中可能需要先保存到临时目录）
    const fileName = formData.audioFileName
    const filePath = `temp/${fileName}.wav` // 临时路径，实际需要根据项目需求调整

    // 调用上传接口
    const result = await broadcastManager.uploadAudioFile(fileName, filePath)

    if (result) {
      // 创建音频文件对象并添加到列表
      const audioFile = {
        id: Date.now(),
        name: formData.audioFileName,
        duration: recordingTime.value,
        type: 'recording',
        blob: recordedBlob.value,
        url: audioUrl.value,
        uploadResult: result
      }

      selectedFiles.value.push(audioFile)
      ElMessage.success('录音文件上传成功')

      // 清空录音相关数据
      resetRecording()
    }
  } catch (error) {
    console.error('上传录音文件失败:', error)
    ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
  }
}

// 重置录音状态
const resetRecording = () => {
  formData.audioFileName = ''
  recordedBlob.value = null
  audioUrl.value = ''
  recordingTime.value = '00:00'

  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value)
  }
}

// 获取音频文件时长（辅助函数）
const getAudioDuration = (file) => {
  return new Promise((resolve) => {
    const audio = new Audio()
    let objectUrl = null

    const cleanup = () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl)
        objectUrl = null
      }
    }

    audio.onloadedmetadata = () => {
      try {
        const duration = Math.floor(audio.duration) || 0
        const minutes = Math.floor(duration / 60)
        const seconds = duration % 60
        const formattedDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        cleanup()
        resolve(formattedDuration)
      } catch (error) {
        console.warn('获取音频时长失败:', error)
        cleanup()
        resolve('00:00')
      }
    }

    audio.onerror = (error) => {
      console.warn('音频加载失败:', error)
      cleanup()
      resolve('00:00') // 如果无法获取时长，返回默认值
    }

    audio.onabort = () => {
      cleanup()
      resolve('00:00')
    }

    try {
      objectUrl = URL.createObjectURL(file)
      audio.src = objectUrl
      // 设置超时，避免长时间等待
      setTimeout(() => {
        if (objectUrl) {
          console.warn('获取音频时长超时')
          cleanup()
          resolve('00:00')
        }
      }, 5000) // 5秒超时
    } catch (error) {
      console.warn('创建音频对象失败:', error)
      cleanup()
      resolve('00:00')
    }
  })
}

// 本地文件上传
const handleLocalUpload = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'audio/*'
  input.multiple = true

  input.onchange = async (event) => {
    const files = event.target.files
    if (files && files.length > 0) {
      const uploadPromises = Array.from(files).map(async (file) => {
        let blobUrl = null
        try {
          // 验证文件类型
          if (!file.type.startsWith('audio/')) {
            console.warn(`文件 ${file.name} 不是音频文件`)
            ElMessage.warning(`文件 ${file.name} 不是音频文件`)
            return false
          }

          // 获取音频时长（如果获取失败则使用默认值）
          let duration = '00:00' // 默认时长
          try {
            duration = await getAudioDuration(file)
          } catch (error) {
            console.warn(`获取文件 ${file.name} 时长失败，使用默认值:`, error)
          }

          // 创建 Blob 对象并生成 blob: URL
          const fileBlob = new Blob([file], { type: file.type })
          blobUrl = URL.createObjectURL(fileBlob)

          // 准备接口参数
          const fileName = file.name.replace(/\.[^/.]+$/, "") // 移除扩展名
          const filePath = blobUrl // 使用生成的 blob: URL

          console.log(`准备上传文件: ${fileName}, blob URL: ${filePath}`)

          // 调用上传接口
          const result = await broadcastManager.uploadAudioFile(fileName, filePath)

          if (result) {
            // 创建文件对象并添加到列表
            const localFile = {
              id: Date.now() + Math.random(),
              name: fileName,
              duration: duration,
              type: 'local',
              file: file,
              blobUrl: blobUrl, // 保存 blob URL 以便后续清理
              uploadResult: result
            }

            getMP3List()
            // selectedFiles.value.push(localFile)

            // 注意：这里不立即清理 blobUrl，因为可能还需要使用
            // 清理工作将在组件卸载或文件删除时进行
            blobUrl = null // 防止在 finally 块中被清理
            return true
          } else {
            // 上传失败，清理 blob URL
            if (blobUrl) {
              URL.revokeObjectURL(blobUrl)
            }
            return false
          }
        } catch (error) {
          console.error(`上传文件 ${file.name} 失败:`, error)
          ElMessage.error(`上传文件 ${file.name} 失败: ${error.message || '未知错误'}`)

          // 发生错误时清理 blob URL
          if (blobUrl) {
            URL.revokeObjectURL(blobUrl)
          }
          return false
        }
      })

      // 等待所有文件上传完成
      const results = await Promise.all(uploadPromises)
      const successCount = results.filter(result => result).length

      if (successCount > 0) {
        ElMessage.success(`成功上传 ${successCount} 个文件`)
      }

      if (successCount < files.length) {
        const failedCount = files.length - successCount
        ElMessage.warning(`${failedCount} 个文件上传失败`)
      }
    }
  }

  input.click()
}

// 格式化时长显示（将秒数转换为 HH:MM:SS 或 MM:SS 格式）
const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) {
    return '00:00'
  }

  const totalSeconds = Math.floor(seconds)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const remainingSeconds = totalSeconds % 60

  if (hours > 0) {
    // 如果有小时，显示 HH:MM:SS 格式
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  } else {
    // 如果没有小时，显示 MM:SS 格式
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }
}

// 删除已选文件
const removeSelectedFile = (index) => {
  const fileToRemove = selectedFiles.value[index]

  // 如果文件有 blobUrl，需要清理以避免内存泄漏
  if (fileToRemove && fileToRemove.blobUrl) {
    URL.revokeObjectURL(fileToRemove.blobUrl)
    console.log(`已清理 blob URL: ${fileToRemove.blobUrl}`)
  }

  // 如果文件有录音的 url，也需要清理
  if (fileToRemove && fileToRemove.url && fileToRemove.type === 'recording') {
    URL.revokeObjectURL(fileToRemove.url)
    console.log(`已清理录音 URL: ${fileToRemove.url}`)
  }

  selectedFiles.value.splice(index, 1)
}

onMounted(() => {
  getMP3List()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (isRecording.value) {
    stopRecording()
  }

  if (audioStream.value) {
    audioStream.value.getTracks().forEach(track => track.stop())
  }

  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
  }

  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value)
  }

  // 清理所有文件的 blob URL
  selectedFiles.value.forEach(file => {
    if (file.blobUrl) {
      URL.revokeObjectURL(file.blobUrl)
      console.log(`组件卸载时清理 blob URL: ${file.blobUrl}`)
    }
    if (file.url && file.type === 'recording') {
      URL.revokeObjectURL(file.url)
      console.log(`组件卸载时清理录音 URL: ${file.url}`)
    }
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body){
  padding: 0 20px 20px 20px;
}
.file-upload-dialog {
  // 自定义头部样式
  .custom-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: -20px -20px 20px -20px;
    background: #fff;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .close-btn {
        padding: 4px;
        color: #909399;
        font-size: 16px;

        &:hover {
          color: #409eff;
        }
      }

      .header-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .header-right {
      .complete-btn {
        padding: 6px 16px;
        border-radius: 4px;
      }
    }
  }

  .file-upload-content {
    min-height: 500px;
    overflow-y: auto;
  }

  .tts-section,
  .audio-section,
  .local-upload-section,
  .selected-files-section {
    margin-bottom: 24px;
    padding: 16px;
  }

  .tts-item,
  .audio-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tts-label,
  .audio-label {
    width: 80px;
    margin-right: 12px;
    font-weight: 500;
    color: #303133;
  }

  .tts-input,
  .audio-input {
    flex: 1;
    margin-right: 8px;
  }

  .required-mark {
    color: #f56c6c;
    margin-left: 4px;
  }

  .tts-actions,
  .audio-actions {
    display: flex;
    gap: 8px;
    margin-left: 12px;
  }

  // 录音相关样式
  .recording-status {
    margin-top: 12px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px;
    border-left: 4px solid #f56c6c;

    .recording-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #f56c6c;
      font-size: 14px;

      .recording-dot {
        width: 8px;
        height: 8px;
        background: #f56c6c;
        border-radius: 50%;
        animation: recording-pulse 1s infinite;
      }
    }
  }

  .audio-preview {
    margin-top: 12px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .record-btn {
    .el-icon {
      margin-right: 4px;
    }
  }

  .local-upload-section {
    text-align: center;
  }

  .local-upload-btn {
    padding: 12px 24px;
  }

  .selected-files-section {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
    }
  }
}

// 录音动画
@keyframes recording-pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
