
.avue-sidebar {
  width: $sidebar_width;
  height: 100%;
  user-select: none;
  position: relative;
  height: 100%;
  position: relative;
  background-color: #fff;
  transition: width .2s;
  box-sizing: border-box;
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .avue-menu {
    height: calc(100% - #{$top_height});
    padding-top: 16px;
  }

  &--tip {
    width: 90%;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    position: absolute;
    top: 5px;
    left: 5%;
    color: #ccc;
    z-index: 2;
    text-align: center;
    font-size: 14px;
    background-color: rgba(0, 0, 0, .4);
  }
  .el-menu{
    padding-left: 12px;
    padding-right: 12px;
  }
  .el-menu-item, .el-sub-menu__title {
    border-radius: 4px;
    height: 54px;
    width: 284px;
    display: flex;
    i {
      margin-right: 16px;
      font-size: 18px;    
    }
    span{
      font-size: 18px;
    }

    i, span {
      color: #333;
    }

    &:hover {
      background: transparent;

      i, span {
        color: #333;
      }
    }

    &.is-active {
      // &:before {
      //   content: '';
      //   top: 0;
      //   left: 0;
      //   bottom: 0;
      //   width: 4px;
      //   background: #409eff;
      //   position: absolute;
      // }
      background-color: #2E74FF;
      // background: linear-gradient(100deg, #6CADFF 0%, #0071FF 100%);

      i, span {
        color: #FFFFFF;
      }
    }
    .el-sub-menu__icon-arrow{
      width: 18px;
    }
  }

}
