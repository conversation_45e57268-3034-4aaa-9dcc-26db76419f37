<template>
    <div class="grouping-container">
        <!-- 左侧分组树 -->
        <div class="left-panel">
            <div class="panel-header">
                <h3 class="panel-title">设备分组</h3>
                <el-button
                    type="primary"
                    size="small"
                    @click="showAddGroupDialog"
                    :icon="Plus"
                >
                    新建分组
                </el-button>
            </div>

            <div class="group-tree">
                <el-tree
                    ref="groupTreeRef"
                    :data="groupTreeData"
                    :props="treeProps"
                    node-key="id"
                    :current-node-key="currentGroupId"
                    :expand-on-click-node="false"
                    @node-click="handleGroupClick"
                    class="group-tree-component"
                >
                    <template #default="{ node, data }">
                        <div class="tree-node">
                            <div class="node-content">
                                <el-icon class="node-icon">
                                    <Folder v-if="!data.isDefault" />
                                    <FolderOpened v-else />
                                </el-icon>
                                <span class="node-label">{{ node.label }}</span>
                                <span class="device-count">({{ data.deviceCount || 0 }})</span>
                            </div>
                            <div class="node-actions" v-if="!data.isDefault">
                                <el-button
                                    type="text"
                                    size="small"
                                    @click.stop="editGroup(data)"
                                    :icon="Edit"
                                />
                                <el-button
                                    type="text"
                                    size="small"
                                    @click.stop="deleteGroup(data)"
                                    :icon="Delete"
                                />
                            </div>
                        </div>
                    </template>
                </el-tree>
            </div>
        </div>

        <!-- 右侧设备列表 -->
        <div class="right-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    {{ currentGroupName }}
                    <span class="device-total">(共 {{ deviceList.length }} 个设备)</span>
                </h3>
                <div class="panel-actions">
                    <el-button
                        type="danger"
                        size="small"
                        :disabled="selectedDevices.length === 0"
                        @click="batchMoveDevices"
                    >
                        批量移动
                    </el-button>
                    <el-button
                        type="danger"
                        size="small"
                        :disabled="selectedDevices.length === 0"
                        @click="batchDeleteDevices"
                    >
                        批量删除
                    </el-button>
                </div>
            </div>

            <!-- 设备表格 -->
            <div class="device-table-container">
                <el-table
                    :data="deviceList"
                    style="width: 100%"
                    size="small"
                    @selection-change="handleDeviceSelectionChange"
                    v-loading="deviceLoading"
                >
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="deviceName" label="设备名称" min-width="150">
                        <template #default="scope">
                            <div class="device-name">
                                <el-icon class="device-icon">
                                    <Monitor v-if="scope.row.deviceType === 'Ipc'" />
                                    <Bell v-else />
                                </el-icon>
                                {{ scope.row.deviceName }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="deviceType" label="设备类型" width="100">
                        <template #default="scope">
                            <el-tag
                                :type="scope.row.deviceType === 'Alarm' ? 'danger' : 'primary'"
                                size="small"
                            >
                                {{ scope.row.deviceType === 'Alarm' ? '报警设备' : '联动设备' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="deviceId" label="设备ID" width="120"></el-table-column>
                    <el-table-column prop="deviceStatus" label="设备状态" width="100">
                        <template #default="scope">
                            <span :class="['status-tag', scope.row.deviceStatus === 'Offline' ? 'offline' : 'online']">
                                {{ scope.row.deviceStatus === 'Offline' ? '离线' : '在线' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="deviceContact" label="联系人" width="100"></el-table-column>
                    <el-table-column prop="devicePhone" label="联系电话" width="120"></el-table-column>
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button
                                type="text"
                                size="small"
                                @click="editDevice(scope.row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                type="text"
                                size="small"
                                @click="moveDeviceToGroup(scope.row)"
                            >
                                移动
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>

        <!-- 添加分组对话框 -->
        <el-dialog
            v-model="addGroupDialogVisible"
            title="新建分组"
            width="500px"
            :before-close="handleAddGroupDialogClose"
        >
            <el-form
                ref="addGroupFormRef"
                :model="addGroupForm"
                :rules="addGroupRules"
                label-width="80px"
            >
                <el-form-item label="分组类型" prop="groupType">
                    <el-select v-model="addGroupForm.groupType" placeholder="请选择分组类型">
                        <el-option label="普通分组" value="normal"></el-option>
                        <el-option label="联动分组" value="linkage"></el-option>
                        <el-option label="联防分组" value="defense"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="分组名称" prop="groupName">
                    <el-input v-model="addGroupForm.groupName" placeholder="请输入分组名称"></el-input>
                </el-form-item>
                <el-form-item label="联系人" prop="groupContact">
                    <el-input v-model="addGroupForm.groupContact" placeholder="请输入联系人"></el-input>
                </el-form-item>
                <el-form-item label="联系电话" prop="groupPhone">
                    <el-input v-model="addGroupForm.groupPhone" placeholder="请输入联系电话"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addGroupDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmAddGroup" :loading="addGroupLoading">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 编辑分组对话框 -->
        <el-dialog
            v-model="editGroupDialogVisible"
            title="编辑分组"
            width="500px"
            :before-close="handleEditGroupDialogClose"
        >
            <el-form
                ref="editGroupFormRef"
                :model="editGroupForm"
                :rules="editGroupRules"
                label-width="80px"
            >
                <el-form-item label="分组名称" prop="groupName">
                    <el-input v-model="editGroupForm.groupName" placeholder="请输入分组名称"></el-input>
                </el-form-item>
                <el-form-item label="联系人" prop="groupContact">
                    <el-input v-model="editGroupForm.groupContact" placeholder="请输入联系人"></el-input>
                </el-form-item>
                <el-form-item label="联系电话" prop="groupPhone">
                    <el-input v-model="editGroupForm.groupPhone" placeholder="请输入联系电话"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editGroupDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmEditGroup" :loading="editGroupLoading">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 编辑设备对话框 -->
        <el-dialog
            v-model="editDeviceDialogVisible"
            title="编辑设备"
            width="500px"
            :before-close="handleEditDeviceDialogClose"
        >
            <el-form
                ref="editDeviceFormRef"
                :model="editDeviceForm"
                :rules="editDeviceRules"
                label-width="80px"
            >
                <el-form-item label="设备名称" prop="deviceName">
                    <el-input v-model="editDeviceForm.deviceName" placeholder="请输入设备名称"></el-input>
                </el-form-item>
                <el-form-item label="联系人" prop="deviceContact">
                    <el-input v-model="editDeviceForm.deviceContact" placeholder="请输入联系人"></el-input>
                </el-form-item>
                <el-form-item label="联系电话" prop="devicePhone">
                    <el-input v-model="editDeviceForm.devicePhone" placeholder="请输入联系电话"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editDeviceDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmEditDevice" :loading="editDeviceLoading">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 移动设备对话框 -->
        <el-dialog
            v-model="moveDeviceDialogVisible"
            title="移动设备到分组"
            width="400px"
        >
            <el-form label-width="80px">
                <el-form-item label="目标分组">
                    <el-select v-model="targetGroupId" placeholder="请选择目标分组" style="width: 100%">
                        <el-option
                            v-for="group in availableGroups"
                            :key="group.id"
                            :label="group.label"
                            :value="group.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="moveDeviceDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmMoveDevice" :loading="moveDeviceLoading">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    Plus,
    Edit,
    Delete,
    Folder,
    FolderOpened,
    Monitor,
    Bell
} from '@element-plus/icons-vue'
import chromeSDKManager from '@/api/broadcast/index.js'

// ==================== 响应式数据 ====================

// 分组树相关
const groupTreeRef = ref(null)
const groupTreeData = ref([])
const currentGroupId = ref(0) // 0表示未分组
const currentGroupName = ref('未分组')

// 树形组件配置
const treeProps = {
    children: 'children',
    label: 'label'
}

// 设备列表相关
const deviceList = ref([])
const selectedDevices = ref([])
const deviceLoading = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 添加分组对话框
const addGroupDialogVisible = ref(false)
const addGroupFormRef = ref(null)
const addGroupLoading = ref(false)
const addGroupForm = ref({
    groupType: 'normal',
    groupName: '',
    groupContact: '',
    groupPhone: ''
})

const addGroupRules = {
    groupType: [
        { required: true, message: '请选择分组类型', trigger: 'change' }
    ],
    groupName: [
        { required: true, message: '请输入分组名称', trigger: 'blur' },
        { min: 1, max: 50, message: '分组名称长度在 1 到 50 个字符', trigger: 'blur' }
    ]
}

// 编辑分组对话框
const editGroupDialogVisible = ref(false)
const editGroupFormRef = ref(null)
const editGroupLoading = ref(false)
const editGroupForm = ref({
    groupId: null,
    groupName: '',
    groupContact: '',
    groupPhone: ''
})

const editGroupRules = {
    groupName: [
        { required: true, message: '请输入分组名称', trigger: 'blur' },
        { min: 1, max: 50, message: '分组名称长度在 1 到 50 个字符', trigger: 'blur' }
    ]
}

// 编辑设备对话框
const editDeviceDialogVisible = ref(false)
const editDeviceFormRef = ref(null)
const editDeviceLoading = ref(false)
const editDeviceForm = ref({
    deviceId: null,
    deviceName: '',
    deviceContact: '',
    devicePhone: ''
})

const editDeviceRules = {
    deviceName: [
        { required: true, message: '请输入设备名称', trigger: 'blur' },
        { min: 1, max: 50, message: '设备名称长度在 1 到 50 个字符', trigger: 'blur' }
    ]
}

// 移动设备对话框
const moveDeviceDialogVisible = ref(false)
const moveDeviceLoading = ref(false)
const targetGroupId = ref(null)
const currentMoveDevice = ref(null)
const currentMoveDevices = ref([])

// 计算属性
const availableGroups = computed(() => {
    return groupTreeData.value.filter(group => group.id !== currentGroupId.value)
})

// ==================== 初始化方法 ====================

/**
 * 页面初始化
 */
const initPage = async () => {
    try {
        console.log('分组管理页面初始化开始...')

        // 初始化SDK
        await initSDK()

        // 登录
        await login()

        // 加载分组数据
        await loadGroupData()

        // 加载设备数据（默认显示未分组设备）
        await loadDeviceData(0)

        console.log('分组管理页面初始化完成')
    } catch (error) {
        console.error('页面初始化失败:', error)
        ElMessage.error('页面初始化失败: ' + error.message)
    }
}

/**
 * 初始化SDK
 */
const initSDK = async () => {
    try {
        if (typeof DHAlarmWeb !== 'undefined') {
            const dhWeb = new DHAlarmWeb()
            chromeSDKManager.init(dhWeb)
            console.log('Chrome SDK初始化成功')
        } else {
            throw new Error('DHAlarmWeb未加载')
        }
    } catch (error) {
        console.error('SDK初始化失败:', error)
        throw error
    }
}

/**
 * 登录
 */
const login = async () => {
    try {
        const result = await chromeSDKManager.quickLogin('mainalc', '123456789', '192.168.3.18')
        // const result = await chromeSDKManager.quickLogin('mainalc', '123456789', '192.168.1.3')
        if (result) {
            console.log('登录成功', result)
        } else {
            throw new Error('登录失败')
        }
    } catch (error) {
        console.error('登录失败:', error)
        throw error
    }
}

/**
 * 加载分组数据
 */
const loadGroupData = async () => {
    try {
        // 设置设备列表回调
        setupDeviceListCallback()

        // 设置分组列表回调
        setupGroupListCallback()

        // 初始化分组树数据（包含默认的"未分组"）
        groupTreeData.value = [
            {
                id: 0,
                label: '未分组',
                isDefault: true,
                deviceCount: 0,
                children: []
            }
        ]

        console.log('分组数据加载完成')
    } catch (error) {
        console.error('加载分组数据失败:', error)
        throw error
    }
}

/**
 * 设置设备列表回调
 */
const setupDeviceListCallback = () => {
    chromeSDKManager.on('onDeviceList', (result) => {
        console.log('设备列表回调:', result)

        if (!result || !result.params) {
            console.warn('设备列表回调数据格式异常:', result)
            return
        }

        // 处理单个设备或设备列表
        let newDevices = []
        if (result.params.list && Array.isArray(result.params.list)) {
            newDevices = result.params.list
        } else if (result.params.device || result.params.deviceId) {
            newDevices = [result.params]
        } else {
            console.warn('无法识别的设备数据格式:', result.params)
            return
        }

        // 累积存储设备信息并去重
        newDevices.forEach(device => {
            if (!device) return

            const deviceKey = device.deviceId || device.id || device.mac || device.ip
            if (!deviceKey) {
                console.warn('设备缺少唯一标识符:', device)
                return
            }

            // 检查设备是否已存在
            const existingIndex = deviceList.value.findIndex(existingDevice => {
                const existingKey = existingDevice.deviceId || existingDevice.id || existingDevice.mac || existingDevice.ip
                return existingKey === deviceKey
            })

            if (existingIndex !== -1) {
                // 更新已存在设备
                deviceList.value[existingIndex] = { ...deviceList.value[existingIndex], ...device }
            } else {
                // 添加新设备
                deviceList.value.push(device)
            }
        })

        // 更新设备总数
        total.value = deviceList.value.length

        // 更新分组中的设备数量
        updateGroupDeviceCount()

        console.log('当前设备总数:', deviceList.value.length)
    })
}

/**
 * 设置分组列表回调
 */
const setupGroupListCallback = () => {
    chromeSDKManager.on('onGroupList', (result) => {
        console.log('分组列表回调:', result)

        if (result && result.params && result.params.list) {
            const groups = result.params.list.map(group => ({
                id: group.groupId || group.id,
                label: group.groupName || group.name,
                isDefault: false,
                deviceCount: 0,
                groupType: group.groupType,
                groupContact: group.groupContact,
                groupPhone: group.groupPhone,
                children: []
            }))

            // 保留未分组，添加其他分组
            groupTreeData.value = [
                groupTreeData.value[0], // 未分组
                ...groups
            ]

            console.log('分组列表更新完成:', groupTreeData.value)
        }
    })
}

/**
 * 加载设备数据
 */
const loadDeviceData = async (groupId = 0) => {
    try {
        deviceLoading.value = true

        // 清空当前设备列表
        deviceList.value = []

        // 根据分组ID过滤设备（这里需要根据实际API调整）
        // 暂时显示所有设备，后续可以根据分组过滤
        console.log('加载分组设备:', groupId)

    } catch (error) {
        console.error('加载设备数据失败:', error)
        ElMessage.error('加载设备数据失败: ' + error.message)
    } finally {
        deviceLoading.value = false
    }
}

/**
 * 更新分组设备数量
 */
const updateGroupDeviceCount = () => {
    // 这里需要根据实际的设备分组关系来计算
    // 暂时将所有设备都算在未分组中
    if (groupTreeData.value.length > 0) {
        groupTreeData.value[0].deviceCount = deviceList.value.length
    }
}

// ==================== 事件处理方法 ====================

/**
 * 分组点击事件
 */
const handleGroupClick = (data) => {
    currentGroupId.value = data.id
    currentGroupName.value = data.label
    loadDeviceData(data.id)
    console.log('选择分组:', data)
}

/**
 * 设备选择变化事件
 */
const handleDeviceSelectionChange = (selection) => {
    selectedDevices.value = selection
    console.log('选中的设备:', selection)
}

/**
 * 分页大小变化
 */
const handleSizeChange = (val) => {
    pageSize.value = val
    loadDeviceData(currentGroupId.value)
}

/**
 * 当前页变化
 */
const handleCurrentChange = (val) => {
    currentPage.value = val
    loadDeviceData(currentGroupId.value)
}

// ==================== 分组管理方法 ====================

/**
 * 显示添加分组对话框
 */
const showAddGroupDialog = () => {
    addGroupForm.value = {
        groupType: 'normal',
        groupName: '',
        groupContact: '',
        groupPhone: ''
    }
    addGroupDialogVisible.value = true
}

/**
 * 处理添加分组对话框关闭
 */
const handleAddGroupDialogClose = (done) => {
    if (addGroupFormRef.value) {
        addGroupFormRef.value.resetFields()
    }
    done()
}

/**
 * 确认添加分组
 */
const confirmAddGroup = async () => {
    try {
        await addGroupFormRef.value.validate()

        addGroupLoading.value = true

        const { groupType, groupName, groupContact, groupPhone } = addGroupForm.value

        await chromeSDKManager.getGroupUserManager().addGroup(
            groupType,
            groupName,
            groupContact,
            groupPhone
        )

        ElMessage.success('分组添加成功')
        addGroupDialogVisible.value = false

        // 重新加载分组数据
        await loadGroupData()

    } catch (error) {
        console.error('添加分组失败:', error)
        ElMessage.error('添加分组失败: ' + error.message)
    } finally {
        addGroupLoading.value = false
    }
}

/**
 * 编辑分组
 */
const editGroup = (groupData) => {
    editGroupForm.value = {
        groupId: groupData.id,
        groupName: groupData.label,
        groupContact: groupData.groupContact || '',
        groupPhone: groupData.groupPhone || ''
    }
    editGroupDialogVisible.value = true
}

/**
 * 处理编辑分组对话框关闭
 */
const handleEditGroupDialogClose = (done) => {
    if (editGroupFormRef.value) {
        editGroupFormRef.value.resetFields()
    }
    done()
}

/**
 * 确认编辑分组
 */
const confirmEditGroup = async () => {
    try {
        await editGroupFormRef.value.validate()

        editGroupLoading.value = true

        const { groupId, groupName, groupContact, groupPhone } = editGroupForm.value

        await chromeSDKManager.getGroupUserManager().editGroup(
            groupId,
            groupName,
            groupContact,
            groupPhone
        )

        ElMessage.success('分组编辑成功')
        editGroupDialogVisible.value = false

        // 重新加载分组数据
        await loadGroupData()

    } catch (error) {
        console.error('编辑分组失败:', error)
        ElMessage.error('编辑分组失败: ' + error.message)
    } finally {
        editGroupLoading.value = false
    }
}

/**
 * 删除分组
 */
const deleteGroup = async (groupData) => {
    try {
        await ElMessageBox.confirm(
            `确定要删除分组 "${groupData.label}" 吗？`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        await chromeSDKManager.getGroupUserManager().delGroup(groupData.id)

        ElMessage.success('分组删除成功')

        // 重新加载分组数据
        await loadGroupData()

        // 如果删除的是当前选中的分组，切换到未分组
        if (currentGroupId.value === groupData.id) {
            currentGroupId.value = 0
            currentGroupName.value = '未分组'
            await loadDeviceData(0)
        }

    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除分组失败:', error)
            ElMessage.error('删除分组失败: ' + error.message)
        }
    }
}

// ==================== 设备管理方法 ====================

/**
 * 编辑设备
 */
const editDevice = (deviceData) => {
    editDeviceForm.value = {
        deviceId: deviceData.deviceId || deviceData.id,
        deviceName: deviceData.deviceName || '',
        deviceContact: deviceData.deviceContact || '',
        devicePhone: deviceData.devicePhone || ''
    }
    editDeviceDialogVisible.value = true
}

/**
 * 处理编辑设备对话框关闭
 */
const handleEditDeviceDialogClose = (done) => {
    if (editDeviceFormRef.value) {
        editDeviceFormRef.value.resetFields()
    }
    done()
}

/**
 * 确认编辑设备
 */
const confirmEditDevice = async () => {
    try {
        await editDeviceFormRef.value.validate()

        editDeviceLoading.value = true

        const { deviceId, deviceName, deviceContact, devicePhone } = editDeviceForm.value

        await chromeSDKManager.getGroupUserManager().editDevice(
            deviceId,
            deviceName,
            deviceContact,
            devicePhone
        )

        ElMessage.success('设备编辑成功')
        editDeviceDialogVisible.value = false

        // 重新加载设备数据
        await loadDeviceData(currentGroupId.value)

    } catch (error) {
        console.error('编辑设备失败:', error)
        ElMessage.error('编辑设备失败: ' + error.message)
    } finally {
        editDeviceLoading.value = false
    }
}

/**
 * 移动设备到分组
 */
const moveDeviceToGroup = (deviceData) => {
    currentMoveDevice.value = deviceData
    currentMoveDevices.value = [deviceData]
    targetGroupId.value = null
    moveDeviceDialogVisible.value = true
}

/**
 * 批量移动设备
 */
const batchMoveDevices = () => {
    if (selectedDevices.value.length === 0) {
        ElMessage.warning('请先选择要移动的设备')
        return
    }

    currentMoveDevice.value = null
    currentMoveDevices.value = [...selectedDevices.value]
    targetGroupId.value = null
    moveDeviceDialogVisible.value = true
}

/**
 * 确认移动设备
 */
const confirmMoveDevice = async () => {
    try {
        if (!targetGroupId.value) {
            ElMessage.warning('请选择目标分组')
            return
        }

        moveDeviceLoading.value = true

        // 移动所有选中的设备
        for (const device of currentMoveDevices.value) {
            const deviceId = device.deviceId || device.id
            await chromeSDKManager.getGroupUserManager().moveDevice(deviceId, targetGroupId.value)
        }

        ElMessage.success(`成功移动 ${currentMoveDevices.value.length} 个设备`)
        moveDeviceDialogVisible.value = false

        // 重新加载设备数据
        await loadDeviceData(currentGroupId.value)

        // 清空选中的设备
        selectedDevices.value = []

    } catch (error) {
        console.error('移动设备失败:', error)
        ElMessage.error('移动设备失败: ' + error.message)
    } finally {
        moveDeviceLoading.value = false
    }
}

/**
 * 批量删除设备
 */
const batchDeleteDevices = async () => {
    try {
        if (selectedDevices.value.length === 0) {
            ElMessage.warning('请先选择要删除的设备')
            return
        }

        await ElMessageBox.confirm(
            `确定要删除选中的 ${selectedDevices.value.length} 个设备吗？`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        // 这里需要根据实际API实现设备删除
        // 暂时只是从列表中移除
        const deviceIds = selectedDevices.value.map(device => device.deviceId || device.id)
        deviceList.value = deviceList.value.filter(device => {
            const deviceId = device.deviceId || device.id
            return !deviceIds.includes(deviceId)
        })

        ElMessage.success(`成功删除 ${selectedDevices.value.length} 个设备`)
        selectedDevices.value = []

    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除设备失败:', error)
            ElMessage.error('删除设备失败: ' + error.message)
        }
    }
}

// ==================== 生命周期 ====================

onMounted(() => {
    console.log('分组管理页面已加载')
    initPage()
})
</script>

<style scoped lang="scss">
.grouping-container {
    display: flex;
    height: calc(100vh - 56px);
    background: #f5f7fa;
}

// 左侧分组面板
.left-panel {
    width: 300px;
    background: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
}

// 右侧设备面板
.right-panel {
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
}

// 面板头部
.panel-header {
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .panel-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }

    .device-total {
        font-size: 14px;
        color: #909399;
        font-weight: normal;
    }

    .panel-actions {
        display: flex;
        gap: 12px;
    }
}

// 分组树
.group-tree {
    flex: 1;
    padding: 10px;
    overflow-y: auto;

    .group-tree-component {
        :deep(.el-tree-node__content) {
            height: 40px;
            padding: 0 8px;

            &:hover {
                background-color: #f5f7fa;
            }
        }

        :deep(.el-tree-node__content.is-current) {
            background-color: #e6f7ff;
            border-left: 3px solid #2283FF;
        }
    }
}

.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .node-content {
        display: flex;
        align-items: center;
        flex: 1;

        .node-icon {
            margin-right: 8px;
            color: #2283FF;
        }

        .node-label {
            font-size: 14px;
            color: #303133;
            margin-right: 8px;
        }

        .device-count {
            font-size: 12px;
            color: #909399;
        }
    }

    .node-actions {
        display: flex;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.3s ease;

        .el-button {
            padding: 4px;
            min-height: auto;

            &:hover {
                background-color: #f0f0f0;
            }
        }
    }

    &:hover .node-actions {
        opacity: 1;
    }
}

// 设备表格容器
.device-table-container {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .el-table {
        flex: 1;

        .device-name {
            display: flex;
            align-items: center;

            .device-icon {
                margin-right: 8px;
                color: #2283FF;
            }
        }

        .status-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;

            &.online {
                background: #f0f9ff;
                color: #67c23a;
            }

            &.offline {
                background: #fef0f0;
                color: #f56c6c;
            }
        }
    }
}

// 分页
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    :deep(.el-pagination) {
        .el-pager li {
            border-radius: 4px;
            margin: 0 2px;
        }

        .btn-prev,
        .btn-next {
            border-radius: 4px;
        }
    }
}

// 对话框样式
:deep(.el-dialog) {
    .el-dialog__header {
        padding: 20px 20px 10px;

        .el-dialog__title {
            font-size: 16px;
            font-weight: 600;
        }
    }

    .el-dialog__body {
        padding: 10px 20px 20px;
    }

    .el-dialog__footer {
        padding: 10px 20px 20px;
        text-align: right;
    }
}

// 表单样式
.el-form {
    .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
            font-weight: 500;
            color: #606266;
        }

        .el-input,
        .el-select {
            width: 100%;
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .left-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .grouping-container {
        flex-direction: column;
        height: auto;
    }

    .left-panel {
        width: 100%;
        height: 300px;
    }

    .panel-header {
        padding: 15px;

        .panel-actions {
            flex-direction: column;
            gap: 8px;
        }
    }

    .device-table-container {
        padding: 15px;
    }
}
</style>