<template>
  <div class="containerHistory">
    <div v-if="!isDetailView" class="content">
      <el-table :data="tableData" v-loading="loading" stripe style="width: 100%; height: calc(100% - 48px);">
        <el-table-column prop="deviceId" label="物联网ID" />
        <el-table-column prop="tvoc" label="总挥发性有机物 (ppb)" />
        <el-table-column prop="hcho" label="甲醛 (mg/m³)" />
        <el-table-column prop="so2" label="二氧化硫 (ppm)" />
        <el-table-column prop="nh3" label="氨气 (ppm)" />
        <el-table-column prop="h2S" label="硫化氢 (ppm)" />
        <el-table-column prop="createTime" label="创建时间" />
      </el-table>
      <div class="pagination">
        <el-pagination
          :page-size="page.pageSize"
          :current-page="page.currentPage"
          background
          layout="prev, pager, next"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
import GasSensorDetail from '@/components/lamp/gasSensor/GasSensorDetail.vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { getHistoryData } from '@/api/lamp/gasSensor.js'
import { ElMessage } from 'element-plus'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const isDetailView = ref(false)
const tableData = ref([])
const loading = ref(false)
const page = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const detailDialogVisible = ref(false)
const currentDetailData = ref({})

const getTableData = async () => {
  loading.value = true
  try {
    const res = await getHistoryData({
      current: page.value.currentPage,
      size: page.value.pageSize,
      deviceId: props.data.deviceId
    })
    if (res.data.data) {
      tableData.value = res.data.data.records || []
      page.value.total = res.data.data.total || 0
    }
  } catch (error) {
    ElMessage.error('获取历史数据失败')
    console.error('获取历史数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (val) => {
  page.value.pageSize = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value.currentPage = val
  getTableData()
}

onMounted(() => {
  getTableData()
})
</script>

<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.containerHistory {
  // padding: 16px;
  height: 94%;
  .content{
    height: 100%;
    padding: 16px;
    border-radius: 4px;
    background-color: #FFF;
  }
  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
  
  :deep(.el-table) {
    .el-table__header-wrapper {
      th {
        background-color: #F5F7FA;
        color: #606266;
        font-weight: 500;
      }
    }
    
    .el-table__row {
      td {
        padding: 8px 0;
      }
    }
  }
}
.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}

.detail-header {
  margin-bottom: 16px;
}
</style>