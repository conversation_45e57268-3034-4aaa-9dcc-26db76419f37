<template>
  <div class="volume-control">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>音量控制</span>
          <el-tag v-if="selectedDevice" type="info" size="small">
            设备: {{ selectedDevice.deviceName || selectedDevice.deviceId }}
          </el-tag>
        </div>
      </template>

      <!-- 麦克风音量控制 -->
      <el-row :gutter="20" class="volume-row">
        <el-col :span="24">
          <div class="volume-item">
            <div class="volume-label">
              <el-icon><Microphone /></el-icon>
              <span>麦克风音量</span>
            </div>
            <div class="volume-controls">
              <el-button 
                size="small" 
                @click="adjustMicVolume(-10)"
                :disabled="!canOperate || micVolume <= 0"
              >
                -10
              </el-button>
              <div class="volume-slider">
                <el-slider
                  v-model="micVolume"
                  :min="0"
                  :max="100"
                  :step="1"
                  :disabled="!canOperate"
                  @change="handleMicVolumeChange"
                  show-input
                  :show-input-controls="false"
                />
              </div>
              <el-button 
                size="small" 
                @click="adjustMicVolume(10)"
                :disabled="!canOperate || micVolume >= 100"
              >
                +10
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 广播音量控制 -->
      <el-row :gutter="20" class="volume-row">
        <el-col :span="24">
          <div class="volume-item">
            <div class="volume-label">
              <el-icon><Speaker /></el-icon>
              <span>广播音量</span>
            </div>
            <div class="volume-controls">
              <el-button 
                size="small" 
                @click="adjustBroadcastVolume(-10)"
                :disabled="!canOperate || broadcastVolume <= 0"
              >
                -10
              </el-button>
              <div class="volume-slider">
                <el-slider
                  v-model="broadcastVolume"
                  :min="0"
                  :max="100"
                  :step="1"
                  :disabled="!canOperate"
                  @change="handleBroadcastVolumeChange"
                  show-input
                  :show-input-controls="false"
                />
              </div>
              <el-button 
                size="small" 
                @click="adjustBroadcastVolume(10)"
                :disabled="!canOperate || broadcastVolume >= 100"
              >
                +10
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 快捷操作 -->
      <el-row :gutter="20" class="volume-row">
        <el-col :span="24">
          <div class="quick-actions">
            <el-button-group size="small">
              <el-button @click="setVolumePreset(0)" :disabled="!canOperate">静音</el-button>
              <el-button @click="setVolumePreset(30)" :disabled="!canOperate">低音</el-button>
              <el-button @click="setVolumePreset(60)" :disabled="!canOperate">中音</el-button>
              <el-button @click="setVolumePreset(80)" :disabled="!canOperate">高音</el-button>
              <el-button @click="setVolumePreset(100)" :disabled="!canOperate">最大</el-button>
            </el-button-group>
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshVolumes"
              :disabled="!canOperate"
              :loading="refreshing"
            >
              刷新音量
            </el-button>
          </div>
        </el-col>
      </el-row>

      <!-- 音量状态显示 -->
      <el-row :gutter="20" class="volume-row" v-if="volumeStatus">
        <el-col :span="24">
          <el-alert
            :title="volumeStatus.title"
            :type="volumeStatus.type"
            :description="volumeStatus.description"
            show-icon
            :closable="false"
          />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import intercomAPI from '@/api/intercom/broadcast.js';

export default {
  name: 'VolumeControl',
  props: {
    selectedDevice: {
      type: Object,
      default: null
    },
    isConnected: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      micVolume: 80,
      broadcastVolume: 80,
      refreshing: false,
      volumeStatus: null
    };
  },
  
  computed: {
    canOperate() {
      return this.isConnected && this.selectedDevice;
    }
  },
  
  watch: {
    selectedDevice: {
      handler(newDevice) {
        if (newDevice && this.isConnected) {
          this.refreshVolumes();
        }
      },
      immediate: true
    }
  },
  
  methods: {
    // 调整麦克风音量
    adjustMicVolume(delta) {
      const newVolume = Math.max(0, Math.min(100, this.micVolume + delta));
      this.micVolume = newVolume;
      this.handleMicVolumeChange(newVolume);
    },
    
    // 调整广播音量
    adjustBroadcastVolume(delta) {
      const newVolume = Math.max(0, Math.min(100, this.broadcastVolume + delta));
      this.broadcastVolume = newVolume;
      this.handleBroadcastVolumeChange(newVolume);
    },
    
    // 处理麦克风音量变化
    async handleMicVolumeChange(value) {
      if (!this.canOperate) {
        return;
      }

      try {
        const requestId = intercomAPI.generateRequestId();
        await intercomAPI.setDeviceVolume(
          requestId,
          this.selectedDevice.deviceId,
          'input',
          [value]
        );
        
        console.log(`✅ 麦克风音量已设置为: ${value}%`);
        this.showVolumeStatus('success', '麦克风音量设置成功', `音量已调整为 ${value}%`);
        
        this.$emit('volume-changed', { type: 'microphone', value });
      } catch (error) {
        console.error('❌ 设置麦克风音量失败:', error);
        this.showVolumeStatus('error', '麦克风音量设置失败', error.message);
      }
    },
    
    // 处理广播音量变化
    async handleBroadcastVolumeChange(value) {
      if (!this.canOperate) {
        return;
      }

      try {
        const requestId = intercomAPI.generateRequestId();
        await intercomAPI.setBroadcastVolume(
          requestId,
          this.selectedDevice.deviceId,
          [value]
        );
        
        console.log(`✅ 广播音量已设置为: ${value}%`);
        this.showVolumeStatus('success', '广播音量设置成功', `音量已调整为 ${value}%`);
        
        this.$emit('volume-changed', { type: 'broadcast', value });
      } catch (error) {
        console.error('❌ 设置广播音量失败:', error);
        this.showVolumeStatus('error', '广播音量设置失败', error.message);
      }
    },
    
    // 设置音量预设
    async setVolumePreset(volume) {
      this.micVolume = volume;
      this.broadcastVolume = volume;
      
      await Promise.all([
        this.handleMicVolumeChange(volume),
        this.handleBroadcastVolumeChange(volume)
      ]);
    },
    
    // 刷新当前音量
    async refreshVolumes() {
      if (!this.canOperate) {
        return;
      }
      
      this.refreshing = true;
      try {
        const requestId = intercomAPI.generateRequestId();
        
        // 获取麦克风音量
        const micVolumeResult = await intercomAPI.getDeviceVolume(
          requestId,
          this.selectedDevice.deviceId,
          'input'
        );
        
        // 获取广播音量
        const broadcastVolumeResult = await intercomAPI.getBroadcastVolume(
          requestId + 1,
          this.selectedDevice.deviceId
        );
        
        console.log('音量信息:', {
          microphone: micVolumeResult,
          broadcast: broadcastVolumeResult
        });
        
        // 更新音量值（根据实际API返回结构调整）
        if (micVolumeResult.params && micVolumeResult.params.volume && micVolumeResult.params.volume.length > 0) {
          this.micVolume = micVolumeResult.params.volume[0];
        }
        
        if (broadcastVolumeResult.params && broadcastVolumeResult.params.volume && broadcastVolumeResult.params.volume.length > 0) {
          this.broadcastVolume = broadcastVolumeResult.params.volume[0];
        }
        
        this.showVolumeStatus('success', '音量信息已刷新', `麦克风: ${this.micVolume}%, 广播: ${this.broadcastVolume}%`);
        
      } catch (error) {
        console.error('❌ 获取音量信息失败:', error);
        this.showVolumeStatus('error', '获取音量信息失败', error.message);
      } finally {
        this.refreshing = false;
      }
    },
    
    // 显示音量状态
    showVolumeStatus(type, title, description) {
      this.volumeStatus = { type, title, description };
      
      // 3秒后自动隐藏状态
      setTimeout(() => {
        this.volumeStatus = null;
      }, 3000);
    }
  }
};
</script>

<style scoped>
.volume-control {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.volume-row {
  margin-bottom: 16px;
}

.volume-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.volume-label {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  font-weight: 500;
}

.volume-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.volume-slider {
  flex: 1;
  margin: 0 8px;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

.el-alert {
  margin-top: 8px;
}
</style>
