/**
 * Chrome SDK 设备管理相关接口
 * 基于Chrome SDK接口文档
 */
import chromeSDK from './chromeSDK.js';

/**
 * 设备管理类
 */
class DeviceManager {
  constructor() {
    this.sdk = chromeSDK;
  }

  /**
   * 生成请求ID
   * @returns {number} 请求ID
   */
  generateRequestId() {
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  // ==================== 设备推送配置接口 ====================

  /**
   * 获取设备推送配置
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 推送配置
   */
  getWebPush(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetWebPush;
      this.sdk.callbacks.onGetWebPush = (result) => {
        this.sdk.callbacks.onGetWebPush = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getWebPush(requestId, loginHandle, deviceId);
      } catch (error) {
        this.sdk.callbacks.onGetWebPush = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 编辑设备推送配置
   * @param {number} deviceId - 设备ID
   * @param {string} pushUrl - 推送接收url
   * @param {string} context - 用户数据
   * @param {string} isAuth - 是否需要认证 0-否 1-是
   * @param {string} userName - 认证用户名
   * @param {string} password - 认证密码
   * @returns {Promise} 操作结果
   */
  editWebPush(deviceId, pushUrl, context = '', isAuth = '0', userName = '', password = '') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onEditWebPush;
      this.sdk.callbacks.onEditWebPush = (result) => {
        this.sdk.callbacks.onEditWebPush = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.editWebPush(requestId, loginHandle, deviceId, pushUrl, context, isAuth, userName, password);
      } catch (error) {
        this.sdk.callbacks.onEditWebPush = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 设备额外信息接口 ====================

  /**
   * 获取设备额外信息（设备坐标）
   * @param {number} deviceId - 设备ID，0表示获取所有设备
   * @returns {Promise} 设备额外信息
   */
  getDeviceExtra(deviceId = 0) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetDeviceExtra;
      this.sdk.callbacks.onGetDeviceExtra = (result) => {
        this.sdk.callbacks.onGetDeviceExtra = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getDeviceExtra(requestId, loginHandle, deviceId);
      } catch (error) {
        this.sdk.callbacks.onGetDeviceExtra = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 设置设备额外信息（设备坐标）
   * @param {number} deviceId - 设备ID
   * @param {string} location - 坐标，格式为：经度_纬度，如：118.6691_36.1341
   * @param {string} contact - 联系人名称
   * @param {string} phone - 联系电话
   * @param {string} remark - 备注
   * @returns {Promise} 操作结果
   */
  setDeviceExtra(deviceId, location = '', contact = '', phone = '', remark = '') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSetDeviceExtra;
      this.sdk.callbacks.onSetDeviceExtra = (result) => {
        this.sdk.callbacks.onSetDeviceExtra = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.setDeviceExtra(requestId, loginHandle, deviceId, location, contact, phone, remark);
      } catch (error) {
        this.sdk.callbacks.onSetDeviceExtra = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 设备音量接口 ====================

  /**
   * 获取设备音量
   * @param {number} deviceId - 设备ID
   * @param {string} type - 音量类型（input: 麦克风音量，output：喇叭音量）
   * @returns {Promise} 设备音量
   */
  getDeviceVolume(deviceId, type) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetDeviceVolume;
      this.sdk.callbacks.onGetDeviceVolume = (result) => {
        this.sdk.callbacks.onGetDeviceVolume = originalCallback;
        if (result.reqData && result.reqData.id === requestId) {
          resolve(result);
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getDeviceVolume(requestId, loginHandle, deviceId, type);
      } catch (error) {
        this.sdk.callbacks.onGetDeviceVolume = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 设置设备音量
   * @param {number} deviceId - 设备ID
   * @param {string} type - 音量类型（input: 麦克风音量，output：喇叭音量）
   * @param {number[]} volume - 设备音量，数组类型，每个音频通道一个配置（暂只支持单通道）
   * @returns {Promise} 操作结果
   */
  setDeviceVolume(deviceId, type, volume) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSetDeviceVolume;
      this.sdk.callbacks.onSetDeviceVolume = (result) => {
        this.sdk.callbacks.onSetDeviceVolume = originalCallback;
        if (result.reqData && result.reqData.id === requestId) {
          resolve(result);
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.setDeviceVolume(requestId, loginHandle, deviceId, type, volume);
      } catch (error) {
        this.sdk.callbacks.onSetDeviceVolume = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 获取广播音量
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 广播音量
   */
  getBroadcastVolume(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetBroadcastVolume;
      this.sdk.callbacks.onGetBroadcastVolume = (result) => {
        this.sdk.callbacks.onGetBroadcastVolume = originalCallback;
        if (result.reqData && result.reqData.id === requestId) {
          resolve(result);
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getBroadcastVolume(requestId, loginHandle, deviceId);
      } catch (error) {
        this.sdk.callbacks.onGetBroadcastVolume = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 设置广播音量
   * @param {number} deviceId - 设备ID
   * @param {number[]} volume - 设备音量，数组类型，每个音频通道一个配置（暂只支持单通道）
   * @returns {Promise} 操作结果
   */
  setBroadcastVolume(deviceId, volume) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSetBroadcastVolume;
      this.sdk.callbacks.onSetBroadcastVolume = (result) => {
        this.sdk.callbacks.onSetBroadcastVolume = originalCallback;
        if (result.reqData && result.reqData.id === requestId) {
          resolve(result);
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.setDeviceVolume(requestId, loginHandle, deviceId, volume);
      } catch (error) {
        this.sdk.callbacks.onSetBroadcastVolume = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 录像相关接口 ====================

  /**
   * 获取录像列表
   * @param {number} deviceId - 设备ID
   * @param {number} begintime - 录像开始时间
   * @param {number} endtime - 录像结束时间
   * @returns {Promise} 录像列表
   */
  getRecordList(deviceId, begintime = 0, endtime = 0) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetRecordList;
      this.sdk.callbacks.onGetRecordList = (result) => {
        this.sdk.callbacks.onGetRecordList = originalCallback;
        if (result.params && result.params.deviceId === deviceId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getRecordList(loginHandle, deviceId, begintime, endtime);
      } catch (error) {
        this.sdk.callbacks.onGetRecordList = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 播放录像
   * @param {number} deviceId - 设备ID
   * @param {number} begintime - 录像开始时间
   * @param {Object} video - 播放窗口对象
   * @returns {Promise} 操作结果
   */
  playBack(deviceId, begintime, video) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      try {
        this.sdk.dhWeb.playBack(loginHandle, deviceId, begintime, video);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== 二维码下发接口 ====================

  /**
   * 二维码下发
   * @param {Object} qrConfig - 二维码配置
   * @returns {Promise} 操作结果
   */
  configPayStatus(qrConfig) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onConfigPayStatus;
      this.sdk.callbacks.onConfigPayStatus = (result) => {
        this.sdk.callbacks.onConfigPayStatus = originalCallback;
        if (result.reqData && JSON.parse(result.reqData).id === requestId) {
          resolve(result);
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        const {
          deviceId,
          status,
          divCode,
          orgCode,
          carNumber = '',
          carType,
          qrText,
          parkingTime,
          validDate,
          cost
        } = qrConfig;

        this.sdk.dhWeb.configPayStatus(
          requestId,
          loginHandle,
          deviceId,
          status,
          divCode,
          orgCode,
          carNumber,
          carType,
          qrText,
          parkingTime,
          validDate,
          cost
        );
      } catch (error) {
        this.sdk.callbacks.onConfigPayStatus = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 批量控制DO接口 ====================

  /**
   * 批量控制DO
   * @param {number[]} cameraIds - 设备ID数组
   * @param {number} index - 数字输出的索引号，目前支持1和2
   * @returns {Promise} 操作结果
   */
  openDo(cameraIds, index) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onOpenDo;
      this.sdk.callbacks.onOpenDo = (result) => {
        this.sdk.callbacks.onOpenDo = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.openDo(requestId, cameraIds, index);
      } catch (error) {
        this.sdk.callbacks.onOpenDo = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 设备配置信息接口 ====================

  /**
   * 获取设备配置信息
   * @param {number[]} deviceIds - 设备ID数组
   * @returns {Promise} 设备配置信息
   */
  getCameraInfo(deviceIds) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetCameraInfo;
      this.sdk.callbacks.onGetCameraInfo = (result) => {
        this.sdk.callbacks.onGetCameraInfo = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getCameraInfo(requestId, deviceIds);
      } catch (error) {
        this.sdk.callbacks.onGetCameraInfo = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 删除设备
   * @param {number[]} deviceIds - 设备ID数组
   * @returns {Promise} 操作结果
   */
  deleteDevice(deviceIds) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onDeleteDevice;
      this.sdk.callbacks.onDeleteDevice = (result) => {
        this.sdk.callbacks.onDeleteDevice = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.deleteDevice(requestId, deviceIds);
      } catch (error) {
        this.sdk.callbacks.onDeleteDevice = originalCallback;
        reject(error);
      }
    });
  }
}

// 创建单例实例
const deviceManager = new DeviceManager();

export default deviceManager;
