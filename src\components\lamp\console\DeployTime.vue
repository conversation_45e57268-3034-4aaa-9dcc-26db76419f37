<template>
  <div class="deploytimebox">
    <div class="timebox-left">
        <div class="title-left">亮度控制</div>
        <div class="slider-box">
            <el-slider @change="sliderChange" v-model="props.item.brightness" vertical height="580px" />
        </div>
    </div>
    <div class="timebox-right">
        <div class="add-btn-box">
            <el-button type="primary" icon="Plus" @click="dialogDeploy = true">新 增</el-button>
        </div>
        <div class="calendar-box">
            <div class="calendar-item">
                <el-calendar v-model="calendar1">
                    <template #header="{ date }">
                        <span>{{ date }}</span>
                    </template>
                    <template #date-cell="{ data }">
                        <div class="day-style" :class="bgfu(1,data)?'day-bg' : ''" style="font-size: 12px;" >
                            {{ data.day.split('-').slice(2).join('-') }}
                        </div>
                    </template>
                </el-calendar>
            </div>
            <div class="calendar-item">
                <el-calendar v-model="calendar2">
                    <template #header="{ date }">
                        <span>{{ date }}</span>
                    </template>
                    <template #date-cell="{ data }">
                    <div class="day-style" style="font-size: 12px;"  :class="bgfu(2,data)?'day-bg' : ''">
                        {{ data.day.split('-').slice(2).join('-') }}
                    </div>
                    </template>
                </el-calendar>
            </div>
            <div class="calendar-item">
                <el-calendar v-model="calendar3">
                    <template #header="{ date }">
                        <span>{{ date }}</span>
                    </template>
                    <template #date-cell="{ data }">
                    <div class="day-style" style="font-size: 12px;" :class="bgfu(3,data)?'day-bg' : ''">
                        {{ data.day.split('-').slice(2).join('-') }}
                    </div>
                    </template>
                </el-calendar>
            </div>
            <div class="calendar-item">
                <el-calendar v-model="calendar4">
                    <template #header="{ date }">
                        <span>{{ date }}</span>
                    </template>
                    <template #date-cell="{ data }">
                    <div class="day-style" style="font-size: 12px;" :class="bgfu(4,data)?'day-bg' : ''">
                        {{ data.day.split('-').slice(2).join('-') }}
                    </div>
                    </template>
                </el-calendar>
            </div>
            <div class="calendar-item">
                <el-calendar v-model="calendar5">
                    <template #header="{ date }">
                        <span>{{ date }}</span>
                    </template>
                    <template #date-cell="{ data }">
                    <div class="day-style" style="font-size: 12px;" :class="bgfu(5,data)?'day-bg' : ''">
                        {{ data.day.split('-').slice(2).join('-') }}
                    </div>
                    </template>
                </el-calendar>
            </div>
        </div>
        <div class="table-box">
            <el-table :data="tableData" border v-if="tableData.length" stripe style="width: 100%">
                <el-table-column prop="executionTime" label="执行时间" />
                <el-table-column prop="lampsLanterns" label="执行单灯" />
                <el-table-column prop="policyTypeName" label="策略类型" />
                <el-table-column prop="executionStatusName" label="执行状态" />
                <el-table-column prop="policyStatusName" label="策略状态" >
                  <template #default="data">
                    <el-switch 
                      v-model="data.row.policyStatus"
                      :active-value="1"
                      :inactive-value="2"
                      @change="policyStatusChange(data.row)" />
                  </template>
                </el-table-column>
                <!-- 操作栏 -->
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                    <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
  </div>

  <el-dialog
    v-model="dialogDeploy"
    title="新增时间控制"
    width="80%"
    :before-close="handleClose">
    <div class="deploy-box">
        <el-form
          ref="ruleFormRef"
          style="width: 80%; height: 80%;"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="策略类型：" prop="policyType">
            <el-select
                v-model="ruleForm.policyType"
                @change="policyTypeChange"
                placeholder="请选择"
                style="width: 100%">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
            </el-select>
            <!-- <el-input class="inputStyles" v-model="" /> -->
          </el-form-item>
          <el-form-item label="执行亮度：" prop="executionBrightness">
            <el-slider v-model="ruleForm.executionBrightness" />
          </el-form-item>
          <el-form-item label="灯具执行状态：" prop="executionStatus">
            <el-switch 
            v-model="ruleForm.executionStatus"
            :active-value="1"
            :inactive-value="2" />
          </el-form-item>
          <el-form-item v-if="ruleForm.policyType === 2" label="执行策略：" prop="policyInfo">
            <el-checkbox-group v-model="ruleForm.policyInfo">
              <el-checkbox :value="item" name="type" v-for="item in 7" :key="item">
                {{ item === 1 ? '周一' : item === 2 ? '周二' : item === 3 ? '周三' : item === 4 ? '周四' : item === 5 ? '周五' : item === 6 ? '周六' : '周日' }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="ruleForm.policyType === 3" label="执行日：" prop="policyInfo">
            <el-checkbox-group v-model="ruleForm.policyInfo">
              <el-checkbox :value="item" name="type" v-for="item in 31" :key="item">
                  {{ item<10 ? '0'+item : item }}日
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="ruleForm.policyType === 4" label="执行日：" prop="policyInfo">
            <el-date-picker
              v-model="ruleForm.policyInfo"
              style="width: 100%;"
              type="date"
              placeholder="请选择日期"
            />
          </el-form-item>
          <el-form-item label="执行时间：" prop="executionTime">
            <el-time-picker 
              style="width: 100%;"  
              v-model="ruleForm.executionTime" 
              placeholder="请选择执行时间"
              format="HH:mm"
              value-format="HH:mm"/>
          </el-form-item>
        </el-form>
        <div class="btn-box">
            <el-button  type="primary" @click="submitForm(ruleFormRef)"> 提交 </el-button>
        </div>
    </div>
  </el-dialog>

  <el-dialog
    v-model="dialogView"
    title="新增时间控制"
    width="80%"
    :before-close="handleViewClose">
    <div class="deploy-box">
        <el-form
          ref="ruleFormRef"
          style="width: 80%; height: 80%;"
          :model="ruleForms"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="策略类型：" prop="policyType">
            <el-select
                v-model="ruleForms.policyType"
                placeholder="请选择"
                style="width: 100%">
                <el-option
                    v-for="item in options"
                    :disabled="true"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
            </el-select>
            <!-- <el-input class="inputStyles" v-model="" /> -->
          </el-form-item>
          <el-form-item label="执行亮度：" prop="executionBrightness">
            <el-slider :disabled="true" v-model="ruleForms.executionBrightness" />
          </el-form-item>
          <el-form-item label="灯具执行状态：" prop="executionStatus">
            <el-switch 
            v-model="ruleForms.executionStatus"
            :disabled="true"
            :active-value="1"
            :inactive-value="2" />
          </el-form-item>
          <el-form-item v-if="ruleForms.policyType === 2" label="执行策略：" prop="policyInfo">
            <el-checkbox-group v-model="ruleForms.policyInfos">
              <el-checkbox :disabled="true" :value="item" name="type" v-for="item in 7" :key="item">
                {{ item === 1 ? '周一' : item === 2 ? '周二' : item === 3 ? '周三' : item === 4 ? '周四' : item === 5 ? '周五' : item === 6 ? '周六' : '周日' }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="ruleForms.policyType === 3" label="执行日：" prop="policyInfo">
            <el-checkbox-group v-model="ruleForms.policyInfos">
              <el-checkbox :disabled="true" :value="item" name="type" v-for="item in 31" :key="item">
                  {{ item<10 ? '0'+item : item }}日
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="ruleForms.policyType === 4" label="执行日：" prop="policyInfo">
            <el-date-picker
              v-model="ruleForms.policyInfos"
              :disabled="true"
              style="width: 100%;"
              type="date"
              placeholder="请选择日期"
            />
          </el-form-item>
          <el-form-item label="执行时间：" prop="executionTime">
            <el-time-picker 
              style="width: 100%;"  
              v-model="ruleForms.executionTime" 
              :disabled="true"
              placeholder="请选择执行时间"
              format="HH:mm"
              value-format="HH:mm"/>
          </el-form-item>
        </el-form>
    </div>
  </el-dialog>

</template>

<script setup>
import { ref, reactive, onMounted, defineProps } from 'vue'
import { getpeizTimeData, add, chargeStatus, StrategyRemove, singleLampSwitch } from "@/api/lamp/console.js"

const options = [
  {
    value: 1,
    label: '每天',
  },
  {
    value: 2,
    label: '每周',
  },
  {
    value: 3,
    label: '每月',
  },
  {
    value: 4,
    label: '单次',
  }
]

const ruleForm = ref({})

const rules = reactive({
  executionBrightness: [
    { required: true, message: "请选择执行亮度", trigger: "change" },
  ],
  executionStatus: [
    { required: false, message: "请选择执行状态", trigger: "change" },
  ],
  policyType: [
    { required: true, message: "请选择执行策略类型", trigger: "change" },
  ],
  policyInfo: [
    { required: true, message: "请选择执行策略", trigger: "change" },
  ],
  executionTime: [
    { required: true, message: "请选择执行时间", trigger: "change" },
  ]
});

const policyTypeChange = (val) => {
  ruleForm.value = {}
  ruleForm.value.policyType = val
}

const ruleFormRef = ref(null)
const submitForm = async (formEl) => {
  console.log("submit!",formEl);
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if(ruleForm.value.policyType === 2 || ruleForm.value.policyType === 3){
        let data = ''
        ruleForm.value.policyInfo.forEach((item,index)=>{
          if(index== 0){
            data = `${item}`
          }else{
            data = `${data},${item}`
          }
        })
        ruleForm.value.policyInfo = data
      }
      ruleForm.value.executionLampsLanterns = props.item.luminaireNo

      console.log("submit!",ruleForm.value);
      addFun()
    } else {
      console.log("error submit!", fields);
    }
  });
};

const addFun = ()=>{
  add(ruleForm.value).then(res=>{
    dialogDeploy.value = false
    getData()
    ruleForm.value = {}
  }).catch(error=>{
    dialogDeploy.value = false
    getData()
    ruleForm.value = {}
  })
}

const dialogDeploy = ref(false)

const calendar1 = ref(new Date());
const calendar2 = ref(new Date(calendar1.value.getTime()));
calendar2.value.setMonth(calendar2.value.getMonth() + 1);
const calendar3 = ref(new Date(calendar1.value.getTime()));
calendar3.value.setMonth(calendar3.value.getMonth() + 2);
const calendar4 = ref(new Date(calendar1.value.getTime()));
calendar4.value.setMonth(calendar4.value.getMonth() + 3);
const calendar5 = ref(new Date(calendar1.value.getTime()));
calendar5.value.setMonth(calendar5.value.getMonth() + 4);




const sliderValue = ref(50)

const tableData = ref([ ])

const dayList = ref([])

const bgfu = (num,data) => {
    if (num === 1) {
        return dayList.value.find(item => item === data.day)
    }else if(num === 2){
        return dayList.value.find(item => item === data.day)
    }else if(num === 3){
        return dayList.value.find(item => item === data.day)
    }else if(num === 4){
        return dayList.value.find(item => item === data.day)
    }else if(num === 5){
        return dayList.value.find(item => item === data.day)
    }
}


const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})
const getData=()=>{
  console.log(props.item,"props.item")

  let row = {
    executionLampsLanterns: props.item.luminaireNo
  }
  getpeizTimeData(row).then(res=>{
      console.log(res.data.data)
      dayList.value = res.data.data[0].dates
      tableData.value = res.data.data
      console.log(tableData.value,"tableData.value")
  })
}


// 策略状态
const policyStatusChange = (data)=>{
  console.log(data,"data")
  let row = {
    id: data.id,
    status: data.policyStatus
  }
  chargeStatus(row).then(res=>{
    console.log(res)
  })
}

// 策略删除
const handleDelete = (data)=>{
  StrategyRemove({id:data.id}).then(res=>{
    console.log(res)
    getData()
  })
}

const dialogView = ref(false)
const ruleForms = ref({})
// 查看策略
const handleView = (data)=>{
  console.log(data,"data")
  ruleForms.value = data
  let arryData = []
  if(ruleForms.value.policyType === 2 || ruleForms.value.policyType === 3){
    arryData = ruleForms.value.policyInfo.split(",").map(Number);
    // ruleForms.value.policyInfo = ruleForms.value.policyInfo.split(",").map(Number);
    ruleForms.value.policyInfos = arryData
  }
  console.log(ruleForms.value.policyInfo,"ruleForms.value")
  dialogView.value = true
}

// 关闭查看窗口
const handleViewClose = () =>{
  dialogView.value = false
  ruleForms.value = {}
}

// 调整亮度
const sliderChange = (val)=>{
  singleLampSwitch({
    luminaireNo: props.item.luminaireNo,
    brightness: val
  }).then(res=>{
    console.log(res)
  })
}

onMounted(()=>{
  getData()
})



</script>
<style lang="scss" scoped>
.deploytimebox{
    width: 100%;
    height: 660px;
    display: flex;
    border: 1px solid #ccc;
}
.timebox-left{
    width: 20%;
    height: 100%;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    border-right: 1px solid #ccc;
}
.title-left{
    width: 100%;
}

.timebox-right{
    width: 80%;
    padding: 16px;
    display: flex;
    flex-direction: column;
}

.add-btn-box{
    margin-bottom: 24px;
}

.calendar-box{
    width: 100%;
    height: 40%;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-gap: 16px;
    .calendar-item{
        min-height: 276px;
        background-color: #f5f5f5;
    }
}




.table-box{
    flex: 1;
    padding: 16px 0;
    margin-top: 16px;
    // background-color: aquamarine;
}

.day-style{
    width: 100%;
    height: 24px;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}


:deep(.el-calendar__body){
    padding: 12px 20px 26px;
}

:deep(.el-calendar-table .el-calendar-day){
    height: 24px;
    padding: 0;
}

:deep(.el-calendar){
    background-color: #f5f5f5;
}

.day-bg{
    background-color: #409eff;
    color: #fff;
}

.deploy-box{
    width: 100%;
    height: 660px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.inputStyles{
    width: 100%;
}
</style>