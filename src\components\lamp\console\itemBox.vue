<template>
  <div class="itemBox">
    <div class="top-box">
      <div class="icon-title">
        <img style="width: 40px; height: 40px;" src="@/assets/login/login-bg.png" alt=""></img>
        <div class="title">{{ props.item.luminaireName }}</div>
      </div>
      <div class="text-box">
        <div class="position">
          <span>位置：</span>
          <div class="fontsizestyle">{{ props.item.address }}</div>
        </div>
        <div class="number-box">
          <span>编号：</span>
          <div class="fontsizestyles">{{  props.item.luminaireNo }}</div>
        </div>
      </div>
    </div>
    <div class="btn-box">
      <img style="width: 100%; height: 100%;" src="@/assets/lightpole.png" alt="">
    </div>
    <div class="totalBtn-box">
      <div class="total-right" style="margin-top: 6px;">
        <div style="margin-right: 10px; font-size: 12px;"> 单灯状态 </div>
        <div class="status-style" v-if="props.item.status === 1"></div>
        <div class="status-styles" v-if="props.item.status === 2"></div>
      </div>
      <div class="total-right">
        <div style="margin-right: 10px; font-size: 12px;"> 单灯状态 </div>
        <el-switch 
          v-model="props.item.status" 
          @change="changeSingleLamp($event,props.item)"
          :active-value="1"
          :inactive-value="2"/>
      </div>
      <div class="total-right">
        <div style="margin-right: 10px; font-size: 12px;"> 时间控制 </div>
        <el-button type="primary" round size="small" @click="dialogtime = true"> 配置 </el-button>
      </div>
    </div>
    <div class="item-edit">
      <el-icon style="color: red;margin-right: 6px;" @click="deletelItem(item)"><Delete /></el-icon>
      <!-- <el-icon style="color: #409eff;" @click="editlightpole(item)"><Edit /></el-icon> -->
    </div>
  </div>
  <el-dialog
    v-model="dialogtime"
    title="时间控制配置"
    width="92%"
    :before-close="handleClose">
    <DeployTime :item="props.item" v-if="dialogtime"></DeployTime>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import DeployTime from './DeployTime.vue'
import {  singleLampSwitch, remove } from '@/api/lamp/console.js'
import { ElMessage, ElMessageBox } from 'element-plus'



const dialogtime = ref(false)
const total = ref(false)

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['initialization'])
const changeSingleLamp = (val,data) => {
  let row = {
    luminaireNo:data.luminaireNo,
    status: val,
  }
  singleLampSwitch(row).then(res => {
    console.log(res)
    emit('initialization')
  }).catch(err => {
    emit('initialization')
    console.log(err)
  })
}

const deletelItem = (item) => {
  console.log(item)
  ElMessageBox.confirm(
    '该操作将删除该单灯，是否继续？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
      remove({id:item.id}).then(res => {
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
        emit('initialization')
      }).catch(error=>{
        console.log(error)
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: 'Delete canceled',
      })
    })
}

</script>
<style lang="scss" scoped>

.itemBox{
    height: 366px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.icon-title{
  display: flex;
  align-items: center;
  .title{
    width: calc(100% - 40px);
    text-align: center;
    color: #fff;
    user-select: none;
  }
}

.top-box{
  padding: 16px 16px 24px;
  background-color: #0d60a6;
}


.text-box{
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  font-size: 12px;
  .position{
    color: #FFF;
    display: flex;
    user-select: none;
  }
  .number-box{
    color: #FFF;
    display: flex;
    user-select: none;
  }

}

.fontsizestyle{
  font-size: 12px;
  width: 94px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  user-select: none;
}

.fontsizestyles{
  font-size: 12px;
  width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  user-select: none;
}

.btn-box{
  width: 100%;
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  img{

  }
}


.totalBtn-box{
  width: 100%;
  // height: 44px;
  background-color: #FFF;
  .total-right{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 6px;
  }
}

.status-style{
  width: 20px;
  height: 20px; 
  border-radius: 50%;
  background-color: #63a103;
}

.status-styles{
  width: 20px;
  height: 20px; 
  border-radius: 50%;
  border: 1px solid #333;
  background-color: #FFF;
}

.item-edit{
  position: absolute;
  top: 6px;
  right: 6px;
  cursor: pointer;
}

</style>