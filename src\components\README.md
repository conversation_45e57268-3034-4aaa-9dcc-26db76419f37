# FileUploadDialog 组件

文件上传弹窗组件，用于选择和上传音频文件。

## 功能特性

- 🎵 TTS文本转语音
- 🎤 录音文件上传
- 📁 本地文件上传
- 📋 文件选择列表
- ✅ 已选文件管理

## 使用方法

### 基本用法

```vue
<template>
  <FileUploadDialog
    v-model="visible"
    :available-files="availableFiles"
    @confirm="handleConfirm"
    @close="handleClose"
  />
</template>

<script setup>
import { ref } from 'vue'
import FileUploadDialog from '@/components/FileUploadDialog.vue'

const visible = ref(false)
const availableFiles = ref([
  { id: 1, name: '文件1.mp3', duration: '00:02:30' },
  { id: 2, name: '文件2.mp3', duration: '00:01:45' }
])

const handleConfirm = (selectedFiles) => {
  console.log('选中的文件:', selectedFiles)
}

const handleClose = () => {
  console.log('弹窗关闭')
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Boolean | false | 控制弹窗显示/隐藏 |
| availableFiles | Array | [] | 可选择的文件列表 |

### availableFiles 数据格式

```javascript
[
  {
    id: 1,           // 文件ID
    name: '文件名.mp3', // 文件名称
    duration: '00:02:30' // 文件时长
  }
]
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | Boolean | 弹窗显示状态变化 |
| confirm | Array | 确认选择文件，返回选中的文件列表 |
| close | - | 弹窗关闭事件 |

## 组件内部功能

### TTS转换
- 输入TTS标题和内容
- 支持试听功能
- 一键上传生成音频文件

### 录音上传
- 输入录音文件名称
- 支持试听功能
- 上传录音文件

### 本地文件上传
- 支持多文件选择
- 自动识别音频格式
- 显示文件信息

### 文件选择
- 从预设文件列表中选择
- 避免重复选择
- 实时更新已选列表

## 样式定制

组件使用 scoped 样式，如需定制可通过以下方式：

```vue
<style>
.file-upload-dialog {
  /* 自定义样式 */
}
</style>
```

## 注意事项

1. 确保传入的 `availableFiles` 数据格式正确
2. 文件ID应该是唯一的
3. 组件内部会自动去重，避免选择重复文件
4. 确认时至少需要选择一个文件
