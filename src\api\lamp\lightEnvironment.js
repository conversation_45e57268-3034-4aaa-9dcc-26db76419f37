import request from '@/axios';

export const getList = (current, size, params) =>{
    return request({
      url: '/hztech-light/environmentAcquisition/page',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
}

// 新增
export const add = (row) => {
  return request({
      url: '/hztech-light/environmentAcquisition/save',
      method: 'post',
      data: row
  })
}

export const remove = (id) => {
  return request({
      url: '/hztech-light/environmentAcquisition/remove',
      method: 'post',
      params: {
        id,
      }
  })
}

export const chargeStatus = (row) => {
  return request({
      url: '/hztech-light/environmentAcquisition/chargeStatus',
      method: 'post',
      data: row
  })
}

// 修改回路状态
export const SingleLamp = ( params ) =>{
    return request({
      url: '/hztech-light/lampsLanterns/singleLampSwitch',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 修改all回路状态
export const SingallleLamp = ( params ) =>{
    return request({
      url: '/hztech-light/loop/controlAlleLamp',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 获取设备型号
export const ControllerType = ( params ) =>{
  return request({
    url: '/hztech-light/centralizedController/centralizedControllerType',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 获取集中控制器回路列表
export const getForLoop = (current, size, params) =>{
  return request({
    url: '/hztech-light/loop/getLampsLanternsForLoop',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取未绑定
export const QueryIndicator = (current, size, params) =>{
  return request({
    url: '/hztech-light/lampsLanterns/exampleQueryIndicator',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取已绑定
export const leLightCentral = ( params) =>{
  return request({
    url: '/hztech-light/lampsLanterns/getSingleLightCentral',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const saveleLightCentral = ( params) =>{
  return request({
    url: '/hztech-light/lightPole/savePoleCentral',
    method: 'post',
    params: {
      ...params
    }
  })
}


// 控制所有的单灯的亮度
export const zedleLampSwitch = ( params) =>{
  return request({
    url: '/hztech-light/centralizedController/singCentralizedSwitch',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 获取集中控制器回路列表灯杆
export const getStem = (current, size, params) =>{
  return request({
    url: '/hztech-light/loop/getLightPoleForLoop',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取未绑定
export const QueryIndicatorStem = (current, size, params) =>{
  return request({
    url: '/hztech-light/lightPole/exampleQueryIndicator',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取已绑定
export const leLightCentralStem = ( params) =>{
  return request({
    url: '/hztech-light/lightPole/getSingleLightCentral',
    method: 'get',
    params: {
      ...params
    }
  })
}


// 获取时间控制页面数据
export const getTimeData = ( params) =>{
  return request({
    url: '/hztech-light/environmentStrategy/page',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 新增时间策略
export const addTimeStrategy = (row) => {
  return request({
      url: '/hztech-light/environmentStrategy/save',
      method: 'post',
      data: row
  })
}

// 控制单个时间策略开关
export const controlStrategy = (row) => {
  return request({
      url: '/hztech-light/environmentStrategy/chargeStatus',
      method: 'get',
      params: {
        ...row
      }
  })
}

// 单个时间策略删除
export const removeTimeStrategy = (id) => {
  return request({
      url: '/hztech-light/environmentStrategy/remove',
      method: 'post',
      params: {
        id,
      }
  })
}


// 修改设备状态
export const chargeDeviceStatus = ( params ) =>{
  return request({
    url: '/hztech-light/environmentAcquisition/chargeStatus',
    method: 'post',
    params: {
      ...params
    }
  })
}


// 获取集中控制的回路
export const getRound = ( params ) =>{
  return request({
    url: '/hztech-light/loop/selectLoopForCentralizedNo',
    method: 'get',
    params: {
      ...params
    }
  })
}