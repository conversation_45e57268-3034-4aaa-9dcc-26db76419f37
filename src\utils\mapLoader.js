export function loadQQMap(key) {
  return new Promise((resolve, reject) => {
    if (window.qq && window.qq.maps) {
      resolve(window.qq.maps);
      return;
    }
    
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = `https://map.qq.com/api/js?v=2.exp&key=${key}&callback=initQQMap`;
    
    window.initQQMap = () => {
      resolve(window.qq.maps);
      delete window.initQQMap;
    };
    
    script.onerror = () => {
      reject(new Error('QQ Maps SDK loading failed'));
    };
    
    document.head.appendChild(script);
  });
} 