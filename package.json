{"name": "hztech-front-end", "version": "1.0.0", "scripts": {"dev": "vite --host", "prod": "vite --mode production", "build": "vite build", "build:prod": "vite build --mode production", "serve": "vite preview --host"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@saber/nf-design-base-elp": "^1.1.1", "@smallwei/avue": "^3.2.23", "adaptorjs": "^1.0.1", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "element-plus": "^2.3.1", "hzwl-easy-player": "^0.1.6", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "vite-plugin-mock": "^2.9.4", "vue": "^3.2.40", "vue-i18n": "^9.1.9", "vue-jsonp": "^2.1.0", "vue-router": "^4.2.4", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.2", "@vue/compiler-sfc": "^3.0.5", "prettier": "^2.8.7", "sass": "^1.37.5", "unplugin-auto-import": "^0.11.2", "vite": "^5.2.13", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.3.4", "vite-plugin-vue-setup-extend": "^0.4.0"}}