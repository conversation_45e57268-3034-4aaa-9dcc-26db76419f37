<template>
  <div class="container-bigbox">
    <div v-if="showSelect" style="width: 100%;">
      <div class="title">选择杆体</div>
      <div class="ganti-box">
          <div class="ganti-item" :class="activation === 1?'activation': ''" @click="activItem(1)">
            <img class="img-style" src="@/assets/lightpole.png" alt="">
          </div>
          <div class="ganti-item" :class="activation === 2?'activation': ''" @click="activItem(2)">
            <img class="img-style" src="@/assets/lightpole.png" alt="">
          </div>
          <div class="ganti-item" :class="activation === 3?'activation': ''" @click="activItem(3)">
            <img class="img-style" src="@/assets/lightpole.png" alt="">
          </div>
      </div>
    </div>
    <!-- 选择设备页面 -->
    <selectDevice 
      v-if="ShowselectDevice" 
      ref="selectDevice" 
      @activItem="deviceActivItem"
      :selectData="selectData"></selectDevice>
    <!-- 提交设备数据页面 -->
    <detailfrom ref="detailfromRef" v-if="showDetailfrom" :activItemData="activItemData"></detailfrom>
    <!-- 选择设备后的详情页 -->
    <detail 
      v-if="showDetail" 
      :activation="activation"></detail>
    <Devicedetail 
      v-if="showDeviceDetail" 
      :activItemData="activItemData"></Devicedetail>
    <Deploy 
      v-if="showDeploy" 
      ref="deploy" 
      :lampsLanterns="lampsLanterns"
      :activation="activation" 
      @coles="coles"
      @showSelectfun="showSelectfun"></Deploy>
    <div class="btn-box">
        <el-button color="#165DFF" v-if="showDeploy" @click="DeploySbumit">提交</el-button>
        <el-button color="#165DFF" v-else @click="next">下一步</el-button>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, defineEmits } from 'vue'
import detail from "./detail.vue"
import Deploy from "./Deploy.vue"
import selectDevice from "./select.vue"
import detailfrom from "./detailfrom.vue"
import Devicedetail from "./devicedetail.vue"
import { LampsLanternsType, add } from "@/api/lamp/index.js";
import { ElMessage, ElLoading } from 'element-plus'

const emit = defineEmits(['handleClose'])

const showSelect = ref(true);
const showDetail = ref(false);
const showDeploy = ref(false);
const showDetailfrom = ref(false);
const ShowselectDevice = ref(false);
const showDeviceDetail = ref(false);

const activation = ref(0);

const loading = ref(false);

const activItem = (index) => {
  activation.value = index;
}

const activItemData = ref({});

const deviceActivItem = (item) => {
  console.log("item",item);
  activItemData.value = item;
}

const selectData = ref({});
const showSelectfun = (bol) => {
  console.log(bol,"点击了！！！");
  
  if(bol === 'den'){
    LampsLanternsType().then(res=>{
      console.log(res);
      selectData.value = res.data.data;
      showDeploy.value = false;
      ShowselectDevice.value = true;
    })
  }
  if(bol === 'jiankou'){
    LampsLanternsType().then(res=>{
      console.log(res);
      selectData.value = res.data.data;
      showDeploy.value = false;
      ShowselectDevice.value = true;
    })
  }
};

const next = () => {
  if(showSelect.value){
    showDetail.value = true;
    showSelect.value = false;
  }else if(showDetail.value){
    showDeploy.value = true;
    showDetail.value = false;
  }else if(ShowselectDevice.value){
    showDeviceDetail.value = true;
    ShowselectDevice.value = false;
    return
  }else if(showDeviceDetail.value){
    showDetailfrom.value = true;
    showDeviceDetail.value = false;
    return
  }else if(showDetailfrom.value){
    console.log("showDetailfrom",showDetailfrom.value);
    detailfromSbumit()
  }
}

const detailfromRef = ref(null);
const lampsLanterns = ref({});
const detailfromSbumit = async () => {
  console.log("detailfromRef", detailfromRef.value);
  const result = await detailfromRef.value.detailSubmitForm();
  console.log("result", result);
  if (result.success) {
    // 处理成功情况
    console.log('表单提交成功，字段信息：', result.data);
    lampsLanterns.value = result
    showDeploy.value = true;
    showDetailfrom.value = false;

  } else {
    // 处理失败情况
    console.log('表单提交失败，字段信息：', result.data);
  }
};

const deploy = ref(null);
const DeploySbumit = async () => {
  if (!deploy.value) return;
  
  try {
    loading.value = true;
    const result = await deploy.value.submitForm();
    
    if (result.success) {
      const submitData = {
        poleName: result.data.poleName,
        poleAddress: result.data.poleAddress,
        lng: result.data.lng,
        lat: result.data.lat,
        icon: result.data.icon,
        remark: result.data.remark,
        modelNo: result.data.modelNo,
        poleNo: result.data.poleNo,
        lampsLanterns: lampsLanterns.value?.data ? JSON.stringify(lampsLanterns.value.data) : ''
      }
      
      await addFun(submitData);
    } else {
      ElMessage.error('表单验证失败，请检查输入');
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('提交失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

const addFun = async (row) => {
  try {
    const res = await add(row);
    console.log("res",res);
    if (res.data.code === 200) {
      ElMessage.success('添加成功');
      deploy.value.resetForm();
      coles();
    } else {
      ElMessage.error(res.message || '添加失败1');
    }
  } catch (err) {
    console.error('添加失败2:', err);
    ElMessage.error('添加失败，请稍后重试');
  }
}

const coles = () => {
  showDeploy.value = false;
  showDetail.value = true;
  emit('handleClose')
}

</script>
<style lang="scss" scoped>
.container-bigbox{
    width: 100%;
}

.title{
  margin-bottom: 24px;
}

.ganti-box{
    width: 100%;
    height: 453px;
    display: flex;
    justify-content: space-between;
}

.btn-box{
    width: 100%;
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
}

.ganti-item{
  width: 280px;
  height: 100%;
  border-radius: 8px;
  background-color: #F2F2F2;
  box-sizing: border-box;
  overflow: hidden;
  cursor: pointer;
  .img-style{
    width: 100%;
    height: 100%;
  }
}

.activation{
  border: 1px solid #165DFF;
}

</style>