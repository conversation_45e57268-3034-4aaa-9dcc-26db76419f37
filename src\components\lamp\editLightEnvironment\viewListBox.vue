<template>
  <div class="poleList">
    <div class="search-box">
        <el-input v-model="search.poleName" placeholder="请输入区域" style="width: 300px; margin-right: 24px;"></el-input>
        <el-input v-model="search.poleNo" placeholder="请输入编号" style="width: 300px; margin-right: 24px;"></el-input>
        <el-button type="primary" icon="Search" @click="searchList">搜索</el-button>
    </div>
    <el-table 
    :data="tableData"
    height="250" 
    border
    stripe
    style="width: 100%; height: 82%;">
        <el-table-column type="index" width="60" label="序号" :index="indexMethod" />
        <el-table-column
            prop="date" 
            label="单灯编号"
            show-overflow-tooltip/>
        <el-table-column
            prop="name"
            label="单灯名称" 
            show-overflow-tooltip/>
        <el-table-column
            prop="name"
            label="单灯位置" 
            show-overflow-tooltip/>
        <el-table-column
            prop="name"
            label="单灯状态" 
            show-overflow-tooltip/>
        <el-table-column
            prop="value"
            label="单灯亮度" 
            show-overflow-tooltip>
            <template #default="scope">
                <el-progress :text-inside="true" :stroke-width="18" :percentage="scope.row.value" />
            </template>
        </el-table-column>
    </el-table>
    <div class="paging-box">
      <el-pagination
        :page-size="page.pageSize"
        :size="page.currentPage"
        background
        layout="prev, pager, next"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const tableData = ref( [
  {
    date: 'K892812934',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812934',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812934',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812934',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812934',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
  {
    date: 'K892812935',
    name: 'Tom',
    value: '50'
  },
])

const page = ref(
  {
    pageSize: 10,
    currentPage: 1,
    total: 100,
  },
)
const search = ref({
  poleName: '',
  poleNo: ''
})
const handleCurrentChange=(val)=>{
  console.log(`current page: ${val}`)
//   getDATA(val, page.value.pageSize, {})
}
</script>
<style lang="scss" scoped>
.poleList{
    width: 100%;
    height: 560px;
}
</style>

<style lang="scss" scoped>
.search-box{
    margin-bottom: 16px;
}
.paging-box{
  display: flex;
  justify-content: flex-end;
  margin: 24px 0;
}
.menu-btn{
  position: fixed;
  right: 0;
  bottom: calc(50% - 130px);
  width: 52px;
  height: 262px;
  font-size: 20px;
  line-height: 52px;
  writing-mode: vertical-lr;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  cursor: pointer;
}


.el-pagination.number:not(.is-active) {
  background-color: #fff;
}
:deep( .btn-prev){
  background-color: #fff !important;
}
:deep(.btn-next){
  background-color: #fff !important;
}

:deep .el-pagination .el-pager li:not(.active):not(.disabled) {
  background-color: #fff !important;
  // color: #000;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #165DFF !important; //修改默认的背景色
  color: #FFF;
}
</style>