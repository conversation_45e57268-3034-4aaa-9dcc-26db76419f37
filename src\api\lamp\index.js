import request from '@/axios';

/**
 * 获取灯杆列表
 * 
 * 该函数用于分页查询灯杆信息，通过发送GET请求到指定的URL，
 * 并携带当前页码、页面大小以及额外的查询参数来获取数据
 * 
 * @param {number} current 当前页码，用于指定从哪一页开始查询
 * @param {number} size 页面大小，用于指定每页返回的记录数
 * @param {Object} params 额外的查询参数，用于进一步过滤查询结果
 * @returns {Promise} 返回一个Promise对象，包含请求的结果
 */
export const getlampList = (current, size, params) =>{
    return request({
      url: '/hztech-light/lightPole/page',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
}

export const getDetail = (id) => {
    return request({
      url: '/hztech-light/alarmDeviceDefense/detail',
      method: 'get',
      params: {
        id
      }
    })
  }

export const remove = (id) => {
    return request({
        url: '/hztech-light/lightPole/remove',
        method: 'get',
        params: {
          id,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/hztech-light/lightPole/save',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/hztech-light/lightPole/update',
        method: 'post',
        data: row
    })
}


// 获取单灯设备
export const LampsLanternsType = () => {
  return request({
      url: '/hztech-light/lampsLanterns/LampsLanternsType',
      method: 'get',
  })
}

// 获取监控设备
export const MonitorType = () => {
  return request({
    url: '/hztech-light/supervisoryControl/videoType',
    method: 'get',
  })
}

