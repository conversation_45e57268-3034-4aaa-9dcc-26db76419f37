<template>
  <div class="title">设备说明</div>
  <div class="detail">
    <div class="img-box">
        <img class="img-style" :src="props.activItemData.icon" alt="">
    </div>
    <div class="content-box">
      <p class="indent">
          {{ props.activItemData.info }}
        </p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'

let props = defineProps({
    activItemData: {
        type: Number,
    },
});

const ruleForm = ref({ });
const rules = ref({
  poleName: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 3, max: 5, message: "名称长度未3-5个字段", trigger: "blur" },
  ],
  poleNo: [
    { required: true, message: "请输入灯杆编号", trigger: "blur" },
  ],
  poleAddress: [
    { required: true, message: "请选择灯杆地址", trigger: "blur" },
  ],

});

const ruleFormRef = ref(null);
const submitForm = async () => {
  console.log("submit!",ruleFormRef.value);
  if (!ruleFormRef.value) return;
  await ruleFormRef.value.validate((valid, fields) => {
    console.log("submit!",valid, fields);
    if (valid) {
      ruleForm.value.modelNo = props.activation
      console.log("submit222222",valid, ruleForm.value);
      addFun(ruleForm.value)
    } else {
      console.log("error submit!", fields);
    }
  });
};


defineExpose({
  submitForm
});
</script>
<style lang="scss" scoped>
.detail{
    width: 100%;
    height: 460px;
    display: flex;
    justify-content: space-around;
}
.title{
    margin-bottom: 24px;
}
.img-box{
    width: 280px;
    height: 460px;
    background-color: #F2F2F2;
    .img-style{
        width: 100%;
        height: 100%;
    }
}
.content-box{
    width: 280px;
    height: 100%;
    overflow-y: auto;
    text-align: center;
}

.indent{
    text-indent: 32px;
    text-align: left;
}
</style>