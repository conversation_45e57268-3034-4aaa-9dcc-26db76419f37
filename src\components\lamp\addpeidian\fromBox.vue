<template>
  <div class="frombox">
    <div class="deviceicon">
        <img class="icon-style" :src="props.activItemData.icon" alt="">
    </div>
    <div class="fromright" v-show="showform">
        <el-form
            ref="ruleFormRef"
            style="max-width: 600px; min-height: 94%;"
            :model="ruleForm"
            :rules="rules"
            label-width="auto"
            class="demo-ruleForm"
            status-icon >
            <el-form-item label="控制器名称：" prop="deviceName">
                <el-input maxlength="20" class="inputStyles" v-model="ruleForm.deviceName" placeholder="请输入控制器名称" />
            </el-form-item>
            <el-form-item label="集中控制器编号：" prop="deviceNo">
                <el-input maxlength="20" class="inputStyles" v-model="ruleForm.deviceNo" placeholder="请输入灯杆编号" />
            </el-form-item>
            <el-form-item label="物联网ID：" prop="deviceId">
                <el-input maxlength="20" class="inputStyles" v-model="ruleForm.deviceId" placeholder="请输入物联网ID" />
            </el-form-item>
            <el-form-item label="集中控制器位置：" prop="address">
                <el-input class="inputStyles" v-model="ruleForm.address" @click="selectAddress" placeholder="请选择集中控制器位置" />
            </el-form-item>
            <el-form-item label="图标：" prop="icon">
              <el-upload
                class="avatar-uploader"
                action="/api/hztech-resource/oss/endpoint/put-file"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="ruleForm.icon" :src="ruleForm.icon" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item label="备注：" prop="remark">
                <el-input 
                    type="textarea"  
                    :autosize="{ minRows: 4, maxRows: 6 }"
                    class="inputStyles" 
                    placeholder="请输入备注"
                    maxlength="300"
                    v-model="ruleForm.remark" />
            </el-form-item>
        </el-form>
        <div class="btnbox">
            <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
        </div>
    </div>
    <Adress  v-if="showMap" @changeAddress="changeAddress"></Adress>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits} from 'vue'
import { getToken } from "@/utils/auth";
import { ElMessage } from 'element-plus'
import Adress from '../map/adress.vue';

const props = defineProps({
    activItemData:{
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['submit'])

let headers = ref({
    //上传图片headers
    "hztech-auth": "bearer " + getToken(),
})

const handleAvatarSuccess = (response, uploadFile)=>{
  ruleForm.value.icon = response.data.link;
};

const beforeAvatarUpload = (rawFile)=>{
  if (rawFile.type!== 'image/jpeg') {
    ElMessage.error('请上传image/jpeg格式图片');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片文件不能大于2M');
    return false;
  }
  return true;
};

const showform = ref(true);
const showMap = ref(false);

const ruleForm = ref({})


const selectAddress = () => {
  console.log('点击了');
  showform.value = false;
  showMap.value = true;
}
const changeAddress = (row) => {
  console.log('row', row);
  ruleForm.value.address = row.store_address;
  ruleForm.value.lat = row.lat;
  ruleForm.value.lng = row.lng;
  showform.value = true;
  showMap.value = false;
}

const rules = {
    deviceName: [
      { required: true, message: '请输入控制器名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
    ],
    deviceNo: [
      { required: true, message: '请输入集中控制器编号', trigger: 'blur' },
      { pattern: /^[A-Za-z0-9]+$/, message: '只能输入数字和字母', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
    ],
    deviceId: [
      { required: true, message: '请输入物联网ID', trigger: 'blur' },
      { pattern: /^[A-Za-z0-9]+$/, message: '只能输入数字和字母', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
    ],
    address: [
      { required: true, message: '请输入集中控制器位置', trigger: 'change'},
    ],
    icon: [
      { required: true, message: '请上传图标', trigger: 'blur' },
    ],
}

const ruleFormRef = ref(null);
const submitForm = async () => {
  console.log("submit!",ruleFormRef.value);
  let row ={}
  if (!ruleFormRef.value) return;
  await ruleFormRef.value.validate((valid, fields) => {
    console.log("submit!",valid, fields);
    if (valid) {
      ruleForm.value.type = props.activItemData.type
      console.log("submit222222",valid, ruleForm.value);
      row = { success: valid , data: ruleForm.value }
      emit('submit', row)
    } else {
      console.log("error submit!", fields);
      row = { success: valid , data: ruleForm.value }
      emit('submit', row)
    }
  });
};

onMounted(()=>{
  // ruleForm.value.deviceNo = props.activation
  console.log("props.activItemData",props.activItemData);
})

defineExpose({
  submitForm
});

</script>
<style lang="scss" scoped>

.frombox{
    width: 100%;
    height: 500px;
    display: flex;
}
.fromright{
    width: 50%;
    height: 100%;
    .btnbox{
        width: 100%;
        display: flex;
        justify-content: end;
    }
}
.deviceicon{
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    background-color: #F7F8FA;
    border-radius: 8px;
    // border: 1px solid #ccc;
    .icon-style{
        width: 80%;
        height: 80%;
    }
}

.inputStyles{
  width: 100%;
  height: 40px;
}

.avatar-uploader .avatar {
  width: 60px;
  height: 60px;
  display: block;
  border: 1px dashed #8c939d;
  border-radius: 6px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #8c939d;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 60px;
  height: 60px;
  text-align: center;
  border: 1px dashed #8c939d;
  border-radius: 6px;
}

:deep(.el-input) {
  .el-input__wrapper {
    background-color: #F7F8FA;
    border: none;
    box-shadow: none;
    padding: 12px;
    
    &.is-focus {
      box-shadow: none;
    }

    &.is-readonly {
      cursor: pointer;
    }
  }
  
  input {
    height: 20px;
    font-size: 14px;
    &::placeholder {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 350;
      line-height: normal;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #A2AAB3;
    }
  }
}
</style>