<template>
    <div class="left-box">
        <div class="left-box-ghj">
            <div class="title-box" style="margin-bottom: 28px;">光环境设备在线情况</div>
            <div class="ghj-number-box">
                <div class="ghj-number-item" style="margin-right: 79px;">
                    <img style="width: 131px; height: 76px;" src="@/assets/largescreen/ghj-zx.png" alt="">
                    <div class="zt-title">在线设备</div>
                    <div class="number">{{ lightOnline }}</div>
                </div>
                <div class="ghj-number-item">
                    <img style="width: 131px; height: 76px;" src="@/assets/largescreen/ghj-lx.png" alt="">
                    <div class="zt-title">离线设备</div>
                    <div class="number">{{ lightOffline }}</div>
                </div>
            </div>
        </div>
        <div class="left-box-jzkzq">
            <div class="title-box" >集中控制器设备在线情况</div>
            <div class="device-status">
                <div class="status-item">
                    <div class="status-label">设备在线数</div>
                    <div class="status-value">{{ controllerOnline }}</div>
                </div>
                <div class="progress-wrapper online-gradient">
                    <div class="progress-inner">
                        <el-progress 
                            :percentage="onlinePercentage" 
                            :show-text="false" 
                            :stroke-width="6"
                            :color="onlineColors"
                            class="custom-progress"
                        />
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">设备离线数</div>
                    <div class="status-value">{{ controllerOffline }}</div>
                </div>
                <div class="progress-wrapper offline-gradient">
                    <div class="progress-inner">
                        <el-progress 
                            :percentage="offlinePercentage" 
                            :show-text="false" 
                            :stroke-width="6"
                            :color="offlineColors"
                            class="custom-progress"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="left-box-qtcgq">
            <div class="title-box">气体传感器设备在线情况</div>
            <div class="qtcgq-number-box">
                <div class="qtcgq-number-item" style="margin-right: 117px;">
                    <img style="width: 93px; height: 64px;" src="@/assets/largescreen/qtcgq-zx.png" alt="">
                    <div class="number">{{ gasOnline }}</div>
                    <div class="zt-title qtcgq-before-zx" >在线设备</div>
                </div>
                <div class="qtcgq-number-item">
                    <img style="width: 93px; height: 64px;" src="@/assets/largescreen/qtcgq-lx.png" alt="">
                    <div class="number">{{ gasOffline }}</div>
                    <div class="zt-title qtcgq-before-lx">离线设备</div>
                </div>
            </div>
        </div>
    </div>
</template> 
<script setup> 
import { ref, computed, onMounted } from 'vue';
import { getLightOnline, getControllerOnline, getGasOnline } from '@/api/lamp/largeScreen';

// 光环境设备数据
const lightOnline = ref(0);
const lightOffline = ref(0);

// 集中控制器数据
const controllerOnline = ref(0);
const controllerOffline = ref(0);

// 气体传感器数据
const gasOnline = ref(0);
const gasOffline = ref(0);

// 计算总数
const totalControllers = computed(() => controllerOnline.value + controllerOffline.value);

// 计算百分比
const onlinePercentage = computed(() => {
    return totalControllers.value ? (controllerOnline.value / totalControllers.value) * 100 : 0;
});

const offlinePercentage = computed(() => {
    return totalControllers.value ? (controllerOffline.value / totalControllers.value) * 100 : 0;
});

// 设置进度条颜色
const onlineColors = [
    { color: '#23B1DD', percentage: 0 },
    { color: '#48D8F9', percentage: 100 }
];

const offlineColors = [
    { color: '#DD2323', percentage: 0 },
    { color: '#F94848', percentage: 100 }
];

// 获取光环境设备在线情况
const fetchLightOnline = async () => {
    try {
        const res = await getLightOnline();
        if (res.data.code === 200) {
            lightOnline.value = res.data.data.online || 0;
            lightOffline.value = res.data.data.offOnline || 0;
        }
    } catch (error) {
        console.error('获取光环境设备在线情况失败:', error);
    }
};

// 获取集中控制器在线情况
const fetchControllerOnline = async () => {
    try {
        const res = await getControllerOnline();
        if (res.data.code === 200) {
            controllerOnline.value = res.data.data.online || 0;
            controllerOffline.value = res.data.data.offOnline || 0;
        }
    } catch (error) {
        console.error('获取集中控制器在线情况失败:', error);
    }
};

// 获取气体传感器在线情况
const fetchGasOnline = async () => {
    try {
        const res = await getGasOnline();
        if (res.data.code === 200) {
            gasOnline.value = res.data.data.online || 0;
            gasOffline.value = res.data.data.offOnline || 0;
        }
    } catch (error) {
        console.error('获取气体传感器在线情况失败:', error);
    }
};

// 页面加载时获取数据
onMounted(() => {
    fetchLightOnline();
    fetchControllerOnline();
    fetchGasOnline();
});
</script>
<style scoped lang="scss">
*{
    box-sizing: border-box;
}
.left-box{
    width: 487px;
    height: calc(100% - 82px);
    position: absolute;
    top: 82px;
    left: 0;
    padding-top: 16px;
    padding-left: 36px;
}
.left-box-ghj{
    width: 451px;
    min-height: 273px;
    background: url(@/assets/largescreen/ghj-bg.png);
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 32px;
}
.left-box-jzkzq{
    width: 451px;
    min-height: 273px;
    background: url(@/assets/largescreen/jzkzq-bg.png);
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 32px;
}

.left-box-qtcgq{
    width: 451px;
    min-height: 315px;
    background: url(@/assets/largescreen/qtcgq-bg.png);
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 32px;
}

.ghj-number-box{
    width: 100%;
    height: 201px;
    display: flex;
    align-items: center;
    justify-content: center;
    .ghj-number-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
}

.qtcgq-number-box{
    width: 100%;
    height: 201px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 76px;
    .qtcgq-number-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
}

.qtcgq-before-zx::before{
    content: " ";
    display: block;
    width: 6px;
    height: 6px;
    background: #0759DA;
    margin-right: 4px;
}
.qtcgq-before-lx::before{
    content: " ";
    display: block;
    width: 6px;
    height: 6px;
    background: #D3D5DA;
    margin-right: 4px;
}

.zt-title{
    font-family: Source Han Sans;
    font-size: 13px;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFFFFF;
    display: flex;
    align-items: center;
}

.number{
    font-family: Source Han Sans;
    font-size: 20px;
    font-weight: 500;
    line-height: 25px;
    letter-spacing: 0px;
    color: #FFFFFF;
}

.title-box{
    width: 100%;
    padding-left: 21px;
    padding-top: 20px;
    display: flex;
    align-items: center;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    line-height: normal;
    text-align: center;
    letter-spacing: 0.11em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #FFFFFF;
}
.title-box::before{
    content: " ";
    display: block;
    width: 4px;
    height: 15px;
    background: #37AEE1;
    margin-right: 6px;
}

.device-status {
    padding: 60px 30px 0;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.status-label {
    font-family: Source Han Sans;
    font-size: 14px;
    color: #FFFFFF;
}

.status-value {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: bold;
    color: #FFFFFF;
}

.progress-wrapper {
    height: 18px;
    margin-bottom: 24px;
    border-radius: 10px;
    padding: 1px;
}

.online-gradient {
    background: linear-gradient(90deg, #37A6E0 0%, rgba(255, 255, 255, 0) 100%);
}

.offline-gradient {
    margin-bottom: 0;
    background: linear-gradient(90deg, #E03737 0%, rgba(224, 55, 55, 0) 100%);
}

.progress-inner {
    height: 100%;
    background: #0c1637;
    border-radius: 9px;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 0 3px;
}

.custom-progress {
    height: 12px;
    width: 100%;
}

:deep(.el-progress-bar__outer) {
    background-color: transparent;
    border-radius: 10px !important;
    height: 8px !important;
}

:deep(.el-progress-bar__inner) {
    border-radius: 10px !important;
}
</style>