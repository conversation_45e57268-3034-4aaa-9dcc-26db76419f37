<template>
    <!-- 腾讯地图 -->
    <section>
        <div id="container"></div>
        <!-- <span class="text-warning">&nbsp;可拖动放大</span> -->
    </section>
</template>

<script>
import { jsonp } from "vue-jsonp";
import { loadQQMap } from "@/utils/mapLoader";

export default {
    name: "Tx-Map",
    props: {},
    comments: {},
    data() {
        return {
            mapKey: "I75BZ-2Q4C7-LEIXE-HKERV-OWEES-5LF5I",
            maps: null,
            isMapLoaded: false
        };
    },
    async created() {
        try {
            this.maps = await loadQQMap(this.mapKey);
            this.isMapLoaded = true;
        } catch (error) {
            console.error('Failed to load QQ Maps:', error);
        }
    },
    watch: {},
    methods: {
        async getCurrent() {
            if (!this.isMapLoaded) {
                console.warn('Map not loaded yet');
                return;
            }
            let Current = new this.maps.Geolocation(
                this.mapKey,
                "报警管理总"
            );
            Current.getLocation(this.sucCallback, this.errCallback);
        },
        sucCallback(data) {
            console.log("成功了啊！", data);
            this.init(15,
                data.lat,
                data.lng,
                '杭州');
            jsonp(
                `https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${this.mapKey}&location=${data.lat},${data.lng}`
            )
                .then((res) => {
                    console.log(res);
                    if (!res.result) {
                        return
                    }
                    this.$emit("setAddress", res.result);
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        errCallback(data) {
            console.log("失败了啊！", data);
        },
        init(zoom = 8, lat = 39.911265, lng = 116.375212, storeAddress = '杭州') {
            if (!this.isMapLoaded) {
                console.warn('Map not loaded yet');
                return;
            }
            console.log("地图初始化-纬度--lat", lat);
            console.log("地图初始化-经度--lng", lng);
            let that = this;
            //步骤：定义map变量 调用 qq.maps.Map() 构造函数   获取地图显示容器
            //设置地图中心点
            var myLatlng = new this.maps.LatLng(lat, lng);
            //定义工厂模式函数
            var myOptions = {
                zoom, //设置地图缩放级别
                center: myLatlng, //设置中心点样式
                disableDefaultUI: true
            };
            //获取dom元素添加地图信息
            var map = new this.maps.Map(
                document.getElementById("container"),
                myOptions
            );

            var marker;
            if (storeAddress) {
                if (!marker) {
                    marker = new this.maps.Marker({
                        position: myLatlng,
                        draggable: true,
                        map: map,
                    });
                }
            }
            // 添加地图点击事件
            this.maps.event.addListener(map, "click", function (event) {
                jsonp(
                    `https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${that.mapKey}&location=${event.latLng.getLat()},${event.latLng.getLng()}`
                )
                    .then((res) => {
                        console.log(res);
                        if (!res.result) {
                            return
                        }
                        that.$emit("setAddress", res.result);
                        myLatlng = new that.maps.LatLng(
                            res.result.location.lat,
                            res.result.location.lng
                        );
                        marker.setMap(null); //清除地图的所有marker标点
                        marker = new that.maps.Marker({
                            position: myLatlng,
                            draggable: true,
                            map: map,
                        });
                    })
                    .catch(err => {
                        console.log(err);
                    })
            });
        },
    },
};
</script>

<style lang="scss" scoped>
#container {
    width: 100%;
    min-height: 400px;
    display: inline-block;
    // 容器可拖放
    resize: both;
    overflow: auto;
}
</style>