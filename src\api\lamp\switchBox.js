import request from '@/axios';

export const getList = (current, size, params) =>{
    return request({
      url: '/hztech-light/centralizedController/page',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
}

// 新增
export const add = (row) => {
  return request({
      url: '/hztech-light/centralizedController/save',
      method: 'post',
      data: row
  })
}

export const remove = (id) => {
  return request({
      url: '/hztech-light/centralizedController/remove',
      method: 'post',
      params: {
        id,
      }
  })
}

// 修改回路状态
export const SingleLamp = ( params ) =>{
    return request({
      url: '/hztech-light/lampsLanterns/singleLampSwitch',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 修改all回路状态
export const SingallleLamp = ( params ) =>{
    return request({
      url: '/hztech-light/loop/controlAlleLamp',
      method: 'get',
      params: {
        ...params
      }
    })
}

// 获取设备型号
export const ControllerType = ( params ) =>{
  return request({
    url: '/hztech-light/centralizedController/centralizedControllerType',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 获取集中控制器回路列表
export const getForLoop = (current, size, params) =>{
  return request({
    url: '/hztech-light/loop/getLampsLanternsForLoop',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取未绑定
export const QueryIndicator = (current, size, params) =>{
  return request({
    url: '/hztech-light/lampsLanterns/exampleQueryIndicator',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取已绑定
export const leLightCentral = ( params) =>{
  return request({
    url: '/hztech-light/lampsLanterns/getSingleLightCentral',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const saveleLightCentral = ( params) =>{
  return request({
    url: '/hztech-light/lightPole/savePoleCentral',
    method: 'post',
    params: {
      ...params
    }
  })
}


// 控制所有的单灯的亮度
export const zedleLampSwitch = ( params) =>{
  return request({
    url: '/hztech-light/centralizedController/singCentralizedSwitch',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 获取集中控制器回路列表灯杆
export const getStem = (current, size, params) =>{
  return request({
    url: '/hztech-light/loop/getLightPoleForLoop',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取未绑定
export const QueryIndicatorStem = (current, size, params) =>{
  return request({
    url: '/hztech-light/lightPole/exampleQueryIndicator',
    method: 'get',
    params: {
      current,
      size,
      ...params
    }
  })
}

// 获取已绑定
export const leLightCentralStem = ( params) =>{
  return request({
    url: '/hztech-light/lightPole/getSingleLightCentral',
    method: 'get',
    params: {
      ...params
    }
  })
}

// 获取已绑定灯杆分页查询
export const leLightpage= ( params) =>{
  return request({
    url: '/hztech-light/lightPole/page',
    method: 'get',
    params: {
      ...params
    }
  })
}