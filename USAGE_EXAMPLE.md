# 实时广播功能使用示例

## 功能演示步骤

### 1. 文件广播示例

#### 步骤1: 选择文件广播模式
```javascript
// 用户点击"文件广播"卡片
// activeCard.value 会被设置为 '2'
```

#### 步骤2: 选择音频文件
```javascript
// 方式1: 从下拉列表选择预设文件
selectedFile.value = 'system' // 系统提示音.mp3
// 或
selectedFile.value = 'alarm'  // 警报声.wav

// 方式2: 上传本地文件
// 用户点击"选择本地文件"按钮
// 选择音频文件后，uploadedFile.value 会包含文件对象
// selectedFile.value 会显示文件名
```

#### 步骤3: 配置广播参数
```javascript
// 设置循环模式
loopMode.value = 'infinite' // 无限循环
// 或
loopMode.value = 'count'    // 指定次数
loopCount.value = 3         // 循环3次

// 设置广播音量
broadcastVolume.value = 75  // 音量75%
```

#### 步骤4: 选择广播设备
```javascript
// 用户在设备表格中勾选要广播的设备
// selectedDevices.value 会包含选中的设备列表
// 例如: [{ deviceId: 12345, deviceName: '设备1' }, { deviceId: 67890, deviceName: '设备2' }]
```

#### 步骤5: 开始文件广播
```javascript
// 用户点击播放按钮
// 系统会调用:
const deviceIds = [12345, 67890]
const broadcastType = 2 // 文件广播类型
const filePath = '/audio/system.mp3' // 或上传文件的路径

await chromeSDKManager.quickStartBroadcast(deviceIds, broadcastType, filePath)
```

### 2. 麦克风广播示例

#### 步骤1: 选择麦克风广播模式
```javascript
// 用户点击"麦克风广播"卡片
// activeCard.value 会被设置为 '1'
```

#### 步骤2: 配置麦克风音量
```javascript
// 设置麦克风音量
micVolume.value = 80  // 音量80%
```

#### 步骤3: 选择广播设备
```javascript
// 同文件广播，在设备表格中选择设备
selectedDevices.value = [
    { deviceId: 12345, deviceName: '设备1' },
    { deviceId: 67890, deviceName: '设备2' }
]
```

#### 步骤4: 开始麦克风广播
```javascript
// 用户点击播放按钮
// 系统会调用:
const deviceIds = [12345, 67890]
const broadcastType = 1 // 麦克风广播类型

await chromeSDKManager.quickStartBroadcast(deviceIds, broadcastType, '')
```

### 3. 停止广播示例

```javascript
// 用户再次点击播放按钮（此时显示为"停止广播"）
// 系统会调用:
await chromeSDKManager.quickStopBroadcast()

// 广播状态会被重置:
// isPlaying.value = false
// 计时器会被停止
// currentTime.value = 0
```

## 错误处理示例

### 1. 未选择设备
```javascript
// 如果用户未选择设备就点击播放按钮
if (selectedDevices.value.length === 0) {
    ElMessage.error('请先选择要广播的设备')
    return
}
```

### 2. 文件广播未选择文件
```javascript
// 如果文件广播模式下未选择文件
if (activeCard.value === '2' && !selectedFile.value) {
    throw new Error('请先选择要广播的文件')
}
```

### 3. 文件类型验证
```javascript
// 上传文件时的类型验证
const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/x-m4a']
if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|m4a)$/i)) {
    ElMessage.error('请选择有效的音频文件 (MP3, WAV, M4A)')
    return
}
```

### 4. 文件大小验证
```javascript
// 文件大小限制检查
const maxSize = 50 * 1024 * 1024 // 50MB
if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过50MB')
    return
}
```

## API调用流程

### 开始广播流程
```
1. 用户操作 → togglePlay()
2. 参数验证 → 设备选择、文件选择等
3. 构建参数 → deviceIds, broadcastType, filePath
4. API调用 → chromeSDKManager.quickStartBroadcast()
5. 状态更新 → isPlaying = true, 启动计时器
6. 用户反馈 → 成功提示或错误提示
```

### 停止广播流程
```
1. 用户操作 → togglePlay()
2. API调用 → chromeSDKManager.quickStopBroadcast()
3. 状态更新 → isPlaying = false, 停止计时器
4. 用户反馈 → 成功提示或错误提示
```

## 界面状态变化

### 播放按钮状态
```javascript
// 未选择设备时
<div class="play-button disabled">
    // 按钮显示为禁用状态，灰色背景

// 选择设备后
<div class="play-button">
    // 按钮可点击，红色背景

// 广播进行中
<div class="play-button">
    <VideoPause /> // 显示暂停图标
    // 按钮文字显示"停止广播"
```

### 设备计数显示
```javascript
// 选择设备后显示
<div class="device-count">
    已选择 2 个设备
</div>
```

### 文件选择状态
```javascript
// 选择预设文件
selectedFile.value = 'system'
// 下拉框显示: "系统提示音.mp3"

// 上传本地文件
uploadedFile.value = File对象
selectedFile.value = '我的音频.mp3'
// 下拉框显示: "我的音频.mp3"
```

## 调试信息

所有关键操作都会在控制台输出详细日志：

```javascript
console.log('切换到卡片:', cardType)
console.log('选中的设备:', selection)
console.log('开始文件广播:', { deviceIds, broadcastType, filePath, ... })
console.log('开始麦克风广播:', { deviceIds, broadcastType, volume })
console.log('选择的文件:', file)
console.log('广播开始成功')
console.log('广播停止成功')
```

这些日志信息有助于开发和调试过程中跟踪功能执行情况。
