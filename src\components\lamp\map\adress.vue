<template>
  <!-- 在vue项目中使用腾讯地图 -->
  <div style="width: 50%; height: 100%;">
    <el-form :model="form" ref="ruleForm" :rules="rules" class="form">
      <el-form-item label="地址:" prop="stationName" >
        <el-select v-model="position" value-key="id" filterable remote reserve-keyword placeholder="请输入地址"
          :remote-method="remoteMethod" :loading="loading" style="width: 100%;margin-bottom: 10px;"
          @change="selectAddress" popper-class="my-select-dropdown">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"
            class="diy-option">
            <div class="address-box">
              <img src="https://webapi.amap.com/ui/1.1/ui/misc/PoiPicker/assets/poi.png">
              <div class="address-info">
                <div class="address-name">{{ item.label }}</div>
                <div class="address-lat">{{ item.value.address }}</div>
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="定位:" label-width="55px">
        <tx-map ref="map" class="Txmap-box" @setAddress="setAddress" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import TxMap from "./TxMap/index.vue"; //腾讯地图
import { jsonp } from "vue-jsonp";

export default {
  components: { TxMap },
  props: {
    oldAddress: {
      type: Object,
      default: () => { }
    },
  },
  emits: ['changeAddress'],
  data() {
    return {
      options: [],
      rules: {
        stationName: [
          { required: true, message: 'Please input Activity name', trigger: 'blur' },
        ],
      },
      loading: false,
      form: {
        lat: "",
        lng: "",
      },
      position: "",
      mapKey: "EMWBZ-B4CKQ-FLP5W-2YMQ6-DNZMF-FFF6U",
    };
  },
  methods: {
    async selectAddress() {
      console.log(this.position);
      let row = {
        store_address: this.position.address,
        title: this.position.title,
        lat: this.position.location.lat,
        lng: this.position.location.lng
      }
      let res = await jsonp(
        `https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${this.mapKey}&location=${row.lat},${row.lng}`
      )
      row.cityCode = res.result.ad_info.city_code
      this.$emit('changeAddress', row)
      this.$refs["map"].init(
        15,
        this.position.location.lat,
        this.position.location.lng,
        this.form.store_address
      );
    },
    remoteMethod(query) {
      // console.log(query);
      this.loading = true
      if (query !== "") {
        jsonp("https://apis.map.qq.com/ws/place/v1/suggestion", {
          key: 'EMWBZ-B4CKQ-FLP5W-2YMQ6-DNZMF-FFF6U',
          keyword: query,
          output: "jsonp"
        }).then(res => {
          // console.log(res);
          let data = res.data
          this.options = data.map(item => {
            return { value: item, label: item.title }
          })
          this.loading = false
        })
      }
    },
    //更新地点
    setAddress(newAddress) {
      console.log("newAddress", newAddress);
      this.form.store_address =
        newAddress.formatted_addresses.recommend; //地址
      this.form.lat = newAddress.location.lat; //纬度
      this.form.lng = newAddress.location.lng; //经度
      this.form.cityCode = newAddress.ad_info.city_code; //经度
      this.form = Object.assign({}, this.form, {
        store_address: newAddress.formatted_addresses.standard_address,
        title: newAddress.formatted_addresses.recommend,
      });
      this.position = this.form.title
      console.log("this.form.store_address", this.form.store_address);
      this.$emit('changeAddress', this.form)
    },
    addressChange(zoom = 15) {
      //在地图上定位地点
      this.position = this.oldAddress.addressName
      this.$refs["map"].init(
        zoom,
        this.oldAddress.lat,
        this.oldAddress.lng,
        this.oldAddress.addressName
      );
    },
  },
  created() {
    this.$nextTick(() => {
      if (this.oldAddress?.lat) {
        this.addressChange(15);
      } else {
        this.$refs["map"].getCurrent();
      }
    })

  },
};
</script>

<style lang="scss" scoped>
.Txmap-box {
  width: 100%;
  height: 100%;
}

.diy-option {
  height: 60px;
}

.address-box {
  display: flex;
  align-items: center;
  height: 60px;
  border-bottom: 1px solid #f0f0f0;

  img {
    width: 11px;
    height: 16px;
    margin-right: 10px;
  }

  .address-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .address-name {
    margin: 0;
    height: 40%;
    line-height: 20px;
  }

  .address-lat {
    height: 40%;
    line-height: 20px;
  }
}

:deep(.el-select) {
  .el-select__wrapper {
    background-color: #F7F8FA;
    border: none;
    box-shadow: none;
    padding: 12px;
    
    &.is-focus {
      box-shadow: none;
    }

    &.is-readonly {
      cursor: pointer;
    }
  }
  
  .el-input__inner {
    &::placeholder {
      color: #A2AAB3;
    }
  }

  input {
    height: 20px;
    font-size: 14px;
    background-color: transparent;
    &::placeholder {
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: 350;
      line-height: normal;
      letter-spacing: 0.04em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #A2AAB3;
    }
  }
}
</style>
