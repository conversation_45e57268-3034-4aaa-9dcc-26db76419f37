/**
 * Chrome SDK 广播相关接口
 * 基于Chrome SDK接口文档
 */
import chromeSDK from './chromeSDK.js';

/**
 * 广播管理类
 */
class BroadcastManager {
  constructor() {
    this.sdk = chromeSDK;
  }

  /**
   * 生成请求ID
   * @returns {number} 请求ID
   */
  generateRequestId() {
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  // ==================== 广播控制接口 ====================

  /**
   * 打开广播
   * @param {number[]} deviceIds - 广播的设备
   * @param {number} broadcastType - 广播类型（1：麦克风广播；2：文件广播）
   * @param {string} bcFilePath - 广播文件路径，文件广播时必填
   * @returns {Promise} 操作结果
   */
  startBroadcast(deviceIds, broadcastType, bcFilePath = '') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onStartBroadcast;
      this.sdk.callbacks.onStartBroadcast = (result) => {
        this.sdk.callbacks.onStartBroadcast = originalCallback;
        if (result.error === 'success') {
          resolve(result);
        } else {
          reject(new Error(result.error));
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.startBroadcast(loginHandle, deviceIds, broadcastType, bcFilePath);
      } catch (error) {
        this.sdk.callbacks.onStartBroadcast = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 更新广播文件
   * @param {string} bcFilePath - 广播文件路径
   * @returns {Promise} 操作结果
   */
  updateBroadcastFile(bcFilePath) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      try {
        this.sdk.dhWeb.updateBroadcastFile(bcFilePath);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 关闭广播
   * @returns {Promise} 操作结果
   */
  stopBroadcast() {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      try {
        this.sdk.dhWeb.stopBroadcast(loginHandle);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 设置广播状态
   * @param {string} bcPlayState - 广播状态（play : 播放； pause: 暂停）
   * @returns {Promise} 操作结果
   */
  setBroadcastState(bcPlayState) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      try {
        this.sdk.dhWeb.setBroadcastState(bcPlayState);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== 音频文件管理接口 ====================

  /**
   * 获取音频文件列表
   * @returns {Promise} 音频文件列表
   */
  getAudioFileList() {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetAudioFileList;
      this.sdk.callbacks.onGetAudioFileList = (result) => {
        this.sdk.callbacks.onGetAudioFileList = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getAudioFileList(requestId, loginHandle);
      } catch (error) {
        this.sdk.callbacks.onGetAudioFileList = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 上传音频文件
   * @param {string} fileName - 文件名称
   * @param {string} filePath - 文件路径
   * @returns {Promise} 操作结果
   */
  uploadAudioFile(fileName, filePath) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onUploadAudioFile;
      this.sdk.callbacks.onUploadAudioFile = (result) => {
        this.sdk.callbacks.onUploadAudioFile = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.uploadAudioFile(requestId, loginHandle, fileName, filePath);
      } catch (error) {
        this.sdk.callbacks.onUploadAudioFile = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 删除音频文件
   * @param {number} fileId - 文件ID
   * @returns {Promise} 操作结果
   */
  deleteAudioFile(fileId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onDeleteAudioFile;
      this.sdk.callbacks.onDeleteAudioFile = (result) => {
        this.sdk.callbacks.onDeleteAudioFile = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.deltetAudioFile(requestId, loginHandle, fileId);
      } catch (error) {
        this.sdk.callbacks.onDeleteAudioFile = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 音量管理接口 ====================

  /**
   * 获取设备音量
   * @param {number} deviceId - 设备ID
   * @param {string} type - 音量类型（input: 麦克风音量，output：喇叭音量）
   * @returns {Promise} 设备音量信息
   */
  getDeviceVolume(deviceId, type) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetDeviceVolume;
      this.sdk.callbacks.onGetDeviceVolume = (result) => {
        this.sdk.callbacks.onGetDeviceVolume = originalCallback;
        try {
          const msg = typeof result === 'string' ? JSON.parse(result) : result;
          if (msg.reqData && msg.reqData.id === requestId) {
            if (msg.tunnelData) {
              const tunnelData = typeof msg.tunnelData === 'string' ? JSON.parse(msg.tunnelData) : msg.tunnelData;
              if (tunnelData.result === true) {
                resolve({
                  deviceId: msg.deviceId,
                  loginHandle: msg.loginHandle,
                  volume: tunnelData.params?.volume || [],
                  type: msg.reqData.type
                });
              } else {
                reject(new Error('获取设备音量失败'));
              }
            } else {
              reject(new Error('获取设备音量失败：无返回数据'));
            }
          }
        } catch (error) {
          reject(new Error('解析设备音量数据失败: ' + error.message));
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getDeviceVolume(requestId, loginHandle, deviceId, type);
      } catch (error) {
        this.sdk.callbacks.onGetDeviceVolume = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 设置设备音量
   * @param {number} deviceId - 设备ID
   * @param {string} type - 音量类型（input: 麦克风音量，output：喇叭音量）
   * @param {number[]} volume - 设备音量，数组类型，每个音频通道一个配置（暂只支持单通道）
   * @returns {Promise} 操作结果
   */
  setDeviceVolume(deviceId, type, volume) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSetDeviceVolume;
      this.sdk.callbacks.onSetDeviceVolume = (result) => {
        this.sdk.callbacks.onSetDeviceVolume = originalCallback;
        try {
          const msg = typeof result === 'string' ? JSON.parse(result) : result;
          if (msg.reqData && msg.reqData.id === requestId) {
            if (msg.tunnelData) {
              const tunnelData = typeof msg.tunnelData === 'string' ? JSON.parse(msg.tunnelData) : msg.tunnelData;
              if (tunnelData.result === true) {
                resolve({
                  deviceId: msg.deviceId,
                  loginHandle: msg.loginHandle,
                  success: true
                });
              } else {
                reject(new Error('设置设备音量失败'));
              }
            } else {
              reject(new Error('设置设备音量失败：无返回数据'));
            }
          }
        } catch (error) {
          reject(new Error('解析设备音量设置结果失败: ' + error.message));
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.setDeviceVolume(requestId, loginHandle, deviceId, type, volume);
      } catch (error) {
        this.sdk.callbacks.onSetDeviceVolume = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 获取广播音量
   * @param {number} deviceId - 设备ID
   * @returns {Promise} 广播音量信息
   */
  getBroadcastVolume(deviceId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetBroadcastVolume;
      this.sdk.callbacks.onGetBroadcastVolume = (result) => {
        this.sdk.callbacks.onGetBroadcastVolume = originalCallback;
        try {
          const msg = typeof result === 'string' ? JSON.parse(result) : result;
          if (msg.reqData && msg.reqData.id === requestId) {
            if (msg.tunnelData) {
              const tunnelData = typeof msg.tunnelData === 'string' ? JSON.parse(msg.tunnelData) : msg.tunnelData;
              if (tunnelData.result === true) {
                resolve({
                  deviceId: msg.deviceId,
                  loginHandle: msg.loginHandle,
                  volume: tunnelData.params?.volume || []
                });
              } else {
                reject(new Error('获取广播音量失败'));
              }
            } else {
              reject(new Error('获取广播音量失败：无返回数据'));
            }
          }
        } catch (error) {
          reject(new Error('解析广播音量数据失败: ' + error.message));
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getBroadcastVolume(requestId, loginHandle, deviceId);
      } catch (error) {
        this.sdk.callbacks.onGetBroadcastVolume = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 设置广播音量
   * @param {number} deviceId - 设备ID
   * @param {number[]} volume - 设备音量，数组类型，每个音频通道一个配置（暂只支持单通道）
   * @returns {Promise} 操作结果
   */
  setBroadcastVolume(deviceId, volume) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onSetBroadcastVolume;
      this.sdk.callbacks.onSetBroadcastVolume = (result) => {
        this.sdk.callbacks.onSetBroadcastVolume = originalCallback;
        try {
          const msg = typeof result === 'string' ? JSON.parse(result) : result;
          if (msg.reqData && msg.reqData.id === requestId) {
            if (msg.tunnelData) {
              const tunnelData = typeof msg.tunnelData === 'string' ? JSON.parse(msg.tunnelData) : msg.tunnelData;
              if (tunnelData.result === true) {
                resolve({
                  deviceId: msg.deviceId,
                  loginHandle: msg.loginHandle,
                  success: true
                });
              } else {
                reject(new Error('设置广播音量失败'));
              }
            } else {
              reject(new Error('设置广播音量失败：无返回数据'));
            }
          }
        } catch (error) {
          reject(new Error('解析广播音量设置结果失败: ' + error.message));
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.setBroadcastVolume(requestId, loginHandle, deviceId, volume);
      } catch (error) {
        this.sdk.callbacks.onSetBroadcastVolume = originalCallback;
        reject(error);
      }
    });
  }

  // ==================== 广播任务管理接口 ====================

  /**
   * 获取广播任务列表
   * @param {string} taskType - 任务类型（realtime：实时任务，timed：定时任务）默认值：timed
   * @returns {Promise} 广播任务列表
   */
  getBCTaskList(taskType = 'timed') {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetBCTaskList;
      this.sdk.callbacks.onGetBCTaskList = (result) => {
        this.sdk.callbacks.onGetBCTaskList = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getBCTaskList(requestId, loginHandle, taskType);
      } catch (error) {
        this.sdk.callbacks.onGetBCTaskList = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 获取广播任务ID列表
   * @returns {Promise} 广播任务ID列表
   */
  getTaskIdList() {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetTaskIdList;
      this.sdk.callbacks.onGetTaskIdList = (result) => {
        this.sdk.callbacks.onGetTaskIdList = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getTaskIdList(requestId);
      } catch (error) {
        this.sdk.callbacks.onGetTaskIdList = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 根据任务ID获取广播任务详情
   * @param {number[]} taskIds - 任务ID数组
   * @returns {Promise} 广播任务详情
   */
  getTaskIdInfo(taskIds) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onGetTaskIdInfo;
      this.sdk.callbacks.onGetTaskIdInfo = (result) => {
        this.sdk.callbacks.onGetTaskIdInfo = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.getTaskIdInfo(requestId, taskIds);
      } catch (error) {
        this.sdk.callbacks.onGetTaskIdInfo = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 上传广播任务
   * @param {Object} taskConfig - 任务配置
   * @returns {Promise} 操作结果
   */
  uploadBCTask(taskConfig) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onUploadBCTask;
      this.sdk.callbacks.onUploadBCTask = (result) => {
        this.sdk.callbacks.onUploadBCTask = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        const {
          taskName,
          volume,
          deviceIds,
          fileIds,
          enable,
          execMode,
          weekDay,
          startTimeOfDay,
          expireObj,
          timeMode,
          modeContent,
          taskType = 'timed',
          bcType = 'file'
        } = taskConfig;

        this.sdk.dhWeb.uploadBCTask(
          requestId,
          loginHandle,
          taskName,
          volume,
          deviceIds,
          fileIds,
          enable,
          execMode,
          weekDay,
          startTimeOfDay,
          expireObj,
          timeMode,
          modeContent,
          taskType,
          bcType
        );
      } catch (error) {
        this.sdk.callbacks.onUploadBCTask = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 修改广播任务
   * @param {number} taskId - 任务ID
   * @param {Object} taskConfig - 任务配置
   * @returns {Promise} 操作结果
   */
  editBCTask(taskId, taskConfig) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onEditBCTask;
      this.sdk.callbacks.onEditBCTask = (result) => {
        this.sdk.callbacks.onEditBCTask = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        const {
          taskName,
          volume,
          deviceIds,
          fileIds,
          enable,
          execMode,
          weekDay,
          startTimeOfDay,
          expireObj,
          timeMode,
          modeContent,
          taskType = 'timed',
          bcType = 'file'
        } = taskConfig;

        this.sdk.dhWeb.editBCTask(
          requestId,
          loginHandle,
          taskId,
          taskName,
          volume,
          deviceIds,
          fileIds,
          enable,
          execMode,
          weekDay,
          startTimeOfDay,
          expireObj,
          timeMode,
          modeContent,
          taskType,
          bcType
        );
      } catch (error) {
        this.sdk.callbacks.onEditBCTask = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 删除广播任务
   * @param {number} taskId - 任务ID
   * @returns {Promise} 操作结果
   */
  deleteBCTask(taskId) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      // 设置临时回调
      const originalCallback = this.sdk.callbacks.onDeleteBCTask;
      this.sdk.callbacks.onDeleteBCTask = (result) => {
        this.sdk.callbacks.onDeleteBCTask = originalCallback;
        if (result.id === requestId) {
          if (result.error === 'success') {
            resolve(result);
          } else {
            reject(new Error(result.error));
          }
        }
        if (originalCallback) originalCallback(result);
      };

      try {
        this.sdk.dhWeb.deleteBCTask(requestId, loginHandle, taskId);
      } catch (error) {
        this.sdk.callbacks.onDeleteBCTask = originalCallback;
        reject(error);
      }
    });
  }

  /**
   * 试听广播任务（实时文件广播）
   * @param {number} taskId - 任务ID
   * @param {string} action - 试听动作（"start": 开始， "stop": 停止）
   * @returns {Promise} 操作结果
   */
  tryBCTask(taskId, action) {
    return new Promise((resolve, reject) => {
      if (!this.sdk.dhWeb) {
        reject(new Error('Chrome SDK未初始化'));
        return;
      }

      const loginHandle = this.sdk.getLoginHandle();
      if (!loginHandle) {
        reject(new Error('未登录'));
        return;
      }

      const requestId = this.generateRequestId();

      try {
        this.sdk.dhWeb.tryBCTask(requestId, loginHandle, taskId, action);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }
}

// 创建单例实例
const broadcastManager = new BroadcastManager();

export default broadcastManager;
