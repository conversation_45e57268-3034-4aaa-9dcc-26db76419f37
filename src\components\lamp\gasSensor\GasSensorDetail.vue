<template>
  <div class="detail-container">
    <div class="detail-grid">
      <!-- 第一行 -->
      <div class="detail-item">
        <div class="item-title">设备名称</div>
        <div class="item-value">{{ data.deviceName }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
      <div class="detail-item">
        <div class="item-title">物联网ID</div>
        <div class="item-value">{{ data.deviceId }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
      <div class="detail-item">
        <div class="item-title">设备编号</div>
        <div class="item-value">{{ data.deviceNo }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
      <div class="detail-item">
        <div class="item-title">总挥发性有机物</div>
        <div class="item-value">{{ sensorData.tvoc !== '' && sensorData.tvoc != null ? `${sensorData.tvoc} mg/m³` : '暂无' }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="detail-item">
        <div class="item-title">甲醛</div>
        <div class="item-value">{{ sensorData.hcho !== '' && sensorData.hcho != null ? `${sensorData.hcho} mg/m³` : '暂无' }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
      <div class="detail-item">
        <div class="item-title">二氧化硫</div>
        <div class="item-value">{{ sensorData.so2 !== '' && sensorData.so2 != null ? `${sensorData.so2} ppm` : '暂无' }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
      <div class="detail-item">
        <div class="item-title">氨气</div>
        <div class="item-value">{{ sensorData.nh3 !== '' && sensorData.nh3 != null ? `${sensorData.nh3} ppm` : '暂无' }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
      <div class="detail-item">
        <div class="item-title">硫化氢</div>
        <div class="item-value">{{ sensorData.h2S !== '' && sensorData.h2S != null ? `${sensorData.h2S} ppm` : '暂无' }}</div>
        <div class="item-time">
          <div class="item-time-text">更新时间：</div>
          <div class="item-time-text">{{ data.updateTime }}</div>
        </div>
      </div>
    </div>

    <!-- 设备信息 -->
    <div class="device-info">
      <img style="width: 63px; height: 63px;" src="/img/avatar.png"  />
      <div class="info-text">
        <div class="info-name">{{ data.location }}</div>
        <!-- <div class="info-desc">{{ data.location }}</div> -->
      </div>
    </div>

    <!-- 提示文本 -->
    <div class="tip-text">
      可能您所访问的终端不在您的运行分组内，无法通过当前订阅获取终端数据。若不确定终端是否在您的运行分组内，请查看您的运行分组。若确定终端在您的运行分组内，请等待数据同步或重新订阅。若终端不在您的运行分组内，请联系设备所有者将终端加入您的运行分组中。此外，建议您在终端离线后，要等待30秒左右才能获取到最新的数据。
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import { getHistoryData } from '@/api/lamp/gasSensor.js'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

// 安全的 JSON 解析函数
const safeParseJson = (jsonString) => {
  try {
    return jsonString && JSON.parse(jsonString) || {}
  } catch (e) {
    console.warn('JSON parse error:', e)
    return {}
  }
}

// 计算属性获取传感器数据
const sensorData = computed(() => safeParseJson(props.data.data))
</script>

<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.detail-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 0px 24px 24px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.detail-item {
  background-color: #f7f8fa;
  padding: 16px;
  border-radius: 8px;
  width: 358px;
  .item-title {
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #3D3D3D;
  }

  .item-value {
    font-size: 20px;
    font-weight: 600;
    color: #1d2129;
    margin: 20px 0;
  }

  .item-time {
    color: #86909c;
    font-size: 12px;
  }
}

.device-info {
  display: flex;
  align-items: center;
  justify-content: center; 
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f7f8fa;
  border-radius: 8px;
  .info-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    .info-name {
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #137CFF;
      // margin-bottom: 9px;
    }

    .info-desc {
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #9E9E9E;
    }
  }
}

.tip-text {
  color: #86909c;
  font-size: 12px;
  line-height: 1.6;
  padding: 16px;
  background-color: #f7f8fa;
  border-radius: 8px;
  height: 200px;
}
.item-time-text{
  margin-bottom: 8px;
  font-family: Source Han Sans;
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #3D3D3D;
}
</style> 