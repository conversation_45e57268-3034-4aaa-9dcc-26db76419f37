import request from '@/axios';

// 获取分组列表
export const getMonitorList = (params) => {
  return request({
    url: '/hztech-light/supervisoryControl/page',
    method: 'get',
    params
  })
}

// 获取视频监控
export const getVideoMonitor = (row) => {
  return request({
    url: '/hztech-light/light/videoPreview',
    method: 'post',
    data: row
  })
}

// 获取视频监控回放
export const getVideoMonitorReplay = (row) => {
  return request({
    url: '/hztech-light/light/playback',    
    method: 'post',
    data: row
  })
}



