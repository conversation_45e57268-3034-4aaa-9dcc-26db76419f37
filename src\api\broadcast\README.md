# Chrome SDK 接口封装

基于Chrome SDK接口文档的JavaScript接口封装，提供了完整的设备管理、广播控制、用户管理等功能。

## 文件结构

```
src/api/broadcast/
├── index.js          # 主入口文件，统一管理所有模块
├── login.js          # 登录接口封装（单独封装）
├── chromeSDK.js      # Chrome SDK 主要接口类
├── groupUser.js      # 分组和用户管理接口
├── broadcast.js      # 广播相关接口
├── device.js         # 设备管理相关接口
├── system.js         # 系统管理相关接口
├── example.js        # 使用示例
└── README.md         # 说明文档
```

## 快速开始

### 1. 初始化SDK

```javascript
import chromeSDKManager from '@/api/broadcast/index.js';

// 假设DHAlarmWeb已经加载
const dhWeb = new DHAlarmWeb();
chromeSDKManager.init(dhWeb);
```

### 2. 登录

```javascript
// 方式1：使用快捷方法
try {
  const result = await chromeSDKManager.quickLogin('mainalc', 'password', '*************');
  console.log('登录成功:', result);
} catch (error) {
  console.error('登录失败:', error);
}

// 方式2：使用登录管理器
const loginManager = chromeSDKManager.getLoginManager();
loginManager.onLoginSuccess((result) => {
  console.log('登录成功:', result);
});

loginManager.onErrorCallback((error) => {
  console.error('登录失败:', error);
});

await loginManager.login('mainalc', 'password', '*************');
```

### 3. 设置回调函数

```javascript
// 设备列表回调
chromeSDKManager.on('onDeviceList', (result) => {
  console.log('设备列表:', result);
});

// 设备通知回调
chromeSDKManager.on('onNotify', (result) => {
  console.log('设备通知:', result);
});

// 广播开始回调
chromeSDKManager.on('onStartBroadcast', (result) => {
  console.log('广播开始:', result);
});
```

## 主要功能模块

### 登录管理 (login.js)

```javascript
const loginManager = chromeSDKManager.getLoginManager();

// 登录
await loginManager.login(username, password, serverAddr);

// 登出
await loginManager.logout();

// 检查登录状态
const isLoggedIn = loginManager.getLoginStatus();

// 获取登录句柄
const loginHandle = loginManager.getLoginHandle();
```

### 视频播放

```javascript
const sdk = chromeSDKManager.getSDK();

// 播放视频
const video = document.getElementById('videoElement');
await sdk.playRT(video, deviceId, false);

// 停止视频
await sdk.stopRT(deviceId);

// 打开对讲
await sdk.startTalk(deviceId);

// 设备控制（开锁等）
await sdk.doControl(deviceId, 1);
```

### 广播管理

```javascript
const broadcastManager = chromeSDKManager.getBroadcastManager();

// 开始广播
const deviceIds = [12345, 67890];
const broadcastType = chromeSDKManager.BROADCAST_TYPE.FILE;
const filePath = '/path/to/audio.mp3';
await broadcastManager.startBroadcast(deviceIds, broadcastType, filePath);

// 停止广播
await broadcastManager.stopBroadcast();

// 获取音频文件列表
const audioFiles = await broadcastManager.getAudioFileList();

// 获取广播任务列表
const taskList = await broadcastManager.getBCTaskList('timed');

// 创建广播任务
const taskConfig = {
  taskName: '测试任务',
  volume: 50,
  deviceIds: [12345],
  fileIds: [1, 2],
  enable: true,
  execMode: 'Single',
  startTimeOfDay: '10:00:00',
  // ... 其他配置
};
await broadcastManager.uploadBCTask(taskConfig);
```

### 设备管理

```javascript
const deviceManager = chromeSDKManager.getDeviceManager();

// 获取设备额外信息
const deviceExtra = await deviceManager.getDeviceExtra(0);

// 设置设备坐标
await deviceManager.setDeviceExtra(
  deviceId, 
  '118.6691_36.1341', 
  '联系人', 
  '13012345678', 
  '备注'
);

// 获取设备音量
const volume = await deviceManager.getDeviceVolume(deviceId, 'output');

// 设置设备音量
await deviceManager.setDeviceVolume(deviceId, 'output', [50]);

// 获取录像列表
const recordList = await deviceManager.getRecordList(deviceId, beginTime, endTime);

// 播放录像
await deviceManager.playBack(deviceId, beginTime, videoElement);
```

### 分组用户管理

```javascript
const groupUserManager = chromeSDKManager.getGroupUserManager();

// 添加分组
await groupUserManager.addGroup('normal', '分组名称', '联系人', '电话');

// 编辑分组
await groupUserManager.editGroup(groupId, '新名称', '新联系人', '新电话');

// 获取用户列表
const userList = await groupUserManager.getUsers();

// 添加用户
await groupUserManager.addUser('username', 'password');

// 授权分组
await groupUserManager.authorizeGroup(userId, [groupId1, groupId2]);

// 编辑设备
await groupUserManager.editDevice(deviceId, '设备名称', '联系人', '电话');
```

### 系统管理

```javascript
const systemManager = chromeSDKManager.getSystemManager();

// 获取系统配置
const systemConfig = await systemManager.getSystemCfg();

// 设置系统配置
const config = {
  serverName: '服务器名称',
  serverPort: 9534,
  password: '密码',
  // ... 其他配置
};
await systemManager.setSystemCfg(config);

// 搜索日志
const logResult = await systemManager.searchLog(beginTime, endTime, 0, 20);

// 获取铃声规则
const tipAudioInfo = await systemManager.getTipAudioInfo();
```

## 常用常量

```javascript
// 广播类型
chromeSDKManager.BROADCAST_TYPE.MIC    // 麦克风广播
chromeSDKManager.BROADCAST_TYPE.FILE   // 文件广播

// 设备类型
chromeSDKManager.DEVICE_TYPE.ALARM     // 报警设备
chromeSDKManager.DEVICE_TYPE.IPC       // 联动设备

// 设备状态
chromeSDKManager.DEVICE_ACTION.NORMAL   // 设备在线
chromeSDKManager.DEVICE_ACTION.OFFLINE  // 设备离线
chromeSDKManager.DEVICE_ACTION.START    // 设备呼叫中
chromeSDKManager.DEVICE_ACTION.DEALING  // 报警处理中

// 分组类型
chromeSDKManager.GROUP_TYPE.NORMAL      // 普通分组
chromeSDKManager.GROUP_TYPE.LINKAGE     // 联动分组
chromeSDKManager.GROUP_TYPE.DEFENSE     // 联防分组

// 音量类型
chromeSDKManager.VOLUME_TYPE.INPUT      // 麦克风音量
chromeSDKManager.VOLUME_TYPE.OUTPUT     // 喇叭音量

// 广播状态
chromeSDKManager.BROADCAST_STATE.PLAY   // 播放
chromeSDKManager.BROADCAST_STATE.PAUSE  // 暂停
```

## 错误处理

```javascript
// 设置全局错误处理
chromeSDKManager.getLoginManager().onErrorCallback((error) => {
  console.error('SDK错误:', error);
  
  if (error.msg && error.msg.error === 'loginTimeout') {
    console.log('登录超时，需要重新登录');
    // 重新登录逻辑
  }
});

// 设置服务器断开处理
chromeSDKManager.getLoginManager().onLogoutCallback(() => {
  console.log('服务器连接断开');
  // 重连逻辑
});
```

## 完整示例

参考 `example.js` 文件中的完整使用示例，包含：

- SDK初始化
- 登录流程
- 设备管理
- 广播控制
- 用户管理
- 系统配置
- 错误处理

## 注意事项

1. **登录权限**: 接口1.8~1.18只有服务器账号才有权限调用，账号统一为"mainalc"
2. **异步操作**: 所有接口都返回Promise，需要使用async/await或.then()处理
3. **回调设置**: 建议在初始化后立即设置所需的回调函数
4. **错误处理**: 务必添加适当的错误处理逻辑
5. **资源清理**: 在组件销毁时记得清理回调和登出

## 依赖要求

- Chrome SDK (DHAlarmWeb)
- ES6+ 支持
- Promise 支持
