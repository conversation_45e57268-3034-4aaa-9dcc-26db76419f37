<template>
  <div class="title">选择设备</div>
  <div class="big-box">
    <div class="ganti-box">
        <div class="ganti-item" v-for="(item,index) in selectData" :key="index" :class="activation === index?'activation': ''" @click="activationFun(item,index)">
            <img class="img-style" :src="item.icon" alt="">
        </div>
    </div>
  </div>
    
</template>

<script setup>
import { ref, defineEmits } from 'vue'
const props = defineProps({
  selectData: {
    type: Object,
    default: () => ({})
  }
})

const activation = ref('')

const activationFun = (item,val) => {
  activation.value = val
  emit('activItem',item)
}

const emit = defineEmits(['activItem'])

</script>
<style lang="scss" scoped>
.title{
  margin-bottom: 24px;
}
.big-box{
  width: 100%;
  height: 292px;
  overflow-x: auto;
}
.ganti-box{
    height: 280px;
    display: flex;
    flex-wrap: nowrap;
}

.ganti-item{
  width: 280px;
  height: 280px;
  margin-right: 16px;
  background-color: #F2F2F2;
  border: 1px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  .img-style{
    width: 280px;
    height: 280px;
    border-radius: 8px;
  }
}

.activation{
  border: 1px solid #165DFF;
}

</style>