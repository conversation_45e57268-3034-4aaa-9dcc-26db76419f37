<template>
  <div class="bindbox">
    <div class="left">
      <div class="title-box">未选灯杆</div>
      <el-table
        @selection-change="leftChange"
        border
        :data="tableData"
        style="width: 100%; height: 100%"
      >
        <el-table-column type="selection"/>
        <el-table-column prop="poleNo" label="灯杆编号" ></el-table-column>
        <el-table-column prop="poleName" label="灯杆名称" />
        <el-table-column prop="poleAddress" label="灯杆地址" show-overflow-tooltip />
        <!-- <el-table-column prop="status" label="单灯状态" show-overflow-tooltip>
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.status == 1">开启</el-tag>
            <el-tag type="danger" v-if="scope.row.status == 2">关闭</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="单灯亮度" show-overflow-tooltip>
          <template #default="scope">
            <el-progress :text-inside="true" :stroke-width="18" :percentage="scope.row.value" />
          </template>
        </el-table-column> -->
      </el-table>
      <div class="paging-box">
        <el-pagination background layout="prev, pager, next" :page-size="20" :total="100" />
      </div>
    </div>
    <div class="transfer-btn">
      <el-button icon="ArrowLeftBold" size="large" type="primary" @click="leftPush" />
      <el-button icon="ArrowRightBold" size="large" type="primary" @click="rightPush" />
    </div>
    <div class="right">
      <div class="title-box">所选单灯</div>
      <el-table
        @selection-change="rightChange"
        border
        :data="tableDatatwo"
        style="width: 100%; height: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="poleNo" label="灯杆编号" ></el-table-column>
        <el-table-column prop="poleName" label="灯杆名称" />
        <el-table-column prop="poleAddress" label="灯杆地址" show-overflow-tooltip />
        <!-- <el-table-column prop="status" label="单灯状态" show-overflow-tooltip>
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.status == 1">开启</el-tag>
            <el-tag type="danger" v-if="scope.row.status == 2">关闭</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="brightness" label="单灯亮度">
          <template #default="scope">
            <el-progress
              :text-inside="true"
              :stroke-width="18"
              :percentage="scope.row.brightness"
            />
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
  <div class="btn-box">
    <el-button type="primary" @click="submit"> 提 交 </el-button>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue';
import {
  QueryIndicatorStem,
  leLightCentralStem,
  saveleLightCentral,
} from '@/api/lamp/switchBox.js';

const emit = defineEmits(['BindClose']);

const props = defineProps({
  bindData: {
    type: Object,
    default: () => ({}),
  },
});

const page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const getleftListData = () => {
  QueryIndicatorStem(page.value.currentPage, page.value.pageSize).then(res => {
    tableData.value = res.data.data.records;
    page.value.total = res.data.data.total;
    page.value.currentPage = res.data.data.current;
    page.value.pageSize = res.data.data.size;
  });
};

const getrightListData = () => {
  console.log(props.bindData, 'bindData');
  let row = {
    centralizedNo: props.bindData.centralizedNo,
    loopNumber: props.bindData.loopNumber,
  };
  leLightCentralStem(row).then(res => {
    tableDatatwo.value = res.data.data;
  });
};

onMounted(() => {
  console.log(props.bindData, 'bindData');
  getleftListData();
  getrightListData();
});

// 左边表格数据
const tableData = ref([]);
// 右边表格数据
const tableDatatwo = ref([]);
// 左边选中数据
const leftselectData = ref([]);
const leftChange = val => {
  console.log(val, 'leftChange');
  leftselectData.value = val;
  console.log(leftselectData.value, 'leftChange');
};
// 左边添加到右边
const rightPush = () => {
  leftselectData.value.forEach(item => {
    tableDatatwo.value.push(item);
  });
  tableData.value = filterArray(tableData.value, leftselectData.value);
  leftselectData.value = [];
  console.log(tableData.value, 'tableData');
};
// 过滤数组
const filterArray = (arr1, arr2) => {
  const arr2Set = new Set();
  for (const item of arr2) {
    arr2Set.add(`${item.id}-${item.name}`);
  }
  return arr1.filter(item => !arr2Set.has(`${item.id}-${item.name}`));
};
// 右边选中数据
const rightselectData = ref([]);
const rightChange = val => {
  console.log(val, 'rightChange');
  rightselectData.value = val;
  console.log(rightselectData.value, 'rightChange');
};
// 右边添加到左边
const leftPush = () => {
  rightselectData.value.forEach(item => {
    tableData.value.push(item);
  });
  tableDatatwo.value = filterArray(tableDatatwo.value, rightselectData.value);
  rightselectData.value = [];
  console.log(tableDatatwo.value, 'tableDatatwo');
};

const getluminaireNo = data => {
  let luminaireNo = '';
  data.forEach((item, index) => {
    if (index == 0) {
      luminaireNo = `${item.id}`;
    } else {
      luminaireNo = `${luminaireNo},${item.id}`;
    }
  });
  return luminaireNo;
};
const submit = () => {
  console.log(tableDatatwo.value, 'tableDatatwo');
  let poleIds = getluminaireNo(tableDatatwo.value);
  let row = {
    centralizedNo: props.bindData.centralizedNo,
    loopNumber: props.bindData.loopNumber,
    poleIds,
  };
  saveleLightCentral(row)
    .then(res => {
      console.log(res);
      emit('BindClose');
    })
    .catch(err => {
      console.log(err, 'err');
    });
  console.log(row, 'row');
};
</script>

<style scoped >
.bindbox {
  display: flex;
  width: 100%;
  height: 600px;
  padding: 6px 16px;
  align-items: center;
  justify-content: space-between;
}

.title-box {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
}

.left {
  width: 44%;
  height: calc(100% - 32px);
}
.right {
  width: 44%;
  height: calc(100% - 32px);
}
.btn-box {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}
.paging-box {
  display: flex;
  margin-top: 6px;
  justify-content: flex-end;
}
</style>
