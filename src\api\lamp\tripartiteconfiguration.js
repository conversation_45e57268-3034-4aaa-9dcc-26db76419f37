import request from '@/axios';

export const detail = (current, size, params) =>{
    return request({
      url: '/hztech-light/pushAccount/detail',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
}

export const remove = ( row ) =>{
    return request({
      url: '/hztech-light/pushAccount/remove',
      method: 'post',
      params:{
        ...row
      }
    })
}

export const update = ( row ) =>{
    return request({
      url: '/hztech-light/pushAccount/update',
      method: 'post',
      data:row
    })
}

export const save = ( row ) =>{
    return request({
      url: '/hztech-light/pushAccount/save',
      method: 'post',
      data:row
    })
}

export const chargeStatus = ( params ) =>{
  return request({
    url: '/hztech-light/pushAccount/chargeStatus',
    method: 'post',
    params:{
        ...params
    }
  })
}
