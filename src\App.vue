<template>
  <router-view />
  <el-dialog v-model="callDialog" :show-close="false" width="500">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass">This is a custom header!</h4>
        <el-button type="danger" @click="close">
          <el-icon class="el-icon--left"><CircleCloseFilled /></el-icon>
          Close
        </el-button>
      </div>
    </template>
    This is dialog content.
  </el-dialog>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import chromeSDKManager from '@/api/broadcast/index.js';
import { ElNotification, ElMessageBox } from 'element-plus'

// 用于去重的数据结构
const activeCallNotifications = ref(new Set()) // 当前活跃的呼叫通知
const lastCallTime = ref(new Map()) // 记录每个设备的最后呼叫时间

// 清理过期的时间记录（防止内存泄漏）
const cleanupExpiredRecords = () => {
  const currentTime = Date.now();
  const expireTime = 5 * 60 * 1000; // 5分钟过期

  for (const [deviceId, time] of lastCallTime.value.entries()) {
    if (currentTime - time > expireTime) {
      lastCallTime.value.delete(deviceId);
      console.log(`清理设备 ${deviceId} 的过期呼叫记录`);
    }
  }
}

// 定期清理过期记录
setInterval(cleanupExpiredRecords, 2 * 60 * 1000); // 每2分钟清理一次

const callDialog = ref(false);
const initSDK = async ()=>{
  try {
    // 假设DHAlarmWeb已经加载
    if (typeof DHAlarmWeb !== 'undefined') {
      const dhWeb = new DHAlarmWeb();
      chromeSDKManager.init(dhWeb);

      console.log('Chrome SDK初始化成功');
    login()
    } else {
      throw new Error('DHAlarmWeb未加载');
    }
  } catch (error) {
    console.error('SDK初始化失败:', error);
    throw error;
  }
}

// 登录
const login = async () => {
  try {
    const result = await chromeSDKManager.quickLogin('mainalc', '123456', '192.168.3.18');
    // const result = await chromeSDKManager.quickLogin('mainalc', '123456789', '192.168.1.3');
    if (result) {
      console.log('登录成功',result);
      setupDeviceNotifyCallback()
    } else {
      console.log('登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
  }
}

const setupDeviceNotifyCallback = ()=>{
  chromeSDKManager.on('onNotify', (result) => {
    console.log('设备通知:', result);
    if (result.params) {
      const { code, deviceId, action } = result.params;
    // action： “Normal”  设备在线或报警已被处理
		// “Offline”   设备离线
		// “Start”	  设备正在呼叫
		// “Dealing”  报警正在被处理
      console.log(`通知类型: ${code}, 设备ID: ${deviceId}, 动作: ${action}`);
      if (code === 'DeviceStatus') {
        console.log('设备状态变更:', action);
        if (action === 'Normal') {
          console.log('设备在线');
        } else if (action === 'Offline') {
          console.log('设备离线');
        } else if (action === 'Start') {
          console.log('设备正在呼叫');
          openCall(result.params)
        } else if (action === 'Dealing') {
          console.log('报警正在被处理');
        }
      }
    }
  });
}
const openCall = async (params) => {
  console.log('openCall', params);

  const deviceId = params.deviceId;
  const currentTime = Date.now();

  // 检查是否已有该设备的活跃呼叫通知
  if (activeCallNotifications.value.has(deviceId)) {
    console.log(`设备 ${deviceId} 已有活跃的呼叫通知，跳过重复呼叫`);
    return;
  }

  // 检查时间间隔去重（防止短时间内重复呼叫）
  const lastTime = lastCallTime.value.get(deviceId);
  const minInterval = 3000; // 3秒内的重复呼叫将被忽略

  if (lastTime && (currentTime - lastTime) < minInterval) {
    console.log(`设备 ${deviceId} 在 ${minInterval}ms 内重复呼叫，已忽略`);
    return;
  }

  // 记录当前呼叫
  activeCallNotifications.value.add(deviceId);
  lastCallTime.value.set(deviceId, currentTime);

  try {
    // 使用 ElMessageBox 创建交互式弹窗，只显示接听按钮
    await ElMessageBox.alert(
      `设备 ${params.deviceId} 正在呼叫，请点击接听`,
      '呼叫提醒',
      {
        confirmButtonText: '接听',
        type: 'warning',
        center: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false,
        customClass: 'call-notification-box'
      }
    );

    // 用户点击接听
    handleConnect(params.deviceId);

  } catch (error) {
    // 如果有异常，也清除活跃状态
    console.log('呼叫弹窗异常:', error);
  } finally {
    // 无论如何都要清除活跃状态
    activeCallNotifications.value.delete(deviceId);
  }
}

const handleConnect = (deviceId) => {
  console.log('handleConnect', deviceId);

  // 清除该设备的去重状态
  activeCallNotifications.value.delete(deviceId);

  // 这里可以添加实际的接听逻辑
  // 例如：建立音视频连接、更新设备状态等

  ElNotification({
    title: `接听成功`,
    message: `已接听设备 ${deviceId} 的呼叫`,
    type: 'success',
    duration: 3000
  });
}

// 移除了 handleReject 函数，用户只能接听呼叫

onMounted(() => {
    initSDK()
})
</script>

<style lang="scss">
html,
body,
#app {
  width: 100%;
  height: 100%;
}
.input-style{
  width: 100%;
  border: none;
  background-color: #F7F8FA;
}

// 呼叫通知弹窗样式
:deep(.call-notification-box) {
  .el-message-box {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

    .el-message-box__header {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: white;
      border-radius: 12px 12px 0 0;
      padding: 20px;

      .el-message-box__title {
        color: white;
        font-weight: 600;
        font-size: 18px;
      }
    }

    .el-message-box__content {
      padding: 30px 20px;

      .el-message-box__message {
        font-size: 16px;
        color: #303133;
        text-align: center;
        margin: 0;
      }
    }

    .el-message-box__btns {
      padding: 20px;
      display: flex;
      justify-content: center;

      .el-button {
        min-width: 120px;
        height: 45px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;

        &.el-button--primary {
          background: linear-gradient(135deg, #67c23a, #85ce61);
          border: none;
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);

          &:hover {
            background: linear-gradient(135deg, #5daf34, #7bc143);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(103, 194, 58, 0.4);
          }
        }
      }
    }
  }

  // 添加呼叫动画效果
  .el-message-box__header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.3) 0%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(255, 255, 255, 0.3) 100%);
    animation: call-pulse 2s ease-in-out infinite;
  }
}

@keyframes call-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}


</style>
