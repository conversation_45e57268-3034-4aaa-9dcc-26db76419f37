<template>
  <basic-container>
    <div class="control-panel">
        <el-row :gutter="20" class="mb-4">
            <el-col :span="24">
                <el-card>
                    <template #header>
                        <span>连接控制</span>
                        <el-tag :type="connectionStatusType" style="float: right;">
                            {{ connectionStatusText }}
                        </el-tag>
                    </template>

                    <el-form :model="serverConfig" :inline="true">
                        <el-form-item label="服务器地址">
                            <el-input v-model="serverConfig.address" placeholder="服务器地址" />
                        </el-form-item>
                        <el-form-item label="用户名">
                            <el-input v-model="serverConfig.username" placeholder="mainalc" />
                        </el-form-item>
                        <el-form-item label="密码">
                            <el-input v-model="serverConfig.password" type="password" placeholder="密码" />
                        </el-form-item>
                        <el-form-item label="连接方式">
                            <el-radio-group v-model="serverConfig.useHttps">
                                <el-radio :label="true">HTTPS (推荐)</el-radio>
                                <el-radio :label="false">HTTP</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                type="primary"
                                @click="handleLogin()"
                                :loading="connecting"
                                :disabled="isConnected"
                            >
                                {{ isConnected ? '已连接' : '连接' }}
                            </el-button>
                            <el-button
                                @click="handleDisconnect()"
                                :disabled="!isConnected"
                            >
                                断开
                            </el-button>
                            <el-button
                                type="warning"
                                @click="handleDiagnose()"
                                :loading="diagnosing"
                            >
                                连接诊断
                            </el-button>
                            <el-button
                                type="info"
                                @click="toggleDebugMode()"
                            >
                                {{ debugMode ? '停止调试' : '开启调试' }}
                            </el-button>
                            <el-button
                                type="success"
                                @click="testHttpsConnection()"
                                :loading="testingHttps"
                            >
                                测试HTTPS
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </el-col>
        </el-row>

        <el-row :gutter="20" class="mb-4">
            <el-col :span="24">
                <el-card>
                    <template #header>
                        <span>设备控制</span>
                    </template>

                    <el-button-group>
                        <el-button
                            type="primary"
                            @click="testMicrophone()"
                            :loading="testingMic"
                        >
                            测试麦克风
                        </el-button>
                        <el-button @click="startBroadcast()" :disabled="!isConnected || !microphoneReady">开始广播</el-button>
                        <el-button @click="stopAll()" :disabled="!isConnected">停止所有</el-button>
                    </el-button-group>

                    <!-- 快速音量控制 -->
                    <div style="margin-top: 16px;">
                        <el-row :gutter="16">
                            <el-col :span="12">
                                <div class="volume-control-item">
                                    <span>麦克风音量:</span>
                                    <el-button-group size="small">
                                        <el-button @click="adjustMicVolume(-10)" :disabled="!isConnected">-</el-button>
                                        <el-button @click="adjustMicVolume(10)" :disabled="!isConnected">+</el-button>
                                    </el-button-group>
                                    <span>{{ serverConfig.micVolume }}%</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="volume-control-item">
                                    <span>广播音量:</span>
                                    <el-button-group size="small">
                                        <el-button @click="adjustBroadcastVolume(-10)" :disabled="!isConnected">-</el-button>
                                        <el-button @click="adjustBroadcastVolume(10)" :disabled="!isConnected">+</el-button>
                                    </el-button-group>
                                    <span>{{ serverConfig.broadcastVolume }}%</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <div v-if="microphoneStatus" class="microphone-status" style="margin-top: 10px;">
                        <el-tag :type="microphoneStatus.type">
                            {{ microphoneStatus.message }}
                        </el-tag>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>

    <IntercomPanel></IntercomPanel>
  </basic-container>
</template>

<script>
import intercomAPI from '@/api/intercom/broadcast.js';
import { BroadcastType } from '@/api/intercom/types.js';
import { quickDiagnose } from '@/api/intercom/diagnostics.js';
import { startWebSocketMonitoring, stopWebSocketMonitoring, getWebSocketLogs } from '@/api/intercom/debug.js';
import { quickHttpsTest } from '@/api/intercom/https-config.js';
import IntercomPanel from '@/components/IntercomPanel.vue'

export default {
    components: {
        IntercomPanel
    },

    data() {
        return {
            currentDeviceId: 1001,
            isConnected: false,
            connecting: false,
            diagnosing: false,
            debugMode: false,
            testingHttps: false,
            testingMic: false,
            microphoneReady: false,
            microphoneStatus: null,
            serverConfig: {
                address: '************',
                // address: '***********',
                username: 'mainalc',
                password: '123456789',
                useHttps: true,
                dataPort: 8088,
                mediaPort: 8088,
                timeout: 30000,
                micVolume: 80,        // 麦克风音量
                broadcastVolume: 80   // 广播音量
            }
        };
    },

    computed: {
        connectionStatusType() {
            if (this.isConnected) return 'success';
            if (this.connecting) return 'warning';
            return 'danger';
        },

        connectionStatusText() {
            if (this.isConnected) return '已连接';
            if (this.connecting) return '连接中...';
            return '未连接';
        }
    },

    methods: {

        onLogin(result) {
            console.log(result,'✅ 连接成功');
            if (result.error === 'success') {
                this.isConnected = true;
                this.$message.success('连接成功');
            } else {
                this.$message.error('连接失败: ' + result.error);
            }
        },
        async handleLogin() {
            if (!this.serverConfig.address || !this.serverConfig.username || !this.serverConfig.password) {
                this.$message.error('请填写完整的服务器连接信息');
                return;
            }

            this.connecting = true;
            await this.initializeIntercom();
        },

        async initializeIntercom() {
            try {
                // 初始化SDK
                console.log('🔄 正在初始化SDK...');
                const initResult = await intercomAPI.initSDK();
                if (!initResult) {
                    throw new Error('SDK初始化失败，请检查dhsdk.min.js是否正确加载');
                }
                console.log('✅ SDK初始化成功');

                // 设置事件监听
                intercomAPI.on('onLogin', this.onLogin);
                intercomAPI.on('onDeviceList', this.handleDeviceList);
                intercomAPI.on('onNotify', this.handleDeviceNotify);
                intercomAPI.on('onError', this.handleSDKError);
                intercomAPI.on('onServerClosed', this.handleServerClosed);
                console.log('✅ 事件监听器已设置');

                // 登录服务器
                console.log(`🔄 正在连接服务器: ${this.serverConfig.address} (${this.serverConfig.useHttps ? 'HTTPS' : 'HTTP'})`);
                await intercomAPI.login(
                    this.serverConfig.username,
                    this.serverConfig.password,
                    this.serverConfig.address,
                    {
                        useHttps: this.serverConfig.useHttps,
                        dataPort: this.serverConfig.dataPort,
                        mediaPort: this.serverConfig.mediaPort,
                        timeout: this.serverConfig.timeout,
                        retryCount: 2,
                        retryDelay: 3000
                    }
                );

                this.isConnected = true;
                console.log('✅ 服务器连接成功');
                this.$message.success('对讲系统连接成功');

            } catch (error) {
                console.error('❌ 连接失败详情:', error);
                this.isConnected = false;

                // 提供更详细的错误信息
                let errorMessage = error.message;
                if (error.message.includes('WebSocket')) {
                    errorMessage += '\n\n建议：\n1. 检查服务器地址是否正确\n2. 确认服务器正在运行\n3. 检查网络连接\n4. 点击"连接诊断"获取详细信息';
                }

                this.$message.error('对讲系统连接失败: ' + errorMessage);
            } finally {
                this.connecting = false;
            }
        },

        // 断开连接
        async handleDisconnect() {
            try {
                if (this.isConnected) {
                    await intercomAPI.logout();
                    console.log('✅ 已断开服务器连接');
                }

                this.isConnected = false;
                this.$message.success('已断开连接');
            } catch (error) {
                console.error('❌ 断开连接失败:', error);
                this.$message.error('断开连接失败: ' + error.message);
            }
        },

        // 连接诊断
        async handleDiagnose() {
            if (!this.serverConfig.address) {
                this.$message.error('请先输入服务器地址');
                return;
            }

            this.diagnosing = true;
            try {
                console.log('🔍 开始连接诊断...');
                this.$message.info('正在进行连接诊断，请查看控制台输出...');

                const report = await quickDiagnose(this.serverConfig.address);

                // 显示诊断结果摘要
                const { summary } = report;
                if (summary.error > 0) {
                    this.$message.error(`诊断完成：发现 ${summary.error} 个错误，${summary.warning} 个警告。请查看控制台获取详细信息。`);
                } else if (summary.warning > 0) {
                    this.$message.warning(`诊断完成：发现 ${summary.warning} 个警告。请查看控制台获取详细信息。`);
                } else {
                    this.$message.success('诊断完成：所有检查项目都通过了！');
                }

            } catch (error) {
                console.error('❌ 诊断失败:', error);
                this.$message.error('诊断失败: ' + error.message);
            } finally {
                this.diagnosing = false;
            }
        },

        // 切换调试模式
        toggleDebugMode() {
            if (this.debugMode) {
                // 停止调试
                stopWebSocketMonitoring();
                this.debugMode = false;
                this.$message.info('调试模式已关闭');

                // 导出调试日志
                const logs = getWebSocketLogs();
                console.log('📊 调试会话结束，日志导出:', logs);

                if (logs.errors.length > 0) {
                    this.$message.warning(`调试期间发现 ${logs.errors.length} 个错误，请查看控制台`);
                }
            } else {
                // 开始调试
                startWebSocketMonitoring();
                this.debugMode = true;
                this.$message.success('调试模式已开启，WebSocket消息将被监控');
                console.log('🔍 调试模式已开启，所有WebSocket消息将被记录和分析');
            }
        },

        // 测试HTTPS连接
        async testHttpsConnection() {
            if (!this.serverConfig.address) {
                this.$message.error('请先输入服务器地址');
                return;
            }

            this.testingHttps = true;
            try {
                console.log('🔒 开始HTTPS连接测试...');
                this.$message.info('正在测试HTTPS连接，请查看控制台输出...');

                const { testResults, advice } = await quickHttpsTest(
                    this.serverConfig.address,
                    this.serverConfig.dataPort
                );

                // 显示测试结果
                if (testResults.httpsSupported) {
                    if (testResults.certificateValid) {
                        this.$message.success('✅ HTTPS连接测试成功，证书有效');
                    } else {
                        this.$message.warning('⚠️ HTTPS连接可用但证书无效，建议检查证书配置');
                    }
                } else if (testResults.httpSupported) {
                    this.$message.warning('⚠️ 仅支持HTTP连接，建议升级到HTTPS');
                } else {
                    this.$message.error('❌ 连接测试失败，请检查服务器状态');
                }

                // 应用建议的配置
                const successAdvice = advice.find(a => a.type === 'success' || a.type === 'warning');
                if (successAdvice && successAdvice.config) {
                    Object.assign(this.serverConfig, successAdvice.config);
                    this.$message.info('已自动应用推荐的连接配置');
                }

            } catch (error) {
                console.error('❌ HTTPS测试失败:', error);
                this.$message.error('HTTPS测试失败: ' + error.message);
            } finally {
                this.testingHttps = false;
            }
        },

        // 测试麦克风
        async testMicrophone() {
            this.testingMic = true;
            this.microphoneStatus = null;

            try {
                console.log('🎤 开始麦克风测试...');

                // 检查浏览器支持
                if (!navigator.mediaDevices && !navigator.getUserMedia &&
                    !navigator.webkitGetUserMedia && !navigator.mozGetUserMedia) {
                    throw new Error('浏览器不支持麦克风访问，请使用Chrome、Firefox或Edge最新版本');
                }

                // 检查HTTPS协议
                if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                    this.microphoneStatus = {
                        type: 'warning',
                        message: '⚠️ 建议使用HTTPS协议以确保麦克风正常工作'
                    };
                }

                // 尝试获取麦克风权限
                let stream;
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // 使用现代API
                    stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    });
                } else {
                    // 使用旧版API
                    const getUserMedia = navigator.getUserMedia ||
                                        navigator.webkitGetUserMedia ||
                                        navigator.mozGetUserMedia;

                    stream = await new Promise((resolve, reject) => {
                        getUserMedia.call(navigator,
                            { audio: true },
                            resolve,
                            reject
                        );
                    });
                }

                // 测试音频流
                let audioContext;
                try {
                    const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
                    if (!AudioContextClass) {
                        throw new Error('浏览器不支持音频处理');
                    }
                    audioContext = new AudioContextClass();
                } catch (error) {
                    throw new Error('浏览器不支持音频处理');
                }
                const source = audioContext.createMediaStreamSource(stream);
                const analyser = audioContext.createAnalyser();
                source.connect(analyser);

                // 检测音频输入
                const dataArray = new Uint8Array(analyser.frequencyBinCount);
                let hasAudio = false;

                const checkAudio = () => {
                    analyser.getByteFrequencyData(dataArray);
                    const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
                    if (average > 0) {
                        hasAudio = true;
                    }
                };

                // 检测2秒钟
                const checkInterval = setInterval(checkAudio, 100);
                await new Promise(resolve => setTimeout(resolve, 2000));
                clearInterval(checkInterval);

                // 停止音频流
                stream.getTracks().forEach(track => track.stop());
                audioContext.close();

                // 显示结果
                this.microphoneReady = true;
                this.microphoneStatus = {
                    type: 'success',
                    message: hasAudio ?
                        '✅ 麦克风测试成功，检测到音频输入' :
                        '✅ 麦克风权限正常，但未检测到音频输入（请检查麦克风是否静音）'
                };

                this.$message.success('麦克风测试完成，可以开始广播');

            } catch (error) {
                console.error('❌ 麦克风测试失败:', error);

                this.microphoneReady = false;
                let errorMessage = '麦克风测试失败';
                let statusType = 'danger';

                // 分析错误类型
                if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                    errorMessage = '❌ 麦克风权限被拒绝，请点击地址栏左侧的锁图标允许麦克风访问';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = '❌ 未找到麦克风设备，请检查麦克风是否正确连接';
                } else if (error.name === 'NotReadableError') {
                    errorMessage = '❌ 麦克风被其他应用占用，请关闭其他音频应用';
                } else if (error.name === 'SecurityError') {
                    errorMessage = '❌ 安全限制，请使用HTTPS协议访问页面';
                    statusType = 'warning';
                } else if (error.message.includes('浏览器不支持')) {
                    errorMessage = '❌ 浏览器不支持麦克风，请使用Chrome、Firefox或Edge最新版本';
                } else {
                    errorMessage = `❌ 麦克风错误: ${error.message}`;
                }

                this.microphoneStatus = {
                    type: statusType,
                    message: errorMessage
                };

                this.$message.error(errorMessage);
            } finally {
                this.testingMic = false;
            }
        },
        
        async startVideo() {
        try {
            await intercomAPI.playVideo(
            this.$refs.videoRef, 
            this.currentDeviceId, 
            true
            );
        } catch (error) {
            this.$message.error('视频播放失败: ' + error.message);
        }
        },
        
        async startBroadcast() {
        try {
            await intercomAPI.startBroadcast(
            [this.currentDeviceId],
            BroadcastType.MICROPHONE
            );
            this.$message.success('广播已开始');
        } catch (error) {
            this.$message.error('广播开始失败: ' + error.message);
        }
        },
        
        async stopAll() {
        try {
            await intercomAPI.stopVideo(this.currentDeviceId);
            await intercomAPI.stopBroadcast();
            this.$message.success('已停止所有操作');
        } catch (error) {
            this.$message.error('停止操作失败: ' + error.message);
        }
        },
        
        handleDeviceList(result) {
            console.log('设备列表更新:', result.params.list);
            
        },
        
        handleDeviceNotify(result) {
        const { deviceId, action } = result.params;
        console.log(`设备${deviceId}状态: ${action}`);
        },

        // 处理SDK错误
        handleSDKError(error) {
            console.error('SDK错误详情:', error);
            const { msg } = error;
            let errorMessage = '未知错误';
            let solution = '';

            // 检查是否是JSON解析错误
            if (error.toString().includes('JSON') || error.toString().includes('parse')) {
                errorMessage = 'JSON数据解析错误';
                solution = '这通常是由于服务器返回的数据格式不正确或网络传输中断导致的。建议：\n1. 检查服务器程序是否正常运行\n2. 检查网络连接稳定性\n3. 重新启动服务器程序';
            } else {
                switch (msg?.error) {
                    case 'loginTimeout':
                        errorMessage = '登录超时';
                        solution = '请检查网络连接和服务器状态';
                        break;
                    case 'socketError':
                        errorMessage = 'WebSocket连接异常';
                        solution = '请检查服务器地址、端口和防火墙设置';
                        break;
                    case 'dataTimeout':
                        errorMessage = '数据获取超时';
                        solution = '请检查网络连接速度';
                        break;
                    case 'fail':
                        errorMessage = '操作失败';
                        solution = '请检查服务器状态和网络连接';
                        break;
                    default:
                        errorMessage = msg?.error || '连接错误';
                        solution = '请尝试重新连接或联系技术支持';
                }
            }

            console.error(`❌ ${errorMessage}:`, solution);
            this.$message.error(`${errorMessage}${solution ? '\n\n' + solution : ''}`);
            this.isConnected = false;
        },

        // 处理服务器断开
        handleServerClosed() {
            console.warn('服务器连接断开');
            this.$message.warning('服务器连接已断开，请重新连接');
            this.isConnected = false;
        },

        // 调整麦克风音量
        async adjustMicVolume(delta) {
            if (!this.isConnected || !this.currentDeviceId) {
                this.$message.error('请先连接服务器');
                return;
            }

            const newVolume = Math.max(0, Math.min(100, this.serverConfig.micVolume + delta));
            this.serverConfig.micVolume = newVolume;

            try {
                const requestId = intercomAPI.generateRequestId();
                await intercomAPI.setDeviceVolume(
                    requestId,
                    this.currentDeviceId,
                    'input',
                    [newVolume]
                );

                console.log(`✅ 麦克风音量已设置为: ${newVolume}%`);
                this.$message.success(`麦克风音量已调整为: ${newVolume}%`);
            } catch (error) {
                console.error('❌ 设置麦克风音量失败:', error);
                this.$message.error('设置麦克风音量失败: ' + error.message);
            }
        },

        // 调整广播音量
        async adjustBroadcastVolume(delta) {
            if (!this.isConnected || !this.currentDeviceId) {
                this.$message.error('请先连接服务器');
                return;
            }

            const newVolume = Math.max(0, Math.min(100, this.serverConfig.broadcastVolume + delta));
            this.serverConfig.broadcastVolume = newVolume;

            try {
                const requestId = intercomAPI.generateRequestId();
                await intercomAPI.setBroadcastVolume(
                    requestId,
                    this.currentDeviceId,
                    [newVolume]
                );

                console.log(`✅ 广播音量已设置为: ${newVolume}%`);
                this.$message.success(`广播音量已调整为: ${newVolume}%`);
            } catch (error) {
                console.error('❌ 设置广播音量失败:', error);
                this.$message.error('设置广播音量失败: ' + error.message);
            }
        },

        // 音量减（保留原有方法）
        async handleVolumeDown() {
            if (!this.isConnected || !this.currentDeviceId) {
                this.$message.error('请先连接服务器并选择设备');
                return;
            }

            try {
                const requestId = intercomAPI.generateRequestId();
                // 获取当前广播音量
                const volumeResult = await intercomAPI.getBroadcastVolume(requestId, this.currentDeviceId);
                console.log('当前广播音量:', volumeResult);

                // 根据实际返回的数据结构调整，这里使用默认值
                let currentVolume = this.serverConfig.broadcastVolume || 50;
                if (volumeResult.params && volumeResult.params.volume && volumeResult.params.volume.length > 0) {
                    currentVolume = volumeResult.params.volume[0];
                }

                const newVolume = Math.max(0, currentVolume - 10);

                await intercomAPI.setBroadcastVolume(requestId + 1, this.currentDeviceId, [newVolume]);
                this.serverConfig.broadcastVolume = newVolume;
                this.$message.success(`广播音量已调整为: ${newVolume}%`);
            } catch (error) {
                this.$message.error('音量调节失败: ' + error.message);
            }
        },

        // 音量加
        async handleVolumeUp() {
            if (!this.isConnected || !this.currentDeviceId) {
                this.$message.error('请先连接服务器并选择设备');
                return;
            }

            try {
                const requestId = intercomAPI.generateRequestId();
                // 获取当前广播音量
                const volumeResult = await intercomAPI.getBroadcastVolume(requestId, this.currentDeviceId);
                console.log('当前广播音量:', volumeResult);

                // 根据实际返回的数据结构调整，这里使用默认值
                let currentVolume = this.serverConfig.broadcastVolume || 50;
                if (volumeResult.params && volumeResult.params.volume && volumeResult.params.volume.length > 0) {
                    currentVolume = volumeResult.params.volume[0];
                }

                const newVolume = Math.min(100, currentVolume + 10);

                await intercomAPI.setBroadcastVolume(requestId + 1, this.currentDeviceId, [newVolume]);
                this.serverConfig.broadcastVolume = newVolume;
                this.$message.success(`广播音量已调整为: ${newVolume}%`);
            } catch (error) {
                this.$message.error('音量调节失败: ' + error.message);
            }
        },

        cleanup() {
        if (this.isConnected) {
            intercomAPI.logout();
            intercomAPI.destroy();
        }
        }
    }
};
</script>

<style scoped>
.control-panel {
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.el-card {
  margin-bottom: 16px;
}

.el-button-group .el-button:disabled {
  opacity: 0.5;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-control-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.volume-control-item span:first-child {
  min-width: 80px;
  font-size: 14px;
  color: #606266;
}

.volume-control-item span:last-child {
  min-width: 40px;
  font-weight: 500;
  color: #409eff;
}
</style>
