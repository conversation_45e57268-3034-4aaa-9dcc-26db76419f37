<template>
  <div class="container">
    <div style="width: 100%; display: flex; justify-content: flex-end; margin-bottom: 16px;">
      <el-button color="#165DFF"  type="primary" @click="addGuanggan">新 增</el-button>
    </div>
    <!-- 表格 -->
    <el-table 
      :data="tableData" 
      style="width: 100%"
      :header-cell-style="{ background: '#F7F8FA' }"
    >
      <el-table-column prop="status" label="执行状态" align="center">
        <template #default>
          <span>开</span>
        </template>
      </el-table-column>
      <el-table-column prop="policyStatus" label="策略状态" align="center" width="180">
        <template #default="scope">
          <el-switch
            v-model="scope.row.policyStatus"
            active-color="#165DFF"
            :active-value="1"
            :inactive-value="2"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="businessLight" label="亮度值" align="center" />
      <el-table-column label="操作" align="center" width="180">
        <template #default="scope">
          <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部说明文本 -->
    <div class="bottom-text">
      系统会实时读取各通道的光照传感器数值。
      <br />
      当数值位于0到200 Lux之间时,环境光比较微弱，可认为是夜晚；
      <br />
      当数值位于200到1000 Lux之间时,环境光比较微弱，属于日出或日落的黎明/黄昏过渡期；
      <br />
      当数值位于数值大于1000 Lux时，说明光照已接近或达到传感器量程上限，可视为白天。
    </div>

    <!-- 新增/查看弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增' : '查看'"
      width="640px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="custom-dialog"
    >
      <AddGuanggan 
        :readonly="dialogType === 'view'"
        :form-data="currentRow"
        @submit="handleSubmit" 
        @cancel="handleCancel" 
        v-if="dialogVisible"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import AddGuanggan from './AddGuanggan.vue'
import { getTimeData, addTimeStrategy, removeTimeStrategy, controlStrategy } from "@/api/lamp/lightEnvironment.js"

const tableData = ref([])

const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'view'
const currentRow = ref(null)

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})
// 获取表格数据
const fetchTableData = async () => {
  try {
    const res = await getTimeData({type: 1,deviceId: props.item.deviceId})
    if (res.data.code === 200) {
      tableData.value = res.data.data
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 在组件挂载时获取数据
onMounted(() => {
  fetchTableData()
})

const handleSwitchChange = (row) => {
  console.log('切换状态:', row)
  const statusText = row.policyStatus === 1 ? '启用' : '停用'
  ElMessageBox.confirm(
    `确认要${statusText}该策略吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 用户确认后，改变状态并调用接口
    let rows = {
      id: row.id,
      status: row.policyStatus
    }
    controlStrategy(rows).then(res => {
      ElMessage({
        type: 'success',
        message: `${statusText}成功`,
      })
    }).catch(() => {
      // 失败时恢复状态
      row.policyStatus = row.policyStatus === 1 ? 2 : 1
      ElMessage({
        type: 'error',
        message: `${statusText}失败`,
      })
    })
  }).catch(() => {
    // 用户取消操作，状态已经在开始时改回原值了
  })
}

const handleView = (row) => {
  row.loopNums = row.loopNums.split(',')
  row.centralizedNo = row.centralizedNo.split(',')
  dialogType.value = 'view'
  currentRow.value = { ...row } // 创建数据副本
  dialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确认删除该记录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await removeTimeStrategy(row.id)
      if (res.data.code === 200) {
        ElMessage.success('删除成功')
        fetchTableData() // 重新获取表格数据
      } else {
        // ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      // ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

const addGuanggan = () => {
  dialogType.value = 'add'
  currentRow.value = null
  dialogVisible.value = true
}

const handleSubmit = (data) => {
  console.log('提交数据:', data)
  data.type = 1
  data.deviceId = props.item.deviceId
  addTimeStrategy(data).then(res => {
    if (res.data.code === 200) {
      ElMessage.success('新增成功')
      fetchTableData()
      // 这里处理提交逻辑
      dialogVisible.value = false
    }else{
      ElMessage.error(res.msg || '新增失败')
    }
  })
  
}

const handleCancel = () => {
  dialogVisible.value = false
  currentRow.value = null
}
</script>

<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

.bottom-text {
  margin-top: 16px;
  padding: 16px;
  background: #F7F8FA;
  border-radius: 8px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

:deep(.el-table) {
  // 表格边框和分割线颜色
  --el-table-border-color: #F2F3F5;
  --el-table-border: 1px solid #F2F3F5;
  
  // 表头样式
  th {
    font-weight: 500;
    color: #1D2129;
    background: #F7F8FA !important;
  }
  
  // 单元格样式
  td {
    padding: 12px 0;
  }

  // 按钮样式
  .el-button {
    padding: 0;
    margin: 0 8px;
    font-size: 14px;
    
    &.el-button--primary {
      color: #165DFF;
    }
    
    &.el-button--danger {
      color: #F53F3F;
    }
  }
}

:deep(.el-switch) {
  --el-switch-on-color: #165DFF;
}

// 表格hover效果
:deep(.el-table__row:hover) {
  td {
    background-color: #F2F3F5 !important;
  }
}

// 选中行的样式
:deep(.el-table__row.current-row) {
  td {
    background-color: #F2F3F5 !important;
  }
}

:deep(.custom-dialog) {
  .el-dialog__header {
    margin: 0;
    .el-dialog__title {
      font-family: Source Han Sans;
      font-size: 20px;
      font-weight: 500;
      line-height: 24px;
      color: #1D2129;
    }
  }
  
  .el-dialog__body {
    padding: 0px 20px 0;
  }
}
</style>
