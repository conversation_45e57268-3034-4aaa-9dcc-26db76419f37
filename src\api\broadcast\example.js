/**
 * Chrome SDK 使用示例
 * 展示如何使用Chrome SDK接口
 */
import chromeSDKManager from './index.js';

/**
 * Chrome SDK 使用示例类
 */
class ChromeSDKExample {
  constructor() {
    this.sdkManager = chromeSDKManager;
    this.dhWeb = null;
  }

  /**
   * 初始化SDK示例
   */
  async initSDK() {
    try {
      // 假设DHAlarmWeb已经加载
      if (typeof DHAlarmWeb !== 'undefined') {
        this.dhWeb = new DHAlarmWeb();
        this.sdkManager.init(this.dhWeb);
        console.log('Chrome SDK初始化成功');
      } else {
        throw new Error('DHAlarmWeb未加载');
      }
    } catch (error) {
      console.error('SDK初始化失败:', error);
      throw error;
    }
  }

  /**
   * 登录示例
   */
  async loginExample() {
    try {
      // 设置登录回调
      this.sdkManager.getLoginManager().onLoginSuccess((result) => {
        console.log('登录成功回调:', result);
      });

      this.sdkManager.getLoginManager().onErrorCallback((error) => {
        console.error('登录错误回调:', error);
      });

      // 执行登录
      const result = await this.sdkManager.quickLogin('mainalc', 'password', '192.168.1.100');
      console.log('登录结果:', result);
      return result;
    } catch (error) {
      console.error('登录示例失败:', error);
      throw error;
    }
  }

  /**
   * 设备列表回调示例
   */
  setupDeviceListCallback() {
    this.sdkManager.on('onDeviceList', (result) => {
      console.log('设备列表更新:', result);
      if (result.params && result.params.list) {
        result.params.list.forEach(device => {
          console.log(`设备: ${device.deviceName}, ID: ${device.deviceId}, 状态: ${device.action}`);
        });
      }
    });
  }

  /**
   * 设备通知回调示例
   */
  setupDeviceNotifyCallback() {
    this.sdkManager.on('onNotify', (result) => {
      console.log('设备通知:', result);
      if (result.params) {
        const { code, deviceId, action } = result.params;
        console.log(`通知类型: ${code}, 设备ID: ${deviceId}, 动作: ${action}`);
      }
    });
  }

  /**
   * 播放视频示例
   */
  async playVideoExample(deviceId) {
    try {
      // 创建视频元素
      const video = document.createElement('video');
      video.width = 640;
      video.height = 480;
      video.controls = true;
      document.body.appendChild(video);

      // 播放视频
      await this.sdkManager.quickPlayVideo(video, deviceId, false);
      console.log('视频播放成功');
    } catch (error) {
      console.error('播放视频失败:', error);
      throw error;
    }
  }

  /**
   * 广播示例
   */
  async broadcastExample() {
    try {
      // 获取音频文件列表
      const audioFiles = await this.sdkManager.quickGetAudioFileList();
      console.log('音频文件列表:', audioFiles);

      // 开始文件广播
      const deviceIds = [12345, 67890]; // 示例设备ID
      const broadcastType = chromeSDKManager.BROADCAST_TYPE.FILE;
      const filePath = '/path/to/audio/file.mp3';

      await this.sdkManager.quickStartBroadcast(deviceIds, broadcastType, filePath);
      console.log('广播开始成功');

      // 5秒后停止广播
      setTimeout(async () => {
        try {
          await this.sdkManager.quickStopBroadcast();
          console.log('广播停止成功');
        } catch (error) {
          console.error('停止广播失败:', error);
        }
      }, 5000);
    } catch (error) {
      console.error('广播示例失败:', error);
      throw error;
    }
  }

  /**
   * 广播任务管理示例
   */
  async broadcastTaskExample() {
    try {
      // 获取广播任务列表
      const taskList = await this.sdkManager.quickGetBCTaskList('timed');
      console.log('广播任务列表:', taskList);

      // 创建新的广播任务
      const taskConfig = {
        taskName: '测试广播任务',
        volume: 50,
        deviceIds: [12345, 67890],
        fileIds: [1, 2],
        enable: true,
        execMode: 'Single',
        weekDay: [],
        startTimeOfDay: '10:00:00',
        expireObj: {
          enable: true,
          beginDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        timeMode: 'times',
        modeContent: 1,
        taskType: 'timed',
        bcType: 'file'
      };

      const uploadResult = await this.sdkManager.getBroadcastManager().uploadBCTask(taskConfig);
      console.log('广播任务创建成功:', uploadResult);
    } catch (error) {
      console.error('广播任务管理示例失败:', error);
      throw error;
    }
  }

  /**
   * 设备管理示例
   */
  async deviceManagementExample() {
    try {
      // 获取设备额外信息
      const deviceExtra = await this.sdkManager.quickGetDeviceExtra(0);
      console.log('设备额外信息:', deviceExtra);

      // 设置设备坐标
      const deviceId = 12345;
      const location = '118.6691_36.1341';
      const contact = '张三';
      const phone = '13012345678';
      const remark = '测试设备';

      await this.sdkManager.getDeviceManager().setDeviceExtra(deviceId, location, contact, phone, remark);
      console.log('设备额外信息设置成功');

      // 获取设备音量
      const volumeResult = await this.sdkManager.getDeviceManager().getDeviceVolume(deviceId, 'output');
      console.log('设备音量:', volumeResult);
    } catch (error) {
      console.error('设备管理示例失败:', error);
      throw error;
    }
  }

  /**
   * 分组用户管理示例
   */
  async groupUserManagementExample() {
    try {
      // 获取用户列表
      const userList = await this.sdkManager.getGroupUserManager().getUsers();
      console.log('用户列表:', userList);

      // 添加新分组
      const groupResult = await this.sdkManager.getGroupUserManager().addGroup(
        'normal',
        '测试分组',
        '联系人',
        '13012345678'
      );
      console.log('分组添加成功:', groupResult);

      // 添加新用户
      const userResult = await this.sdkManager.getGroupUserManager().addUser('testuser', 'password123');
      console.log('用户添加成功:', userResult);
    } catch (error) {
      console.error('分组用户管理示例失败:', error);
      throw error;
    }
  }

  /**
   * 系统管理示例
   */
  async systemManagementExample() {
    try {
      // 获取系统配置
      const systemConfig = await this.sdkManager.getSystemManager().getSystemCfg();
      console.log('系统配置:', systemConfig);

      // 搜索日志
      const endTime = Date.now();
      const beginTime = endTime - 24 * 60 * 60 * 1000; // 24小时前
      const logResult = await this.sdkManager.getSystemManager().searchLog(beginTime, endTime, 0, 20);
      console.log('日志搜索结果:', logResult);
    } catch (error) {
      console.error('系统管理示例失败:', error);
      throw error;
    }
  }

  /**
   * 完整使用流程示例
   */
  async fullWorkflowExample() {
    try {
      console.log('=== Chrome SDK 完整使用流程示例 ===');

      // 1. 初始化SDK
      await this.initSDK();

      // 2. 设置回调
      this.setupDeviceListCallback();
      this.setupDeviceNotifyCallback();

      // 3. 登录
      await this.loginExample();

      // 4. 等待一段时间让设备列表加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 5. 执行各种操作
      await this.deviceManagementExample();
      await this.broadcastTaskExample();
      await this.groupUserManagementExample();
      await this.systemManagementExample();

      console.log('=== 完整流程示例执行完成 ===');
    } catch (error) {
      console.error('完整流程示例失败:', error);
      throw error;
    }
  }

  /**
   * 错误处理示例
   */
  setupErrorHandling() {
    // 设置全局错误回调
    this.sdkManager.getLoginManager().onErrorCallback((error) => {
      console.error('SDK错误:', error);
      
      // 根据错误类型进行处理
      if (error.msg && error.msg.error === 'loginTimeout') {
        console.log('登录超时，需要重新登录');
        // 可以在这里触发重新登录逻辑
      }
    });

    // 设置服务器断开回调
    this.sdkManager.getLoginManager().onLogoutCallback(() => {
      console.log('服务器连接断开，需要重新连接');
      // 可以在这里触发重连逻辑
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    try {
      // 登出
      this.sdkManager.quickLogout();
      
      // 清理回调
      this.sdkManager.off('onDeviceList');
      this.sdkManager.off('onNotify');
      
      console.log('资源清理完成');
    } catch (error) {
      console.error('资源清理失败:', error);
    }
  }
}

// 导出示例类
export default ChromeSDKExample;

// 使用示例
/*
// 在Vue组件或其他地方使用
import ChromeSDKExample from '@/api/broadcast/example.js';

const example = new ChromeSDKExample();

// 执行完整流程示例
example.fullWorkflowExample().catch(console.error);

// 或者单独执行某个示例
example.loginExample().then(() => {
  return example.broadcastExample();
}).catch(console.error);
*/
