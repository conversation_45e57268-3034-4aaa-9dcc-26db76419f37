<template>
  <div class="editBox">
    <div class="item-box" v-for="(item,index) in ForLoopList" :key="index">
        <div class="item-box-hide">
            <div class="item-box-title">
                {{ item.loopName }}
            </div>
            <div class="item-box-addBtn">
                <el-button
                    icon="Plus" 
                    style="font-size: 20px; color: #333;font-weight: 600;"  
                    type="primary" 
                    text 
                    circle
                    @click="addlight(item)" />
            </div>
        </div>
        <div class="item-box-content">
            <el-table :data="item.lightPoles" style="width: 100%; height: 100%;">
                <el-table-column
                 prop="poleNo" 
                 label="灯杆编号"
                 width="100"
                 show-overflow-tooltip
                 />
                <el-table-column
                 prop="poleName"
                 label="灯杆名称" 
                 width="84"
                 show-overflow-tooltip/>
            </el-table>
        </div>
        <div class="item-box-more">
            <el-button
                type="primary"
                text
                v-if="item.lightPoles.length>3"
                @click="viewList(item)">
                查看更多
            </el-button>
        </div>
    </div>
  </div>
  <div class="paging-box">
    <el-pagination
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        background
        layout="prev, pager, next"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
  </div>
</template>

<script setup>
import { ref, defineEmits, onMounted, defineProps} from 'vue'
import { getStem, QueryIndicatorStem, leLightCentralStem } from '@/api/lamp/switchBox'

const tableData = ref( [
  {
    date: 'K892812934',
    name: 'Tom',
  },
  {
    date: 'K892812935',
    name: 'Tom',
  },
  {
    date: 'K892812935',
    name: 'Tom',
  },
])

const emit = defineEmits(['showViewBox', 'showAddLight', 'handleClose', 'submit'])
const viewList = (item) => {
  console.log(item)
  emit('showViewBox',item)
}

const addlight = (item) => {
  console.log(item)
  emit('showAddLight',item)
}

const page = ref(
  {
    pageSize: 10,
    currentPage: 1,
    total: 0,
  },
)

const handleSizeChange = (val) => {
  getList(page.value.currentPage, val, {})
}

const handleCurrentChange=(val)=>{
  getList(val, page.value.pageSize, {})
}

const props = defineProps({
  editItemData:{
    type: Object,
    default: () => ({})
  }
})

const ForLoopList = ref([])
const getList = (currentPage,pageSize) => {
  console.log(props.editItemData)
  getStem(currentPage, pageSize,{centralizedNo:props.editItemData.deviceNo}).then(res => {
    console.log(res,"deviceNodeviceNodeviceNo")
    ForLoopList.value = res.data.data.records
    page.value.total = res.data.data.total
    page.value.currentPage = res.data.data.current
    page.value.pageSize = res.data.data.size
    console.log(ForLoopList.value,"111111111111111")
  })
}

onMounted(() => {
  getList(page.value.currentPage, page.value.pageSize)
})
</script>
<style lang="scss" scoped>
*{
  box-sizing: border-box;
}
.editBox{
    width: 100%;
    height: 560px;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(5, 240px);
    grid-gap: 10px;
}
.item-box{
    width: 220px;
    height: 260px;
    padding: 6px 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border: 1px solid rgba(0, 0, 0, 0.1);
}
.item-box-hide{
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    .item-box-title{
        line-height: 34px;
        display: flex;
        align-items: center;
    }
    .item-box-title::before{
        content: "";
        display: inline-block;
        width: 4px;
        height: 24px;
        border-radius: 81px;
        background: #2E74FF;
        margin-right: 6px;
    }
    .item-box-addBtn{
        font-size: 22px;
    }
}
.item-box-content{
    height: 160px;
}
.item-box-more{
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

.paging-box{
  width: 100%;
  display: flex;
  margin-top: 16px;
  justify-content: flex-end;
}

</style>