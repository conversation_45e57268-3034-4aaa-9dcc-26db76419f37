<template>
    <div class="real-time-broadcast">
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧文件广播卡片 -->
            <div class="broadcast-card" :class="{
                active: activeCard === '2',
                disabled: activeCard !== '2',
                'broadcast-disabled': isPlaying && activeCard !== '2'
            }" @click="selectCard('2')">
                <div class="card-header">
                    <h3 class="card-title">文件广播</h3>
                    <div class="card-indicator" :class="{ active: activeCard === '2' }"></div>
                </div>

                <div class="card-content">
                    <!-- 文件选择 -->
                    <div class="setting-item" @click.stop>
                        <label class="setting-label">文件</label>
                        <div class="file-select" @click.stop>
                            <el-select
                                v-model="selectedFile"
                                placeholder="请选择文件"
                                style="width: 200px;"
                                :disabled="activeCard !== '2'"
                                @click.stop
                            >
                                <el-option label="请选择文件" value=""></el-option>
                                <el-option label="系统提示音.mp3" value="system"></el-option>
                                <el-option label="警报声.wav" value="alarm"></el-option>
                            </el-select>
                            <el-button
                                type="primary"
                                class="upload-btn"
                                :disabled="activeCard !== '2'"
                                @click.stop="handleUploadFile"
                            >
                                选择本地文件
                            </el-button>
                            <input
                                ref="fileInput"
                                type="file"
                                accept="audio/*,.mp3,.wav,.m4a"
                                style="display: none"
                                @change="handleFileChange"
                            />
                        </div>
                    </div>

                    <!-- 循环设置 -->
                    <div class="setting-item" @click.stop>
                        <label class="setting-label">循环</label>
                        <div class="loop-controls" @click.stop>
                            <el-radio-group
                                v-model="loopMode"
                                size="small"
                                :disabled="activeCard !== '2'"
                                @click.stop
                            >
                                <el-radio label="infinite">无限循环</el-radio>
                                <el-radio label="count">次数</el-radio>
                            </el-radio-group>
                            <el-input-number
                                v-model="loopCount"
                                v-show="loopMode === 'count'"
                                :min="1"
                                :max="999"
                                size="small"
                                :disabled="activeCard !== '2'"
                                style="width: 80px; margin-left: 8px;"
                                placeholder="次数"
                                @click.stop
                            />
                        </div>
                    </div>

                    <!-- 广播音量 -->
                    <div class="setting-item" @click.stop>
                        <label class="setting-label">广播音量</label>
                        <div class="volume-control" @click.stop>
                            <el-slider
                                v-model="broadcastVolume"
                                :max="100"
                                style="width: 200px;"
                                :disabled="activeCard !== '2' || !currentDeviceId || isLoadingBroadcastVolume || !isPlaying"
                                @input="handleBroadcastVolumeInput"
                                @click.stop
                            />
                            <span class="volume-value" :class="{ loading: isLoadingBroadcastVolume }">
                                {{ isLoadingBroadcastVolume ? '...' : broadcastVolume }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧实时广播卡片 -->
            <div class="broadcast-card" :class="{
                active: activeCard === '1',
                disabled: activeCard !== '1',
                'broadcast-disabled': isPlaying && activeCard !== '1'
            }" @click="selectCard('1')">
                <div class="card-header">
                    <h3 class="card-title">麦克风广播</h3>
                    <div class="card-indicator" :class="{ active: activeCard === '1' }"></div>
                </div>

                <div class="card-content">
                    <!-- 广播音量 -->
                    <div class="setting-item" @click.stop>
                        <label class="setting-label">广播音量</label>
                        <div class="volume-control" @click.stop>
                            <el-slider
                                v-model="broadcastVolume"
                                :max="100"
                                style="width: 200px;"
                                :disabled="activeCard !== '1' || !currentDeviceId || isLoadingBroadcastVolume || !isPlaying"
                                @input="handleBroadcastVolumeInput"
                                @click.stop
                            />
                            <span class="volume-value" :class="{ loading: isLoadingBroadcastVolume }">
                                {{ isLoadingBroadcastVolume ? '...' : broadcastVolume }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧播放控制区域 -->
            <div class="right-panel">
                <!-- 时间显示 -->
                <div class="time-display">
                    {{ formatTime(currentTime) }}
                </div>

                <!-- 播放按钮 -->
                <div class="play-control" @click.stop>
                    <div
                        class="play-button"
                        @click.stop="togglePlay"
                        :class="{ disabled: selectedDevices.length === 0 }"
                    >
                        <el-icon class="play-icon" :class="{ playing: isPlaying }">
                            <VideoPlay v-if="!isPlaying" />
                            <VideoPause v-else />
                        </el-icon>
                    </div>
                    <div class="play-text">{{ isPlaying ? '停止广播' : '开始广播' }}</div>
                    <div class="device-count" v-if="selectedDevices.length > 0">
                        已选择 {{ selectedDevices.length }} 个设备
                    </div>
                </div>
            </div>
        </div>

        <!-- 广播设备列表 -->
        <div class="device-section" @click.stop>
            <div class="device-header">
                <h3 class="device-title">广播设备列表</h3>
                <div class="device-actions">
                    <!-- <el-button type="primary" size="small">选择设备</el-button> -->
                    <!-- <el-button type="danger" size="small">批量删除</el-button> -->
                </div>
            </div>

            <!-- 设备表格 -->
            <div class="device-table" @click.stop>
                <el-table
                    :data="deviceList"
                    style="flex: 1;"
                    size="small"
                    @selection-change="handleSelectionChange"
                    ref="deviceTable"
                >
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="deviceName" label="设备名称"></el-table-column>
                    <el-table-column prop="deviceType" label="设备类型">
                        <template #default="scope">
                            {{ scope.row.deviceType === 'Alarm' ? '报警设备' : '联动设备' }}
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="deviceGroup" label="设备分组"></el-table-column> -->
                    <el-table-column prop="deviceId" label="设备ID"></el-table-column>
                    <el-table-column prop="action" label="设备状态">
                        <template #default="scope">
                            <span :class="['status-tag', scope.row.deviceStatus === 'Offline' ? 'offline' : 'online']">
                                {{ scope.row.deviceStatus === 'Offline' ? '离线' : '在线' }}
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[5, 10, 20, 50]"
                        :total="total"
                        layout="prev, pager, next, sizes, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { VideoPlay, VideoPause } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import chromeSDKManager from '@/api/broadcast/index.js'



// 响应式数据
const activeCard = ref('2') // 当前激活的卡片：'1'(麦克风广播) 或 '2'(文件广播)
const selectedFile = ref('')
const loopMode = ref('infinite') // 循环模式：'infinite' 或 'count'
const loopCount = ref(1) // 循环次数
const broadcastVolume = ref(50)
const isPlaying = ref(false)
const currentTime = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 音量控制相关数据
const currentDeviceId = ref(null) // 当前选中的设备ID
const isLoadingBroadcastVolume = ref(false) // 广播音量加载状态
const volumeDebounceTimers = ref({
  broadcast: null
}) // 防抖定时器

// 设备列表数据
const deviceList = ref([])
const selectedDevices = ref([]) // 选中的设备列表
const deviceTable = ref(null) // 设备表格引用

// 文件上传相关
const fileInput = ref(null) // 文件输入引用
const uploadedFile = ref(null) // 上传的文件对象

// 时间格式化
const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 卡片选择控制
const selectCard = (cardType) => {
    // 如果正在广播，禁止切换并显示提示
    if (isPlaying.value) {
        ElMessage.warning('请先停止当前广播后再切换模式')
        return
    }

    // 如果已经是当前卡片，不需要切换
    if (activeCard.value === cardType) {
        return
    }

    activeCard.value = cardType
    console.log('切换到卡片:', cardType)
}

// 设备选择处理
const handleSelectionChange = (selection) => {
    selectedDevices.value = selection
    console.log('选中的设备:', selection)

    // 当选择设备时，记录第一个设备ID用于音量控制
    if (selection.length > 0) {
        const firstDevice = selection[0]
        const deviceId = firstDevice.deviceId || firstDevice.id
        if (deviceId) {
            currentDeviceId.value = deviceId
        }
    } else {
        currentDeviceId.value = null
    }
}

// 播放控制
const togglePlay = async () => {
    // 验证是否选择了设备
    if (selectedDevices.value.length === 0) {
        ElMessage.error('请先选择要广播的设备')
        return
    }

    // 获取选中设备的ID列表
    const deviceIds = selectedDevices.value.map(device => device.deviceId || device.id)

    try {
        if (!isPlaying.value) {
            // 开始广播
            await startBroadcast(deviceIds)
        } else {
            // 停止广播
            await stopBroadcast()
        }
    } catch (error) {
        console.error('广播操作失败:', error)
        ElMessage.error('广播操作失败: ' + error.message)
    }
}

// 计时器
let timer = null
const startTimer = () => {
    timer = setInterval(() => {
        currentTime.value++
    }, 1000)
}

const stopTimer = () => {
    if (timer) {
        clearInterval(timer)
        timer = null
    }
    currentTime.value = 0
}

// 开始广播
const startBroadcast = async (deviceIds) => {
    try {
        // 直接使用activeCard的值作为广播类型（1：麦克风广播；2：文件广播）
        const broadcastType = parseInt(activeCard.value)
        let filePath = ''

        if (activeCard.value === '2') {
            // 文件广播
            // 验证是否选择了文件
            if (!selectedFile.value) {
                throw new Error('请先选择要广播的文件')
            }

            // 如果是上传的文件，使用文件对象；否则使用预设文件路径
            if (uploadedFile.value) {
                // 对于上传的文件，这里需要先上传到服务器获取路径
                // 暂时使用文件名作为路径，实际项目中需要实现文件上传逻辑
                filePath = uploadedFile.value.name
            } else {
                // 预设文件路径映射
                const filePathMap = {
                    'system': '/audio/system.mp3',
                    'alarm': '/audio/alarm.wav'
                }
                filePath = filePathMap[selectedFile.value] || selectedFile.value
            }

            console.log('开始文件广播:', {
                deviceIds,
                broadcastType,
                filePath,
                loopMode: loopMode.value,
                loopCount: loopCount.value,
                volume: broadcastVolume.value
            })
        } else if (activeCard.value === '1') {
            // 麦克风广播
            console.log('开始麦克风广播:', {
                deviceIds,
                broadcastType,
                volume: broadcastVolume.value
            })
        } else {
            throw new Error('请先选择广播类型')
        }

        // 调用广播API
        await chromeSDKManager.quickStartBroadcast(deviceIds, broadcastType, filePath)

        // 更新状态
        isPlaying.value = true
        startTimer()

        // 广播开始后，获取广播音量（仅在有选中设备时）
        if (currentDeviceId.value) {
            setTimeout(() => {
                loadBroadcastVolume(currentDeviceId.value)
            }, 1000) // 延迟1秒获取，确保广播已经开始
        }

        ElMessage.success('广播开始成功')
        console.log('广播开始成功')

    } catch (error) {
        console.error('开始广播失败:', error)
        throw error
    }
}

// 停止广播
const stopBroadcast = async () => {
    try {
        // 调用停止广播API
        await chromeSDKManager.quickStopBroadcast()

        // 更新状态
        isPlaying.value = false
        stopTimer()

        ElMessage.success('广播停止成功')
        console.log('广播停止成功')

    } catch (error) {
        console.error('停止广播失败:', error)
        throw error
    }
}

// 文件上传处理
const handleUploadFile = () => {
    if (fileInput.value) {
        fileInput.value.click()
    }
}

const handleFileChange = (event) => {
    const file = event.target.files[0]
    if (file) {
        // 验证文件类型
        const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/x-m4a']
        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|m4a)$/i)) {
            ElMessage.error('请选择有效的音频文件 (MP3, WAV, M4A)')
            return
        }

        // 验证文件大小 (限制为50MB)
        const maxSize = 50 * 1024 * 1024
        if (file.size > maxSize) {
            ElMessage.error('文件大小不能超过50MB')
            return
        }

        uploadedFile.value = file
        selectedFile.value = file.name
        ElMessage.success(`文件 "${file.name}" 选择成功`)
        console.log('选择的文件:', file)
    }
}

// ==================== 音量管理方法 ====================

// 删除了loadDeviceVolume方法，因为在麦克风广播模式下不需要获取设备输入音量

/**
 * 加载广播音量信息
 * @param {number} deviceId - 设备ID
 */
const loadBroadcastVolume = async (deviceId) => {
    if (!deviceId) return

    try {
        isLoadingBroadcastVolume.value = true

        // 获取广播音量
        const broadcastVolumeResult = await chromeSDKManager.getBroadcastManager().getBroadcastVolume(deviceId)

        if (broadcastVolumeResult && broadcastVolumeResult.volume && broadcastVolumeResult.volume.length > 0) {
            broadcastVolume.value = broadcastVolumeResult.volume[0]
            console.log('获取广播音量成功:', broadcastVolumeResult.volume[0])
        }

    } catch (error) {
        console.error('获取广播音量失败:', error)
        ElMessage.warning('获取广播音量失败: ' + error.message)
    } finally {
        isLoadingBroadcastVolume.value = false
    }
}

// 删除了setDeviceVolumeDebounced方法，统一使用广播音量控制

/**
 * 设置广播音量（带防抖）
 * @param {number} deviceId - 设备ID
 * @param {number} volume - 音量值
 */
const setBroadcastVolumeDebounced = (deviceId, volume) => {
    // 清除之前的定时器
    if (volumeDebounceTimers.value.broadcast) {
        clearTimeout(volumeDebounceTimers.value.broadcast)
    }

    // 设置新的防抖定时器
    volumeDebounceTimers.value.broadcast = setTimeout(async () => {
        // 再次检查广播状态（防止在防抖期间广播被停止）
        if (!isPlaying.value) {
            console.log('广播已停止，取消音量设置')
            return
        }

        try {
            console.log('正在设置广播音量:', { deviceId, volume })
            await chromeSDKManager.getBroadcastManager().setBroadcastVolume(deviceId, [volume])
            console.log('设置广播音量成功:', volume)
            // 移除成功提示，避免频繁弹窗干扰用户
        } catch (error) {
            console.error('设置广播音量失败:', error)

            // 只在真正失败时才显示错误提示
            if (error.message.includes('设置广播音量失败')) {
                ElMessage.error('设置广播音量失败，请确保广播正在进行中')
            } else {
                ElMessage.error('设置广播音量失败: ' + error.message)
            }
        }
    }, 300) // 减少防抖延迟，提高响应速度
}

// 删除了handleMicVolumeChange方法，统一使用广播音量控制


/**
 * 广播音量输入处理（实时响应滑块拖动）
 * @param {number} value - 新的音量值
 */
const handleBroadcastVolumeInput = (value) => {
    // 检查是否在广播状态
    if (!isPlaying.value) {
        return
    }

    if (currentDeviceId.value) {
        setBroadcastVolumeDebounced(currentDeviceId.value, value)
    }
}



// 分页处理
const handleSizeChange = (val) => {
    pageSize.value = val
    console.log('每页条数:', val)
}

const handleCurrentChange = (val) => {
    currentPage.value = val
    console.log('当前页:', val)
}

// 页面初始化
const intPage = async () => {
  try {
    console.log('页面初始化开始...')

    // 这里可以添加页面初始化逻辑

    console.log('页面初始化完成')
    initSDK()
  } catch (error) {
    console.log(error, '初始化失败')
  }
}


const initSDK = async ()=>{
  try {
    // 假设DHAlarmWeb已经加载
    if (typeof DHAlarmWeb !== 'undefined') {
      const dhWeb = new DHAlarmWeb();
      chromeSDKManager.init(dhWeb);
      console.log('Chrome SDK初始化成功');
      login()
    } else {
      throw new Error('DHAlarmWeb未加载');
    }
  } catch (error) {
    console.error('SDK初始化失败:', error);
    throw error;
  }
}

// 登录
const login = async () => {
  try {
    const result = await chromeSDKManager.quickLogin('mainalc', '123456', '192.168.3.18');
    // const result = await chromeSDKManager.quickLogin('mainalc', '123456789', '192.168.1.3');
    if (result) {
      console.log('登录成功',result);
      ondeviceList()
    } else {
      console.log('登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
  }
}

const ondeviceList = () => {
  chromeSDKManager.on('onDeviceList', (result) => {
    console.log('设备列表回调:', result);

    // 检查返回的数据结构
    if (!result || !result.params) {
      console.warn('设备列表回调数据格式异常:', result);
      return;
    }

    // 处理单个设备或设备列表
    let newDevices = [];
    if (result.params.list && Array.isArray(result.params.list)) {
      // 如果是设备列表数组
      newDevices = result.params.list;
    } else if (result.params.device || result.params.deviceId) {
      // 如果是单个设备信息
      newDevices = [result.params];
    } else {
      console.warn('无法识别的设备数据格式:', result.params);
      return;
    }

    // 累积存储设备信息并去重
    newDevices.forEach(device => {
      if (!device) return;

      // 确定设备的唯一标识符（优先级：deviceId > id > mac > ip）
      const deviceKey = device.deviceId || device.id || device.mac || device.ip;
      if (!deviceKey) {
        console.warn('设备缺少唯一标识符:', device);
        return;
      }

      // 检查设备是否已存在（基于唯一标识符去重）
      const existingIndex = deviceList.value.findIndex(existingDevice => {
        const existingKey = existingDevice.deviceId || existingDevice.id || existingDevice.mac || existingDevice.ip;
        return existingKey === deviceKey;
      });

      if (existingIndex !== -1) {
        // 设备已存在，更新设备信息（保持最新状态）
        deviceList.value[existingIndex] = { ...deviceList.value[existingIndex], ...device };
        console.log('更新已存在设备:', deviceKey, device);
      } else {
        // 新设备，添加到列表
        deviceList.value.push(device);
        console.log('添加新设备:', deviceKey, device);
      }
    });

    console.log('当前设备总数:', deviceList.value.length);
  });
}

// 生命周期
onMounted(() => {
    console.log('实时广播页面已加载')
    intPage()
})

onUnmounted(() => {
    if (timer) {
        clearInterval(timer)
    }

    // 清理音量防抖定时器
    Object.values(volumeDebounceTimers.value).forEach(timer => {
        if (timer) {
            clearTimeout(timer)
        }
    })
})
</script>

<style scoped lang="scss">

.real-time-broadcast {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 56px);
  display: flex;
  flex-direction: column;
}

// 主要内容区域
.main-content {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

// 广播卡片样式
.broadcast-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.active {
    border-color: #2283FF;
    box-shadow: 0 4px 16px rgba(34, 131, 255, 0.2);

    .card-header {
      .card-title {
        color: #2283FF;
      }
    }
  }

  &.disabled {
    opacity: 0.6;

    .card-content {
      pointer-events: none;
    }
  }

  // 广播进行中的禁用状态
  &.broadcast-disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #f5f7fa;
    border-color: #e4e7ed;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: none;
    }

    .card-header {
      .card-title {
        color: #909399;
      }

      .card-indicator {
        background: #e4e7ed;
      }
    }

    .card-content {
      pointer-events: none;
      color: #c0c4cc;
    }

    // 添加禁用遮罩
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 8px;
      pointer-events: none;
      z-index: 1;
    }

    // 添加禁用提示
    &::after {
      content: '广播进行中，无法切换';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 2;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;

  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    transition: color 0.3s ease;
  }

  .card-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e4e7ed;
    transition: all 0.3s ease;

    &.active {
      background: #2283FF;
      box-shadow: 0 0 8px rgba(34, 131, 255, 0.4);
    }
  }
}

.card-content {
  transition: opacity 0.3s ease;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .setting-label {
    min-width: 80px;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
  }
}

.file-select {
  display: flex;
  align-items: center;
  gap: 12px;

  .upload-btn {
    background: #2283FF;
    border-color: #2283FF;
    border-radius: 4px;

    &:hover {
      background: #5a95f5;
      border-color: #5a95f5;
    }
  }
}

.loop-controls {
  display: flex;
  align-items: center;
  gap: 12px;

  .el-radio-group {
    display: flex;
    gap: 16px;
  }

  .el-input-number {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }
}



.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;

  .volume-value {
    min-width: 30px;
    color: #2283FF;
    font-weight: 600;
    transition: all 0.3s ease;

    &.loading {
      color: #909399;
      animation: pulse 1.5s ease-in-out infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 右侧播放控制
.right-panel {
  width: 300px;
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.time-display {
  font-size: 36px;
  font-weight: 600;
  color: #2283FF;
  font-family: 'Courier New', monospace;
}

.play-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.play-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);

  &:hover:not(.disabled) {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(135deg, #ccc, #ddd);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .play-icon {
    font-size: 32px;
    color: white;
    transition: all 0.3s ease;

    &.playing {
      transform: scale(0.9);
    }
  }
}

.play-text {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.device-count {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

// 设备列表区域
.device-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .device-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .device-actions {
    display: flex;
    gap: 12px;

    .el-button {
      border-radius: 4px;
    }
  }
}

.device-table {
    flex: 1;
    display: flex;
    flex-direction: column;
  .status-tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;

    &.online {
      background: #f0f9ff;
      color: #67c23a;
    }

    &.offline {
      background: #fef0f0;
      color: #f56c6c;
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;

  :deep(.el-pagination) {
    .el-pager li {
      border-radius: 4px;
      margin: 0 2px;
    }

    .btn-prev,
    .btn-next {
      border-radius: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .right-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .real-time-broadcast {
    padding: 10px;
  }

  .device-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .time-display {
    font-size: 28px;
  }

  .play-button {
    width: 60px;
    height: 60px;

    .play-icon {
      font-size: 24px;
    }
  }
}
</style>